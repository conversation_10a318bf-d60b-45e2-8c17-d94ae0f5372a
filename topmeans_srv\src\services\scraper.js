const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const randomUseragent = require('random-useragent');
const logger = require("../log/logger");

// 启用反检测插件
puppeteer.use(StealthPlugin());

/**
 * 爬取图片URL的函数
 * @param {string} targetUrl - 要爬取的目标URL
 * @param {string} selector - 图片元素的CSS选择器
 * @param {Object} options - 可选配置参数
 * @param {boolean|string} options.headless - 是否使用无头模式，默认为'new'
 * @param {number} options.timeout - 页面加载超时时间，默认为60000ms
 * @param {number} options.selectorTimeout - 选择器等待超时时间，默认为15000ms
 * @param {boolean} options.interceptRequests - 是否拦截请求，默认为true
 * @returns {Promise<string>} - 返回爬取到的图片URL
 */
async function scrape(targetUrl, selector, attr = 'src', checkField = null, options = {}) {
    const defaultOptions = {
        headless: 'new',
        timeout: 20000,
        selectorTimeout: 15000,
        interceptRequests: true
    };

    const config = {...defaultOptions, ...options};

    // 1. 浏览器初始化（带反爬参数）
    const browser = await puppeteer.launch({
        headless: config.headless,  // 使用新的Headless模式
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-web-security',
            '--disable-features=IsolateOrigins,site-per-process',
            `--user-agent=${randomUseragent.getRandom()}`
        ]
    });

    try {
        // 2. 创建带反检测的页面
        const page = await browser.newPage();
        await page.setJavaScriptEnabled(true);
        await page.setViewport({width: 800, height: 600, deviceScaleFactor: 1, isMobile: false});

        // 3. 随机化人类行为模式（可选择是否拦截请求）
        if (config.interceptRequests) {
            await page.setRequestInterception(true);
            page.on('request', (req) => {
                if (['image', 'stylesheet', 'font'].includes(req.resourceType())) {
                    req.abort();
                } else {
                    req.continue();
                }
            });
        }

        // 4. 访问目标页面（带随机延迟）
        await page.goto(targetUrl, {
            waitUntil: ['domcontentloaded', 'networkidle2'],
            timeout: config.timeout,
            delay: Math.floor(Math.random() * 1000) + 500  // 0.5 - 1.5秒随机延迟
        });

        // 5. 模拟人类滚动行为
        await autoScroll(page);

        // 获取页面内容并检查是否有权限错误
        const pageContent = await page.content();
        if (pageContent.includes("You don't have permission to access this resource")) {
            logger.error("权限错误: 无法访问资源，可能是由于内容限制、权限不足或请求配置错误");
            throw new Error("权限错误: 无法访问资源，可能是由于内容限制、权限不足或请求配置错误");
        }

        // 进行一个随机等待
        await new Promise(resolve => setTimeout(resolve, Math.floor(Math.random() * 1000) + 500));

        // 6. 等待元素并获取属性
        await page.waitForSelector(selector, {timeout: config.selectorTimeout});
        if (attr === 'src') {
            return await page.$eval(selector, element => element.src);
        } else if (attr === 'mdurl') {
            return await page.$eval(selector, element => element.mdurl);
        }

        throw new Error(`爬取信息失败，未识别的属性: ${attr}`);
    } catch (error) {
        console.error('爬取失败:', error.message);
        throw error;
    } finally {
        await browser.close();
    }
}

// 人类滚动行为模拟
async function autoScroll(page) {
    await page.evaluate(async () => {
        await new Promise((resolve) => {
            let totalHeight = 0;
            const distance = 100;
            const timer = setInterval(() => {
                const scrollHeight = document.body.scrollHeight;
                window.scrollBy(0, distance);
                totalHeight += distance;
                if (totalHeight >= scrollHeight) {
                    clearInterval(timer);
                    resolve();
                }
            }, 100);
        });
    });
}

exports.scrapeImageUrl = async (req, res) => {
    const {name} = req.body;

    if (!name) {
        logger.error(`缺少必要的参数:${req.body}`);
        return res.status(400).json({error: '缺少必要的参数'});
    }

    const encodedName = encodeURIComponent(name);
    const targetUrl = `https://www.freepik.com/search?ai=only&format=search&last_filter=ai&last_value=only&query=${encodedName}&selection=1`;
    const imageSelector = `#__next > div.\\$bg-surface-0.\\$text-surface-foreground-0.lg\\:flex > div > div:nth-child(4) > div > div > div.\\$max-w-2xl.\\$mx-auto > div > div.\\$grid.\\$items-start.\\$grid-cols-1.\\$gap-\\[24px\\].sm\\:\\$grid-cols-2.md\\:\\$grid-cols-3.\\32 xl\\:\\$grid-cols-4 > figure:nth-child(1) > a > img`;

    // 最大重试次数
    const maxRetries = 3;
    let retryCount = 0;
    let lastError = null;

    while (retryCount < maxRetries) {
        try {
            // 每次重试使用不同的配置
            const options = {
                headless: 'new',
                timeout: 20000 + (retryCount * 5000), // 增加超时时间
                interceptRequests: retryCount < 2 // 最后一次尝试不拦截请求
            };

            logger.info(`尝试爬取图片 (尝试 ${retryCount + 1}/${maxRetries}): ${name}`);
            const imageUrl = await scrape(targetUrl, imageSelector, 'src', null, options);
            return res.json({success: true, url: imageUrl});
        } catch (error) {
            lastError = error;
            logger.warn(`权限错误，准备重试 (${retryCount + 1}/${maxRetries}): ${error.message}`);
            retryCount++;
            // 在重试前等待一段时间
            await new Promise(resolve => setTimeout(resolve, 2000 + (retryCount * 1000)));
        }
    }

    // 所有重试都失败
    logger.error(`爬取图片失败 (已重试 ${retryCount} 次):${req.body}, ${lastError.message}`);
    res.status(500).json({error: '爬取图片失败', details: lastError.message});
}

exports.scrapeHotelUrl = async (req, res) => {
    const {name, user, create_time, day} = req.body;

    if (!name) {
        logger.error(`缺少必要的参数:${req.body}`);
        return res.status(400).json({error: '缺少必要的参数'});
    }

    const encodedQuery = encodeURIComponent(`${name} site:ctrip.com`);
    const targetUrl = `https://sogou.com/web?query=${encodedQuery}`;
    const selector = '#main > div:nth-child(4) > div > div:nth-child(3) > div.struct201102 > h3 > a';

    // 最大重试次数
    const maxRetries = 3;
    let retryCount = 0;
    let lastError = null;

    while (retryCount < maxRetries) {
        try {
            // 每次重试使用不同的配置
            const options = {
                headless: 'new',
                timeout: 20000 + (retryCount * 5000), // 增加超时时间
                interceptRequests: retryCount < 2 // 最后一次尝试不拦截请求
            };

            logger.info(`尝试爬取酒店链接 (尝试 ${retryCount + 1}/${maxRetries}): ${name}`);
            const hotelUrl = await scrape(targetUrl, selector, 'src', null, options);
            res.json({success: true, url: hotelUrl});
            return;
        } catch (error) {
            lastError = error;
            logger.warn(`权限错误，准备重试 (${retryCount + 1}/${maxRetries}): ${error.message}`);
            retryCount++;
            // 在重试前等待一段时间
            await new Promise(resolve => setTimeout(resolve, 2000 + (retryCount * 1000)));
        }
    }

    // 所有重试都失败
    logger.error(`爬取酒店链接失败 (已重试 ${retryCount} 次):${req.body}, ${lastError.message}`);
    res.status(500).json({error: '爬取酒店链接失败', details: lastError.message});
}
