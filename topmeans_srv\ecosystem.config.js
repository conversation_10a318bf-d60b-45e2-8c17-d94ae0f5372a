module.exports = {
    apps: [{
      name: 'topmeans_srv',
      script: './start_server.sh', // 或你的主入口文件
      instances: 'max', // 或 'max' 使用所有CPU核心
      exec_mode: 'fork', // 或 'fork'
      env: {
        NODE_ENV: 'development',
        PORT: 3999
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3999
      },
      // 自动重启配置
      watch: false, // 生产环境建议关闭
      max_memory_restart: '1G',
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      // 重启策略
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s'
    }]
  }