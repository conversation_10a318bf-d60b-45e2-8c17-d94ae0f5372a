const express = require('express');
const jwt = require('jsonwebtoken');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const logger = require('../log/logger');
const {
    createJournal,
    getUserJournals,
    getPublicJournals,
    getJournalsByDestination,
    getJournalById,
    updateJournal,
    deleteJournal,
    toggleJournalPublic,
    toggleJournalLike,
    addJournalComment,
    getJournalComments
} = require('../services/journalService');

const router = express.Router();

// JWT认证中间件
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ success: false, message: '未提供身份凭证' });
    }

    jwt.verify(token, process.env.JWT_SECRET || 'your_secret_key', (err, user) => {
        if (err) {
            return res.status(403).json({ success: false, message: '身份验证失败' });
        }
        req.user = user;
        next();
    });
};

// 可选认证中间件（用于可以匿名访问但需要区分用户身份的接口）
const optionalAuth = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
        jwt.verify(token, process.env.JWT_SECRET || 'your_secret_key', (err, user) => {
            if (!err) {
                req.user = user;
            }
        });
    }
    next();
};

// 配置图片上传
const imageStorage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path.join(__dirname, '../../public/uploads/journals');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const userId = req.user.userId;
        const timestamp = Date.now();
        const ext = path.extname(file.originalname);
        cb(null, `journal_${userId}_${timestamp}${ext}`);
    }
});

const imageFilter = (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('不支持的图片格式'), false);
    }
};

const uploadImage = multer({
    storage: imageStorage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
    fileFilter: imageFilter
});

// 游记相关路由

// 创建游记
router.post('/', authenticateToken, async (req, res) => {
    try {
        const result = await createJournal({
            userId: req.user.userId,
            ...req.body
        });
        res.json(result);
    } catch (error) {
        logger.error('创建游记API错误:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
});

// 获取当前用户的游记列表
router.get('/my', authenticateToken, async (req, res) => {
    try {
        const { page = 1, pageSize = 10 } = req.query;
        const result = await getUserJournals(
            req.user.userId,
            parseInt(page),
            parseInt(pageSize)
        );
        res.json(result);
    } catch (error) {
        logger.error('获取用户游记API错误:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
});

// 获取公开游记列表
router.get('/public', optionalAuth, async (req, res) => {
    try {
        const { page = 1, pageSize = 10 } = req.query;
        const result = await getPublicJournals(
            parseInt(page),
            parseInt(pageSize)
        );
        res.json(result);
    } catch (error) {
        logger.error('获取公开游记API错误:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
});

// 根据目的地获取推荐游记
router.get('/recommendations/:destination', authenticateToken, async (req, res) => {
    try {
        const { destination } = req.params;
        const { page = 1, pageSize = 5 } = req.query;
        const result = await getJournalsByDestination(
            destination,
            req.user.userId,
            parseInt(page),
            parseInt(pageSize)
        );
        res.json(result);
    } catch (error) {
        logger.error('获取推荐游记API错误:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
});

// 获取游记详情
router.get('/:id', optionalAuth, async (req, res) => {
    try {
        const { id } = req.params;
        const currentUserId = req.user ? req.user.userId : null;
        const result = await getJournalById(id, currentUserId);
        res.json(result);
    } catch (error) {
        logger.error('获取游记详情API错误:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
});

// 更新游记
router.put('/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const result = await updateJournal(id, req.user.userId, req.body);
        res.json(result);
    } catch (error) {
        logger.error('更新游记API错误:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
});

// 删除游记
router.delete('/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const result = await deleteJournal(id, req.user.userId);
        res.json(result);
    } catch (error) {
        logger.error('删除游记API错误:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
});

// 切换游记公开状态
router.patch('/:id/toggle-public', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const result = await toggleJournalPublic(id, req.user.userId);
        res.json(result);
    } catch (error) {
        logger.error('切换游记公开状态API错误:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
});

// 点赞/取消点赞游记
router.post('/:id/like', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const result = await toggleJournalLike(id, req.user.userId);
        res.json(result);
    } catch (error) {
        logger.error('切换游记点赞状态API错误:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
});

// 添加游记评论
router.post('/:id/comments', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const { content, parentId } = req.body;

        if (!content || content.trim() === '') {
            return res.status(400).json({ success: false, message: '评论内容不能为空' });
        }

        const result = await addJournalComment(id, req.user.userId, content, parentId);
        res.json(result);
    } catch (error) {
        logger.error('添加游记评论API错误:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
});

// 获取游记评论
router.get('/:id/comments', optionalAuth, async (req, res) => {
    try {
        const { id } = req.params;
        const { page = 1, pageSize = 10 } = req.query;
        const result = await getJournalComments(
            id,
            parseInt(page),
            parseInt(pageSize)
        );
        res.json(result);
    } catch (error) {
        logger.error('获取游记评论API错误:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
});

// 上传游记图片
router.post('/upload-image', authenticateToken, uploadImage.single('image'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ success: false, message: '没有上传文件' });
        }

        const imageUrl = `/uploads/journals/${req.file.filename}`;

        logger.info('游记图片上传成功:', {
            userId: req.user.userId,
            fileName: req.file.filename,
            imageUrl
        });

        res.json({
            success: true,
            imageUrl,
            message: '图片上传成功'
        });
    } catch (error) {
        logger.error('上传游记图片API错误:', error);
        res.status(500).json({ success: false, message: '上传失败' });
    }
});

// 错误处理中间件
router.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({ success: false, message: '文件大小超过限制' });
        }
    }
    if (error.message === '不支持的图片格式') {
        return res.status(400).json({ success: false, message: '不支持的图片格式' });
    }

    logger.error('游记API错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
});

module.exports = router;