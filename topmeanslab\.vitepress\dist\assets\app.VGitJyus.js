import{R as p}from"./chunks/theme.DE6uTiF9.js";import{R as s,aS as i,aT as u,aU as c,aV as l,aW as f,aX as d,aY as m,aZ as h,a_ as g,a$ as A,d as b,u as v,v as R,s as w,b0 as y,b1 as C,b2 as P,aF as S}from"./chunks/framework.oPHriSgN.js";function r(e){if(e.extends){const a=r(e.extends);return{...a,...e,async enhanceApp(t){a.enhanceApp&&await a.enhanceApp(t),e.enhanceApp&&await e.enhanceApp(t)}}}return e}const n=r(p),T=b({name:"VitePressApp",setup(){const{site:e,lang:a,dir:t}=v();return R(()=>{w(()=>{document.documentElement.lang=a.value,document.documentElement.dir=t.value})}),e.value.router.prefetchLinks&&y(),C(),P(),n.setup&&n.setup(),()=>S(n.Layout)}});async function E(){globalThis.__VITEPRESS__=!0;const e=D(),a=_();a.provide(u,e);const t=c(e.route);return a.provide(l,t),a.component("Content",f),a.component("ClientOnly",d),Object.defineProperties(a.config.globalProperties,{$frontmatter:{get(){return t.frontmatter.value}},$params:{get(){return t.page.value.params}}}),n.enhanceApp&&await n.enhanceApp({app:a,router:e,siteData:m}),{app:a,router:e,data:t}}function _(){return A(T)}function D(){let e=s;return h(a=>{let t=g(a),o=null;return t&&(e&&(t=t.replace(/\.js$/,".lean.js")),o=import(t)),s&&(e=!1),o},n.NotFound)}s&&E().then(({app:e,router:a,data:t})=>{a.go().then(()=>{i(a.route,t.site),e.mount("#app")})});export{E as createApp};
