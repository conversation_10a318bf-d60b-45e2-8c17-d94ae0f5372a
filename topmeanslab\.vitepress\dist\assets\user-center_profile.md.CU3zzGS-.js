import{U as e}from"./chunks/UserProfile.BkjgU2Ee.js";import{c as t,o as r,G as a}from"./chunks/framework.oPHriSgN.js";import"./chunks/theme.DE6uTiF9.js";import"./chunks/TopmeansMarkdownService.C-2qduF6.js";const _=JSON.parse('{"title":"","description":"","frontmatter":{"layout":"page"},"headers":[],"relativePath":"user-center/profile.md","filePath":"user-center/profile.md"}'),o={name:"user-center/profile.md"},f=Object.assign(o,{setup(s){return(c,i)=>(r(),t("div",null,[a(e)]))}});export{_ as __pageData,f as default};
