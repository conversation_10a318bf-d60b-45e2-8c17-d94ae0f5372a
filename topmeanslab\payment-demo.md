# 支付模块演示

这是简洁版的支付模块演示页面。

<script setup>
import PaymentMethods from './.vitepress/theme/components/Payment/PaymentMethods.vue'
import { ref } from 'vue'

const showPayment = ref(true)
const amount = ref(99.99)

const handlePaymentSuccess = (data) => {
  console.log('支付成功:', data)
  showPayment.value = false
}

const handlePaymentCancel = () => {
  console.log('支付取消')
  showPayment.value = false
}

const resetDemo = () => {
  showPayment.value = true
}
</script>

<div v-if="showPayment">
  <PaymentMethods 
    :amount="amount"
    subject="TopMeans服务购买"
    :goods="{ type: 'demo', name: '演示商品' }"
    :user-id="1"
    user-account="<EMAIL>"
    @payment-success="handlePaymentSuccess"
    @payment-cancel="handlePaymentCancel"
  />
</div>

<div v-else style="text-align: center; padding: 2rem;">
  <h3>支付流程已结束</h3>
  <button @click="resetDemo" style="padding: 0.5rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 8px; cursor: pointer;">
    重新演示
  </button>
</div>

## 功能特点

### 🎨 简洁设计
- 白色背景，简洁清爽
- 卡片式布局，信息清晰
- 弹窗式支付方式选择

### 💳 支付流程
- 点击"立即购买"打开支付方式选择
- 支持支付宝支付（已实现）
- 微信支付（即将开放）
- 二维码支付流程

### 🔒 安全保障
- 保持原有接口调用逻辑
- 安全的支付状态轮询
- 完整的错误处理机制

### 📱 响应式设计
- 适配各种屏幕尺寸
- 移动端友好的交互体验
