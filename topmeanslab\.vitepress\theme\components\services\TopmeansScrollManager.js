/**
 * 滚动管理器 - 负责页面滚动相关的所有操作
 */
export class TopmeansScrollManager {
    constructor() {
        // 滚动控制相关状态
        this.autoScrollEnabled = true;
        this.userScrollTimeout = null;
        this.lastScrollTop = 0;
        this.isUserScrolling = false;
        this.scrollObserver = null;
        this.handleUserScroll = null;

        // 新增：用于更精确检测用户滚动的变量
        this.lastUserScrollTime = 0;
        this.scrollVelocity = 0;
        this.consecutiveScrollCount = 0;
    }

    /**
     * 初始化滚动监听器
     */
    initScrollListener() {
        if (typeof window === 'undefined') return;

        // 监听用户滚动
        this.handleUserScroll = this.throttle(() => {
            const currentTime = Date.now();
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollDiff = Math.abs(currentScrollTop - this.lastScrollTop);
            const timeDiff = currentTime - this.lastUserScrollTime;

            // 计算滚动速度
            this.scrollVelocity = timeDiff > 0 ? scrollDiff / timeDiff : 0;

            // 检测是否有实际滚动发生
            if (scrollDiff > 5) { // 降低最小检测阈值
                this.consecutiveScrollCount++;

                // 判断是否为用户主动滚动
                const isSignificantScroll = scrollDiff > 30; // 明显的滚动
                const isUpwardScroll = currentScrollTop < this.lastScrollTop; // 向上滚动
                const isHighVelocity = this.scrollVelocity > 0.5; // 高速滚动
                const isConsecutiveScroll = this.consecutiveScrollCount >= 2; // 连续滚动

                // 用户主动滚动的判断条件（更宽松的检测）
                const isUserInitiatedScroll = isUpwardScroll || isSignificantScroll || isHighVelocity ||
                    (isConsecutiveScroll && scrollDiff > 15);

                if (isUserInitiatedScroll) {
                    // 用户开始主动滚动
                    if (!this.isUserScrolling) {
                        this.isUserScrolling = true;
                        this.autoScrollEnabled = false;
                    }

                    // 清除之前的计时器并重新开始计时
                    if (this.userScrollTimeout) {
                        clearTimeout(this.userScrollTimeout);
                    }

                    // 5秒后恢复自动滚动
                    this.userScrollTimeout = setTimeout(() => {
                        this.isUserScrolling = false;
                        this.consecutiveScrollCount = 0; // 重置连续滚动计数

                        // 检查是否所有内容都已完成，如果是则不恢复自动滚动
                        if (!this.checkAllContentCompleted()) {
                            this.autoScrollEnabled = true;
                            // 恢复时滚动到最新内容
                            setTimeout(() => {
                                this.smartScrollToContent();
                            }, 200);
                        }
                    }, 5000);
                }
                // 如果已经在用户滚动状态，持续重置计时器
                else if (this.isUserScrolling && scrollDiff > 8) {
                    // 清除计时器并重新开始
                    if (this.userScrollTimeout) {
                        clearTimeout(this.userScrollTimeout);
                    }

                    this.userScrollTimeout = setTimeout(() => {
                        this.isUserScrolling = false;
                        this.consecutiveScrollCount = 0;

                        if (!this.checkAllContentCompleted()) {
                            this.autoScrollEnabled = true;
                            setTimeout(() => {
                                this.smartScrollToContent();
                            }, 200);
                        }
                    }, 5000);
                }

                this.lastScrollTop = currentScrollTop;
                this.lastUserScrollTime = currentTime;
            } else {
                // 没有显著滚动时，逐渐减少连续滚动计数
                this.consecutiveScrollCount = Math.max(0, this.consecutiveScrollCount - 1);
            }
        }, 80); // 进一步减少节流时间，提高响应性

        window.addEventListener('scroll', this.handleUserScroll, { passive: true });
    }

    /**
     * 清理滚动监听器
     */
    cleanupScrollListener() {
        if (typeof window === 'undefined') return;

        if (this.handleUserScroll) {
            window.removeEventListener('scroll', this.handleUserScroll);
        }

        if (this.userScrollTimeout) {
            clearTimeout(this.userScrollTimeout);
        }
    }

    /**
     * 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function () {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    /**
     * 检查是否所有内容都已完成
     * 这个方法需要从外部传入检查函数
     */
    setContentCompletedChecker(checkerFunction) {
        this.checkAllContentCompleted = checkerFunction;
    }

    /**
     * 设置显示状态检查函数
     */
    setDisplayStateChecker(checkerFunction) {
        this.getDisplayState = checkerFunction;
    }

    /**
     * 默认的内容完成检查函数
     */
    checkAllContentCompleted() {
        // 默认返回false，实际使用时应该从外部设置检查函数
        return false;
    }

    /**
     * 默认的显示状态获取函数
     */
    getDisplayState() {
        return {
            selectedDayIndex: -1,
            isAnyLoading: false
        };
    }

    /**
     * 智能滚动到内容
     */
    smartScrollToContent() {
        if (!this.autoScrollEnabled || typeof window === 'undefined') return;

        // 检查是否所有内容都已完成，如果是则停止自动滚动
        if (this.checkAllContentCompleted()) {
            this.autoScrollEnabled = false;
            return;
        }

        try {
            // 找到最后一个可见的 answer-area-container
            const containers = document.querySelectorAll('.answer-area-container');
            if (containers.length === 0) return;

            let targetContainer = null;
            let hasActiveLoading = false;

            // 获取当前显示状态
            const displayState = this.getDisplayState();
            hasActiveLoading = displayState.isAnyLoading;

            // 检查全局loading状态指示器是否显示（备用检查）
            const globalLoadingBanner = document.querySelector('.global-loading-banner');
            if (!hasActiveLoading && globalLoadingBanner && window.getComputedStyle(globalLoadingBanner).display !== 'none') {
                hasActiveLoading = true;
            }

            // 如果有全局loading，找到最后一个可见的容器
            if (hasActiveLoading) {
                for (let i = containers.length - 1; i >= 0; i--) {
                    const container = containers[i];
                    // 检查容器是否可见（未被v-show隐藏）
                    if (container.offsetParent !== null && window.getComputedStyle(container).display !== 'none') {
                        targetContainer = container;
                        break;
                    }
                }
            }

            // 如果没有找到正在处理的容器，检查是否真的全部完成了
            if (!hasActiveLoading) {
                // 再次检查是否所有内容都已完成
                if (this.checkAllContentCompleted()) {
                    this.autoScrollEnabled = false;
                    return;
                }
                // 如果还有未完成的内容，滚动到最后一个可见容器
                for (let i = containers.length - 1; i >= 0; i--) {
                    const container = containers[i];
                    if (container.offsetParent !== null && window.getComputedStyle(container).display !== 'none') {
                        targetContainer = container;
                        break;
                    }
                }
            }

            if (targetContainer) {
                // 滚动到容器底部附近，确保用户能看到正在生成的内容
                const containerRect = targetContainer.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                // 计算目标滚动位置：容器底部 - 视窗高度的70%，确保容器在视窗中央偏下
                const viewportHeight = window.innerHeight;
                // 如果有全局loading banner，需要考虑其高度
                const globalBannerHeight = globalLoadingBanner && hasActiveLoading ? globalLoadingBanner.offsetHeight : 0;
                const targetScrollTop = scrollTop + containerRect.bottom - viewportHeight * 0.7 + globalBannerHeight;

                // 平滑滚动到目标位置
                window.scrollTo({
                    top: Math.max(0, targetScrollTop),
                    behavior: 'smooth'
                });
            } else {
                // 备用方案：滚动到页面底部
                window.scrollTo({
                    top: document.body.scrollHeight,
                    behavior: 'smooth'
                });
            }
        } catch (error) {
            // 备用方案：滚动到页面底部
            window.scrollTo({
                top: document.body.scrollHeight,
                behavior: 'smooth'
            });
        }
    }

    /**
     * 保留原方法名以兼容其他调用
     */
    scrollPageToBottom() {
        this.smartScrollToContent();
    }

    /**
     * 重置滚动状态（在开始新一轮操作时调用）
     */
    resetScrollState() {
        this.autoScrollEnabled = true;
        this.isUserScrolling = false;

        // 安全地获取当前滚动位置
        if (typeof window !== 'undefined') {
            this.lastScrollTop = window.pageYOffset || document.documentElement.scrollTop || 0;
        } else {
            this.lastScrollTop = 0;
        }

        // 清除用户滚动定时器
        if (this.userScrollTimeout) {
            clearTimeout(this.userScrollTimeout);
            this.userScrollTimeout = null;
        }

        // 重置新增的状态变量
        this.lastUserScrollTime = Date.now();
        this.scrollVelocity = 0;
        this.consecutiveScrollCount = 0;
    }

    /**
     * 获取当前滚动状态
     */
    getScrollState() {
        return {
            autoScrollEnabled: this.autoScrollEnabled,
            isUserScrolling: this.isUserScrolling,
            lastScrollTop: this.lastScrollTop
        };
    }

    /**
     * 设置自动滚动状态
     */
    setAutoScrollEnabled(enabled) {
        this.autoScrollEnabled = enabled;
    }

    /**
     * 检查是否正在用户滚动
     */
    isUserScrollingActive() {
        return this.isUserScrolling;
    }

    /**
     * 强制滚动到页面底部
     */
    forceScrollToBottom() {
        if (typeof window === 'undefined') return;

        window.scrollTo({
            top: document.body.scrollHeight,
            behavior: 'smooth'
        });
    }

    /**
     * 滚动到指定元素
     */
    scrollToElement(element, behavior = 'smooth') {
        if (!element || typeof window === 'undefined') return;

        element.scrollIntoView({
            behavior: behavior,
            block: 'center'
        });
    }

    /**
     * 清理所有资源
     */
    cleanup() {
        this.cleanupScrollListener();

        // 重置所有状态
        this.autoScrollEnabled = true;
        this.isUserScrolling = false;
        this.lastScrollTop = 0;
        this.userScrollTimeout = null;
        this.scrollObserver = null;
        this.handleUserScroll = null;

        // 重置新增的状态变量
        this.lastUserScrollTime = 0;
        this.scrollVelocity = 0;
        this.consecutiveScrollCount = 0;
    }
}

// 创建单例实例
export const scrollManager = new TopmeansScrollManager();
