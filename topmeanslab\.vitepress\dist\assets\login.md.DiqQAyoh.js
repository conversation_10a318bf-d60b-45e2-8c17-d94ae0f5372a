import e from"./chunks/LoginPage.Ckuz8tFG.js";import{c as t,o as a,G as o}from"./chunks/framework.oPHriSgN.js";import"./chunks/Valicode.B4wGwRkJ.js";import"./chunks/theme.DE6uTiF9.js";const _=JSON.parse('{"title":"登录 - TopMeansLab","description":"","frontmatter":{"layout":"page","title":"登录 - TopMeansLab"},"headers":[],"relativePath":"login.md","filePath":"login.md"}'),r={name:"login.md"},d=Object.assign(r,{setup(n){return(i,s)=>(a(),t("div",null,[o(e)]))}});export{_ as __pageData,d as default};
