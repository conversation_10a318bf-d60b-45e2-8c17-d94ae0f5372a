import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { TopmeansApiService } from '@services/TopmeansApiService'

describe('TopmeansApiService', () => {
  let apiService
  let mockFetch

  beforeEach(() => {
    apiService = new TopmeansApiService()
    mockFetch = vi.fn()
    global.fetch = mockFetch
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with null abortController', () => {
      expect(apiService.abortController).toBeNull()
    })
  })

  describe('getHotelUrl', () => {
    it('should successfully get hotel URL', async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({ success: true, url: 'https://example.com/hotel' })
      }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await apiService.getHotelUrl('测试酒店', 'testuser', '2024-01-01', 1, '北京')

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/hotel', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: '北京测试酒店',
          user: 'testuser',
          create_time: '2024-01-01',
          day: '1'
        })
      })
      expect(result).toBe('https://example.com/hotel')
    })

    it('should throw error when response is not ok', async () => {
      const mockResponse = { ok: false }
      mockFetch.mockResolvedValue(mockResponse)

      await expect(apiService.getHotelUrl('测试酒店', 'testuser', '2024-01-01', 1, '北京'))
        .rejects.toThrow('酒店信息获取失败')
    })

    it('should throw error when fetch fails', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      await expect(apiService.getHotelUrl('测试酒店', 'testuser', '2024-01-01', 1, '北京'))
        .rejects.toThrow('Network error')
    })
  })

  describe('getFoodImgUrl', () => {
    it('should successfully get food image URL', async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({ success: true, url: 'https://example.com/food.jpg' })
      }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await apiService.getFoodImgUrl('宫保鸡丁', '川菜经典')

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/ai_img', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: '一种食物或一个著名饭店，请根据后续描述来进行写实风格的图片生成，名字：宫保鸡丁,相关信息：川菜经典'
        })
      })
      expect(result).toBe('https://example.com/food.jpg')
    })

    it('should throw error when response is not ok', async () => {
      const mockResponse = { ok: false }
      mockFetch.mockResolvedValue(mockResponse)

      await expect(apiService.getFoodImgUrl('宫保鸡丁', '川菜经典'))
        .rejects.toThrow('美食图片获取失败')
    })
  })

  describe('getAIImg', () => {
    it('should successfully get AI image URL', async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({ success: true, url: 'https://example.com/ai-image.jpg' })
      }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await apiService.getAIImg('food', '北京', '烤鸭', '北京特色美食')

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/ai_img2', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'food',
          location: '北京',
          name: '烤鸭',
          prompt: '烤鸭：北京特色美食'
        })
      })
      expect(result).toBe('https://example.com/ai-image.jpg')
    })

    it('should throw error when response is not ok', async () => {
      const mockResponse = { ok: false }
      mockFetch.mockResolvedValue(mockResponse)

      await expect(apiService.getAIImg('food', '北京', '烤鸭', '北京特色美食'))
        .rejects.toThrow('美食图片获取失败')
    })
  })

  describe('getViewUrl', () => {
    it('should successfully get view URL', async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({ success: true, url: 'https://example.com/view.jpg' })
      }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await apiService.getViewUrl('故宫')

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/view', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: '故宫' })
      })
      expect(result).toBe('https://example.com/view.jpg')
    })
  })

  describe('savePlanToDB', () => {
    it('should successfully save plan to database', async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({ success: true, message: '保存成功' })
      }
      mockFetch.mockResolvedValue(mockResponse)

      const planData = { content: '行程内容', account: 'testuser', filename: 'test.md' }
      const result = await apiService.savePlanToDB(planData)

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/save_plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: '行程内容',
          user: 'testuser',
          filename: 'test.md'
        })
      })
      expect(result).toEqual({ success: true, message: '保存成功' })
    })
  })

  describe('addPlanToUser', () => {
    it('should successfully add plan to user', async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({ success: true, message: '添加成功' })
      }
      mockFetch.mockResolvedValue(mockResponse)

      const userData = { account: 'testuser', create_time: '2024-01-01', days: 3 }
      const result = await apiService.addPlanToUser(userData)

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/user/add_plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          account: 'testuser',
          create_time: '2024-01-01',
          days: 3
        })
      })
      expect(result).toEqual({ success: true, message: '添加成功' })
    })
  })

  describe('sendMessage', () => {
    it('should successfully send message with streaming', async () => {
      const mockResponse = {
        ok: true,
        body: {
          getReader: vi.fn().mockReturnValue({
            read: vi.fn().mockResolvedValue({
              done: false,
              value: new TextEncoder().encode('data: {"choices":[{"delta":{"content":"test"}}]}\n')
            }).mockResolvedValueOnce({
              done: true,
              value: undefined
            })
          })
        }
      }
      mockFetch.mockResolvedValue(mockResponse)

      const onDataReceived = vi.fn()
      const onComplete = vi.fn()
      const onError = vi.fn()

      await apiService.sendMessage('test message', onDataReceived, onComplete, onError)

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/ds', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ msg: 'test message' }),
        signal: expect.any(AbortSignal)
      })
    })

    it('should handle fetch error', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      const onDataReceived = vi.fn()
      const onComplete = vi.fn()
      const onError = vi.fn()

      await expect(apiService.sendMessage('test message', onDataReceived, onComplete, onError))
        .rejects.toThrow('Network error')

      expect(onError).toHaveBeenCalledWith(expect.any(Error))
    })
  })

  describe('cancelCurrentRequest', () => {
    it('should abort current request', () => {
      const mockAbort = vi.fn()
      apiService.abortController = { abort: mockAbort }

      apiService.cancelCurrentRequest()

      expect(mockAbort).toHaveBeenCalled()
      expect(apiService.abortController).toBeNull()
    })

    it('should handle null abortController', () => {
      apiService.abortController = null

      expect(() => apiService.cancelCurrentRequest()).not.toThrow()
    })
  })

  describe('getApiKeys', () => {
    it('should successfully get API keys', async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({ 
          AMAP_CODE: 'test_code', 
          AMAP_KEY: 'test_key' 
        })
      }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await apiService.getApiKeys()

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/amap_keys', {
        method: 'GET',
        credentials: 'include'
      })
      expect(result).toEqual({ AMAP_CODE: 'test_code', AMAP_KEY: 'test_key' })
    })
  })

  describe('getMapScriptUrl', () => {
    it('should successfully get map script URL', async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({ scriptUrl: 'https://example.com/map.js' })
      }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await apiService.getMapScriptUrl()

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/amap', {
        method: 'GET',
        credentials: 'include'
      })
      expect(result).toEqual({ scriptUrl: 'https://example.com/map.js' })
    })
  })

  describe('saveMapImage', () => {
    it('should successfully save map image', async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({ success: true, url: 'https://example.com/map.jpg' })
      }
      mockFetch.mockResolvedValue(mockResponse)

      const imageData = { image: 'base64data', user: 'testuser', filename: 'map.jpg' }
      const result = await apiService.saveMapImage(imageData)

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/save_amap_img', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          image: 'base64data',
          user: 'testuser',
          filename: 'map.jpg'
        })
      })
      expect(result).toEqual({ success: true, url: 'https://example.com/map.jpg' })
    })
  })

  describe('checkConnection', () => {
    it('should return true when connection is successful', async () => {
      const mockResponse = { ok: true }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await apiService.checkConnection()

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/health', {
        method: 'GET',
        timeout: 5000
      })
      expect(result).toBe(true)
    })

    it('should return false when connection fails', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      const result = await apiService.checkConnection()

      expect(result).toBe(false)
    })
  })

  describe('cleanup', () => {
    it('should cancel current request and reset abortController', () => {
      const mockAbort = vi.fn()
      apiService.abortController = { abort: mockAbort }

      apiService.cleanup()

      expect(mockAbort).toHaveBeenCalled()
      expect(apiService.abortController).toBeNull()
    })
  })
}) 