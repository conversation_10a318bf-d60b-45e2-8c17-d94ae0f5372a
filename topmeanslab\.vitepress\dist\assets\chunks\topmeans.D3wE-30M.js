import{_ as D,u as Qe,a as tn,v as da,l as ua,c as ha,E as ce}from"./theme.DE6uTiF9.js";import{m as os}from"./TopmeansMarkdownService.C-2qduF6.js";import{_ as ls,p as Ae,v as ga,C as Ue,c as _,e as yA,o as M,j as B,G as eA,t as I,F as vA,B as DA,w as UA,k as He,b as wa,a as J,ao as H,ap as S,ab as fA,n as oA,aJ as Yt,b4 as $e,X as rn,Z as Zt}from"./framework.oPHriSgN.js";import{_ as fa}from"./PaymentMethods.BgGK8a_4.js";/*!
 * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
 * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>
 * Released under MIT License
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var fr=function(e,A){return fr=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])},fr(e,A)};function CA(e,A){if(typeof A!="function"&&A!==null)throw new TypeError("Class extends value "+String(A)+" is not a constructor or null");fr(e,A);function t(){this.constructor=e}e.prototype=A===null?Object.create(A):(t.prototype=A.prototype,new t)}var Qr=function(){return Qr=Object.assign||function(A){for(var t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(A[s]=t[s])}return A},Qr.apply(this,arguments)};function nA(e,A,t,r){function n(s){return s instanceof t?s:new t(function(a){a(s)})}return new(t||(t=Promise))(function(s,a){function o(c){try{l(r.next(c))}catch(x){a(x)}}function i(c){try{l(r.throw(c))}catch(x){a(x)}}function l(c){c.done?s(c.value):n(c.value).then(o,i)}l((r=r.apply(e,[])).next())})}function tA(e,A){var t={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},r,n,s,a;return a={next:o(0),throw:o(1),return:o(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function o(l){return function(c){return i([l,c])}}function i(l){if(r)throw new TypeError("Generator is already executing.");for(;t;)try{if(r=1,n&&(s=l[0]&2?n.return:l[0]?n.throw||((s=n.return)&&s.call(n),0):n.next)&&!(s=s.call(n,l[1])).done)return s;switch(n=0,s&&(l=[l[0]&2,s.value]),l[0]){case 0:case 1:s=l;break;case 4:return t.label++,{value:l[1],done:!1};case 5:t.label++,n=l[1],l=[0];continue;case 7:l=t.ops.pop(),t.trys.pop();continue;default:if(s=t.trys,!(s=s.length>0&&s[s.length-1])&&(l[0]===6||l[0]===2)){t=0;continue}if(l[0]===3&&(!s||l[1]>s[0]&&l[1]<s[3])){t.label=l[1];break}if(l[0]===6&&t.label<s[1]){t.label=s[1],s=l;break}if(s&&t.label<s[2]){t.label=s[2],t.ops.push(l);break}s[2]&&t.ops.pop(),t.trys.pop();continue}l=A.call(e,t)}catch(c){l=[6,c],n=0}finally{r=s=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}function At(e,A,t){if(arguments.length===2)for(var r=0,n=A.length,s;r<n;r++)(s||!(r in A))&&(s||(s=Array.prototype.slice.call(A,0,r)),s[r]=A[r]);return e.concat(s||A)}var MA=function(){function e(A,t,r,n){this.left=A,this.top=t,this.width=r,this.height=n}return e.prototype.add=function(A,t,r,n){return new e(this.left+A,this.top+t,this.width+r,this.height+n)},e.fromClientRect=function(A,t){return new e(t.left+A.windowBounds.left,t.top+A.windowBounds.top,t.width,t.height)},e.fromDOMRectList=function(A,t){var r=Array.from(t).find(function(n){return n.width!==0});return r?new e(r.left+A.windowBounds.left,r.top+A.windowBounds.top,r.width,r.height):e.EMPTY},e.EMPTY=new e(0,0,0,0),e}(),Ot=function(e,A){return MA.fromClientRect(e,A.getBoundingClientRect())},Qa=function(e){var A=e.body,t=e.documentElement;if(!A||!t)throw new Error("Unable to get document size");var r=Math.max(Math.max(A.scrollWidth,t.scrollWidth),Math.max(A.offsetWidth,t.offsetWidth),Math.max(A.clientWidth,t.clientWidth)),n=Math.max(Math.max(A.scrollHeight,t.scrollHeight),Math.max(A.offsetHeight,t.offsetHeight),Math.max(A.clientHeight,t.clientHeight));return new MA(0,0,r,n)},kt=function(e){for(var A=[],t=0,r=e.length;t<r;){var n=e.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var s=e.charCodeAt(t++);(s&64512)===56320?A.push(((n&1023)<<10)+(s&1023)+65536):(A.push(n),t--)}else A.push(n)}return A},X=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var t=e.length;if(!t)return"";for(var r=[],n=-1,s="";++n<t;){var a=e[n];a<=65535?r.push(a):(a-=65536,r.push((a>>10)+55296,a%1024+56320)),(n+1===t||r.length>16384)&&(s+=String.fromCharCode.apply(String,r),r.length=0)}return s},nn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Ca=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var et=0;et<nn.length;et++)Ca[nn.charCodeAt(et)]=et;var sn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",De=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var tt=0;tt<sn.length;tt++)De[sn.charCodeAt(tt)]=tt;var pa=function(e){var A=e.length*.75,t=e.length,r,n=0,s,a,o,i;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var l=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),c=Array.isArray(l)?l:new Uint8Array(l);for(r=0;r<t;r+=4)s=De[e.charCodeAt(r)],a=De[e.charCodeAt(r+1)],o=De[e.charCodeAt(r+2)],i=De[e.charCodeAt(r+3)],c[n++]=s<<2|a>>4,c[n++]=(a&15)<<4|o>>2,c[n++]=(o&3)<<6|i&63;return l},Ua=function(e){for(var A=e.length,t=[],r=0;r<A;r+=2)t.push(e[r+1]<<8|e[r]);return t},Fa=function(e){for(var A=e.length,t=[],r=0;r<A;r+=4)t.push(e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]);return t},ie=5,Zr=11,zt=2,ma=Zr-ie,cs=65536>>ie,va=1<<ie,qt=va-1,ya=1024>>ie,Ea=cs+ya,Ha=Ea,Ia=32,ba=Ha+Ia,Sa=65536>>Zr,La=1<<ma,Da=La-1,an=function(e,A,t){return e.slice?e.slice(A,t):new Uint16Array(Array.prototype.slice.call(e,A,t))},Ka=function(e,A,t){return e.slice?e.slice(A,t):new Uint32Array(Array.prototype.slice.call(e,A,t))},Ta=function(e,A){var t=pa(e),r=Array.isArray(t)?Fa(t):new Uint32Array(t),n=Array.isArray(t)?Ua(t):new Uint16Array(t),s=24,a=an(n,s/2,r[4]/2),o=r[5]===2?an(n,(s+r[4])/2):Ka(r,Math.ceil((s+r[4])/4));return new Ma(r[0],r[1],r[2],r[3],a,o)},Ma=function(){function e(A,t,r,n,s,a){this.initialValue=A,this.errorValue=t,this.highStart=r,this.highValueIndex=n,this.index=s,this.data=a}return e.prototype.get=function(A){var t;if(A>=0){if(A<55296||A>56319&&A<=65535)return t=this.index[A>>ie],t=(t<<zt)+(A&qt),this.data[t];if(A<=65535)return t=this.index[cs+(A-55296>>ie)],t=(t<<zt)+(A&qt),this.data[t];if(A<this.highStart)return t=ba-Sa+(A>>Zr),t=this.index[t],t+=A>>ie&Da,t=this.index[t],t=(t<<zt)+(A&qt),this.data[t];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),on="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",_a=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var rt=0;rt<on.length;rt++)_a[on.charCodeAt(rt)]=rt;var Oa="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",ln=50,ka=1,Bs=2,xs=3,Ra=4,Ga=5,cn=7,ds=8,Bn=9,NA=10,Cr=11,xn=12,pr=13,Pa=14,Ke=15,Ur=16,nt=17,Ie=18,Va=19,dn=20,Fr=21,be=22,jt=23,Be=24,dA=25,Te=26,Me=27,xe=28,Na=29,se=30,Ja=31,st=32,at=33,mr=34,vr=35,yr=36,Je=37,Er=38,mt=39,vt=40,$t=41,us=42,Xa=43,Wa=[9001,65288],hs="!",K="×",it="÷",Hr=Ta(Oa),SA=[se,yr],Ir=[ka,Bs,xs,Ga],gs=[NA,ds],un=[Me,Te],Ya=Ir.concat(gs),hn=[Er,mt,vt,mr,vr],Za=[Ke,pr],za=function(e,A){A===void 0&&(A="strict");var t=[],r=[],n=[];return e.forEach(function(s,a){var o=Hr.get(s);if(o>ln?(n.push(!0),o-=ln):n.push(!1),["normal","auto","loose"].indexOf(A)!==-1&&[8208,8211,12316,12448].indexOf(s)!==-1)return r.push(a),t.push(Ur);if(o===Ra||o===Cr){if(a===0)return r.push(a),t.push(se);var i=t[a-1];return Ya.indexOf(i)===-1?(r.push(r[a-1]),t.push(i)):(r.push(a),t.push(se))}if(r.push(a),o===Ja)return t.push(A==="strict"?Fr:Je);if(o===us||o===Na)return t.push(se);if(o===Xa)return s>=131072&&s<=196605||s>=196608&&s<=262141?t.push(Je):t.push(se);t.push(o)}),[r,t,n]},Ar=function(e,A,t,r){var n=r[t];if(Array.isArray(e)?e.indexOf(n)!==-1:e===n)for(var s=t;s<=r.length;){s++;var a=r[s];if(a===A)return!0;if(a!==NA)break}if(n===NA)for(var s=t;s>0;){s--;var o=r[s];if(Array.isArray(e)?e.indexOf(o)!==-1:e===o)for(var i=t;i<=r.length;){i++;var a=r[i];if(a===A)return!0;if(a!==NA)break}if(o!==NA)break}return!1},gn=function(e,A){for(var t=e;t>=0;){var r=A[t];if(r===NA)t--;else return r}return 0},qa=function(e,A,t,r,n){if(t[r]===0)return K;var s=r-1;if(Array.isArray(n)&&n[s]===!0)return K;var a=s-1,o=s+1,i=A[s],l=a>=0?A[a]:0,c=A[o];if(i===Bs&&c===xs)return K;if(Ir.indexOf(i)!==-1)return hs;if(Ir.indexOf(c)!==-1||gs.indexOf(c)!==-1)return K;if(gn(s,A)===ds)return it;if(Hr.get(e[s])===Cr||(i===st||i===at)&&Hr.get(e[o])===Cr||i===cn||c===cn||i===Bn||[NA,pr,Ke].indexOf(i)===-1&&c===Bn||[nt,Ie,Va,Be,xe].indexOf(c)!==-1||gn(s,A)===be||Ar(jt,be,s,A)||Ar([nt,Ie],Fr,s,A)||Ar(xn,xn,s,A))return K;if(i===NA)return it;if(i===jt||c===jt)return K;if(c===Ur||i===Ur)return it;if([pr,Ke,Fr].indexOf(c)!==-1||i===Pa||l===yr&&Za.indexOf(i)!==-1||i===xe&&c===yr||c===dn||SA.indexOf(c)!==-1&&i===dA||SA.indexOf(i)!==-1&&c===dA||i===Me&&[Je,st,at].indexOf(c)!==-1||[Je,st,at].indexOf(i)!==-1&&c===Te||SA.indexOf(i)!==-1&&un.indexOf(c)!==-1||un.indexOf(i)!==-1&&SA.indexOf(c)!==-1||[Me,Te].indexOf(i)!==-1&&(c===dA||[be,Ke].indexOf(c)!==-1&&A[o+1]===dA)||[be,Ke].indexOf(i)!==-1&&c===dA||i===dA&&[dA,xe,Be].indexOf(c)!==-1)return K;if([dA,xe,Be,nt,Ie].indexOf(c)!==-1)for(var x=s;x>=0;){var d=A[x];if(d===dA)return K;if([xe,Be].indexOf(d)!==-1)x--;else break}if([Me,Te].indexOf(c)!==-1)for(var x=[nt,Ie].indexOf(i)!==-1?a:s;x>=0;){var d=A[x];if(d===dA)return K;if([xe,Be].indexOf(d)!==-1)x--;else break}if(Er===i&&[Er,mt,mr,vr].indexOf(c)!==-1||[mt,mr].indexOf(i)!==-1&&[mt,vt].indexOf(c)!==-1||[vt,vr].indexOf(i)!==-1&&c===vt||hn.indexOf(i)!==-1&&[dn,Te].indexOf(c)!==-1||hn.indexOf(c)!==-1&&i===Me||SA.indexOf(i)!==-1&&SA.indexOf(c)!==-1||i===Be&&SA.indexOf(c)!==-1||SA.concat(dA).indexOf(i)!==-1&&c===be&&Wa.indexOf(e[o])===-1||SA.concat(dA).indexOf(c)!==-1&&i===Ie)return K;if(i===$t&&c===$t){for(var f=t[s],u=1;f>0&&(f--,A[f]===$t);)u++;if(u%2!==0)return K}return i===st&&c===at?K:it},ja=function(e,A){A||(A={lineBreak:"normal",wordBreak:"normal"});var t=za(e,A.lineBreak),r=t[0],n=t[1],s=t[2];(A.wordBreak==="break-all"||A.wordBreak==="break-word")&&(n=n.map(function(o){return[dA,se,us].indexOf(o)!==-1?Je:o}));var a=A.wordBreak==="keep-all"?s.map(function(o,i){return o&&e[i]>=19968&&e[i]<=40959}):void 0;return[r,n,a]},$a=function(){function e(A,t,r,n){this.codePoints=A,this.required=t===hs,this.start=r,this.end=n}return e.prototype.slice=function(){return X.apply(void 0,this.codePoints.slice(this.start,this.end))},e}(),Ai=function(e,A){var t=kt(e),r=ja(t,A),n=r[0],s=r[1],a=r[2],o=t.length,i=0,l=0;return{next:function(){if(l>=o)return{done:!0,value:null};for(var c=K;l<o&&(c=qa(t,s,n,++l,a))===K;);if(c!==K||l===o){var x=new $a(t,c,i,l);return i=l,{value:x,done:!1}}return{done:!0,value:null}}}},ei=1,ti=2,qe=4,wn=8,Ht=10,fn=47,Re=92,ri=9,ni=32,ot=34,Se=61,si=35,ai=36,ii=37,lt=39,ct=40,Le=41,oi=95,cA=45,li=33,ci=60,Bi=62,xi=64,di=91,ui=93,hi=61,gi=123,Bt=63,wi=125,Qn=124,fi=126,Qi=128,Cn=65533,er=42,ae=43,Ci=44,pi=58,Ui=59,Xe=46,Fi=0,mi=8,vi=11,yi=14,Ei=31,Hi=127,FA=-1,ws=48,fs=97,Qs=101,Ii=102,bi=117,Si=122,Cs=65,ps=69,Us=70,Li=85,Di=90,rA=function(e){return e>=ws&&e<=57},Ki=function(e){return e>=55296&&e<=57343},de=function(e){return rA(e)||e>=Cs&&e<=Us||e>=fs&&e<=Ii},Ti=function(e){return e>=fs&&e<=Si},Mi=function(e){return e>=Cs&&e<=Di},_i=function(e){return Ti(e)||Mi(e)},Oi=function(e){return e>=Qi},xt=function(e){return e===Ht||e===ri||e===ni},It=function(e){return _i(e)||Oi(e)||e===oi},pn=function(e){return It(e)||rA(e)||e===cA},ki=function(e){return e>=Fi&&e<=mi||e===vi||e>=yi&&e<=Ei||e===Hi},VA=function(e,A){return e!==Re?!1:A!==Ht},dt=function(e,A,t){return e===cA?It(A)||VA(A,t):It(e)?!0:!!(e===Re&&VA(e,A))},tr=function(e,A,t){return e===ae||e===cA?rA(A)?!0:A===Xe&&rA(t):rA(e===Xe?A:e)},Ri=function(e){var A=0,t=1;(e[A]===ae||e[A]===cA)&&(e[A]===cA&&(t=-1),A++);for(var r=[];rA(e[A]);)r.push(e[A++]);var n=r.length?parseInt(X.apply(void 0,r),10):0;e[A]===Xe&&A++;for(var s=[];rA(e[A]);)s.push(e[A++]);var a=s.length,o=a?parseInt(X.apply(void 0,s),10):0;(e[A]===ps||e[A]===Qs)&&A++;var i=1;(e[A]===ae||e[A]===cA)&&(e[A]===cA&&(i=-1),A++);for(var l=[];rA(e[A]);)l.push(e[A++]);var c=l.length?parseInt(X.apply(void 0,l),10):0;return t*(n+o*Math.pow(10,-a))*Math.pow(10,i*c)},Gi={type:2},Pi={type:3},Vi={type:4},Ni={type:13},Ji={type:8},Xi={type:21},Wi={type:9},Yi={type:10},Zi={type:11},zi={type:12},qi={type:14},ut={type:23},ji={type:1},$i={type:25},Ao={type:24},eo={type:26},to={type:27},ro={type:28},no={type:29},so={type:31},br={type:32},Fs=function(){function e(){this._value=[]}return e.prototype.write=function(A){this._value=this._value.concat(kt(A))},e.prototype.read=function(){for(var A=[],t=this.consumeToken();t!==br;)A.push(t),t=this.consumeToken();return A},e.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case ot:return this.consumeStringToken(ot);case si:var t=this.peekCodePoint(0),r=this.peekCodePoint(1),n=this.peekCodePoint(2);if(pn(t)||VA(r,n)){var s=dt(t,r,n)?ti:ei,a=this.consumeName();return{type:5,value:a,flags:s}}break;case ai:if(this.peekCodePoint(0)===Se)return this.consumeCodePoint(),Ni;break;case lt:return this.consumeStringToken(lt);case ct:return Gi;case Le:return Pi;case er:if(this.peekCodePoint(0)===Se)return this.consumeCodePoint(),qi;break;case ae:if(tr(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case Ci:return Vi;case cA:var o=A,i=this.peekCodePoint(0),l=this.peekCodePoint(1);if(tr(o,i,l))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(dt(o,i,l))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(i===cA&&l===Bi)return this.consumeCodePoint(),this.consumeCodePoint(),Ao;break;case Xe:if(tr(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case fn:if(this.peekCodePoint(0)===er)for(this.consumeCodePoint();;){var c=this.consumeCodePoint();if(c===er&&(c=this.consumeCodePoint(),c===fn))return this.consumeToken();if(c===FA)return this.consumeToken()}break;case pi:return eo;case Ui:return to;case ci:if(this.peekCodePoint(0)===li&&this.peekCodePoint(1)===cA&&this.peekCodePoint(2)===cA)return this.consumeCodePoint(),this.consumeCodePoint(),$i;break;case xi:var x=this.peekCodePoint(0),d=this.peekCodePoint(1),f=this.peekCodePoint(2);if(dt(x,d,f)){var a=this.consumeName();return{type:7,value:a}}break;case di:return ro;case Re:if(VA(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case ui:return no;case hi:if(this.peekCodePoint(0)===Se)return this.consumeCodePoint(),Ji;break;case gi:return Zi;case wi:return zi;case bi:case Li:var u=this.peekCodePoint(0),h=this.peekCodePoint(1);return u===ae&&(de(h)||h===Bt)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case Qn:if(this.peekCodePoint(0)===Se)return this.consumeCodePoint(),Wi;if(this.peekCodePoint(0)===Qn)return this.consumeCodePoint(),Xi;break;case fi:if(this.peekCodePoint(0)===Se)return this.consumeCodePoint(),Yi;break;case FA:return br}return xt(A)?(this.consumeWhiteSpace(),so):rA(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):It(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:X(A)}},e.prototype.consumeCodePoint=function(){var A=this._value.shift();return typeof A>"u"?-1:A},e.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},e.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},e.prototype.consumeUnicodeRangeToken=function(){for(var A=[],t=this.consumeCodePoint();de(t)&&A.length<6;)A.push(t),t=this.consumeCodePoint();for(var r=!1;t===Bt&&A.length<6;)A.push(t),t=this.consumeCodePoint(),r=!0;if(r){var n=parseInt(X.apply(void 0,A.map(function(i){return i===Bt?ws:i})),16),s=parseInt(X.apply(void 0,A.map(function(i){return i===Bt?Us:i})),16);return{type:30,start:n,end:s}}var a=parseInt(X.apply(void 0,A),16);if(this.peekCodePoint(0)===cA&&de(this.peekCodePoint(1))){this.consumeCodePoint(),t=this.consumeCodePoint();for(var o=[];de(t)&&o.length<6;)o.push(t),t=this.consumeCodePoint();var s=parseInt(X.apply(void 0,o),16);return{type:30,start:a,end:s}}else return{type:30,start:a,end:a}},e.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return A.toLowerCase()==="url"&&this.peekCodePoint(0)===ct?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===ct?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},e.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===FA)return{type:22,value:""};var t=this.peekCodePoint(0);if(t===lt||t===ot){var r=this.consumeStringToken(this.consumeCodePoint());return r.type===0&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===FA||this.peekCodePoint(0)===Le)?(this.consumeCodePoint(),{type:22,value:r.value}):(this.consumeBadUrlRemnants(),ut)}for(;;){var n=this.consumeCodePoint();if(n===FA||n===Le)return{type:22,value:X.apply(void 0,A)};if(xt(n))return this.consumeWhiteSpace(),this.peekCodePoint(0)===FA||this.peekCodePoint(0)===Le?(this.consumeCodePoint(),{type:22,value:X.apply(void 0,A)}):(this.consumeBadUrlRemnants(),ut);if(n===ot||n===lt||n===ct||ki(n))return this.consumeBadUrlRemnants(),ut;if(n===Re)if(VA(n,this.peekCodePoint(0)))A.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),ut;else A.push(n)}},e.prototype.consumeWhiteSpace=function(){for(;xt(this.peekCodePoint(0));)this.consumeCodePoint()},e.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(A===Le||A===FA)return;VA(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},e.prototype.consumeStringSlice=function(A){for(var t=5e4,r="";A>0;){var n=Math.min(t,A);r+=X.apply(void 0,this._value.splice(0,n)),A-=n}return this._value.shift(),r},e.prototype.consumeStringToken=function(A){var t="",r=0;do{var n=this._value[r];if(n===FA||n===void 0||n===A)return t+=this.consumeStringSlice(r),{type:0,value:t};if(n===Ht)return this._value.splice(0,r),ji;if(n===Re){var s=this._value[r+1];s!==FA&&s!==void 0&&(s===Ht?(t+=this.consumeStringSlice(r),r=-1,this._value.shift()):VA(n,s)&&(t+=this.consumeStringSlice(r),t+=X(this.consumeEscapedCodePoint()),r=-1))}r++}while(!0)},e.prototype.consumeNumber=function(){var A=[],t=qe,r=this.peekCodePoint(0);for((r===ae||r===cA)&&A.push(this.consumeCodePoint());rA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());r=this.peekCodePoint(0);var n=this.peekCodePoint(1);if(r===Xe&&rA(n))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),t=wn;rA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());r=this.peekCodePoint(0),n=this.peekCodePoint(1);var s=this.peekCodePoint(2);if((r===ps||r===Qs)&&((n===ae||n===cA)&&rA(s)||rA(n)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),t=wn;rA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[Ri(A),t]},e.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),t=A[0],r=A[1],n=this.peekCodePoint(0),s=this.peekCodePoint(1),a=this.peekCodePoint(2);if(dt(n,s,a)){var o=this.consumeName();return{type:15,number:t,flags:r,unit:o}}return n===ii?(this.consumeCodePoint(),{type:16,number:t,flags:r}):{type:17,number:t,flags:r}},e.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(de(A)){for(var t=X(A);de(this.peekCodePoint(0))&&t.length<6;)t+=X(this.consumeCodePoint());xt(this.peekCodePoint(0))&&this.consumeCodePoint();var r=parseInt(t,16);return r===0||Ki(r)||r>1114111?Cn:r}return A===FA?Cn:A},e.prototype.consumeName=function(){for(var A="";;){var t=this.consumeCodePoint();if(pn(t))A+=X(t);else if(VA(t,this.peekCodePoint(0)))A+=X(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(t),A}},e}(),ms=function(){function e(A){this._tokens=A}return e.create=function(A){var t=new Fs;return t.write(A),new e(t.read())},e.parseValue=function(A){return e.create(A).parseComponentValue()},e.parseValues=function(A){return e.create(A).parseComponentValues()},e.prototype.parseComponentValue=function(){for(var A=this.consumeToken();A.type===31;)A=this.consumeToken();if(A.type===32)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var t=this.consumeComponentValue();do A=this.consumeToken();while(A.type===31);if(A.type===32)return t;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},e.prototype.parseComponentValues=function(){for(var A=[];;){var t=this.consumeComponentValue();if(t.type===32)return A;A.push(t),A.push()}},e.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},e.prototype.consumeSimpleBlock=function(A){for(var t={type:A,values:[]},r=this.consumeToken();;){if(r.type===32||io(r,A))return t;this.reconsumeToken(r),t.values.push(this.consumeComponentValue()),r=this.consumeToken()}},e.prototype.consumeFunction=function(A){for(var t={name:A.value,values:[],type:18};;){var r=this.consumeToken();if(r.type===32||r.type===3)return t;this.reconsumeToken(r),t.values.push(this.consumeComponentValue())}},e.prototype.consumeToken=function(){var A=this._tokens.shift();return typeof A>"u"?br:A},e.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},e}(),je=function(e){return e.type===15},ye=function(e){return e.type===17},G=function(e){return e.type===20},ao=function(e){return e.type===0},Sr=function(e,A){return G(e)&&e.value===A},vs=function(e){return e.type!==31},ve=function(e){return e.type!==31&&e.type!==4},EA=function(e){var A=[],t=[];return e.forEach(function(r){if(r.type===4){if(t.length===0)throw new Error("Error parsing function args, zero tokens for arg");A.push(t),t=[];return}r.type!==31&&t.push(r)}),t.length&&A.push(t),A},io=function(e,A){return A===11&&e.type===12||A===28&&e.type===29?!0:A===2&&e.type===3},ZA=function(e){return e.type===17||e.type===15},W=function(e){return e.type===16||ZA(e)},ys=function(e){return e.length>1?[e[0],e[1]]:[e[0]]},AA={type:17,number:0,flags:qe},zr={type:16,number:50,flags:qe},JA={type:16,number:100,flags:qe},_e=function(e,A,t){var r=e[0],n=e[1];return[P(r,A),P(typeof n<"u"?n:r,t)]},P=function(e,A){if(e.type===16)return e.number/100*A;if(je(e))switch(e.unit){case"rem":case"em":return 16*e.number;case"px":default:return e.number}return e.number},Es="deg",Hs="grad",Is="rad",bs="turn",Rt={name:"angle",parse:function(e,A){if(A.type===15)switch(A.unit){case Es:return Math.PI*A.number/180;case Hs:return Math.PI/200*A.number;case Is:return A.number;case bs:return Math.PI*2*A.number}throw new Error("Unsupported angle type")}},Ss=function(e){return e.type===15&&(e.unit===Es||e.unit===Hs||e.unit===Is||e.unit===bs)},Ls=function(e){var A=e.filter(G).map(function(t){return t.value}).join(" ");switch(A){case"to bottom right":case"to right bottom":case"left top":case"top left":return[AA,AA];case"to top":case"bottom":return gA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[AA,JA];case"to right":case"left":return gA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[JA,JA];case"to bottom":case"top":return gA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[JA,AA];case"to left":case"right":return gA(270)}return 0},gA=function(e){return Math.PI*e/180},WA={name:"color",parse:function(e,A){if(A.type===18){var t=oo[A.name];if(typeof t>"u")throw new Error('Attempting to parse an unsupported color function "'+A.name+'"');return t(e,A.values)}if(A.type===5){if(A.value.length===3){var r=A.value.substring(0,1),n=A.value.substring(1,2),s=A.value.substring(2,3);return XA(parseInt(r+r,16),parseInt(n+n,16),parseInt(s+s,16),1)}if(A.value.length===4){var r=A.value.substring(0,1),n=A.value.substring(1,2),s=A.value.substring(2,3),a=A.value.substring(3,4);return XA(parseInt(r+r,16),parseInt(n+n,16),parseInt(s+s,16),parseInt(a+a,16)/255)}if(A.value.length===6){var r=A.value.substring(0,2),n=A.value.substring(2,4),s=A.value.substring(4,6);return XA(parseInt(r,16),parseInt(n,16),parseInt(s,16),1)}if(A.value.length===8){var r=A.value.substring(0,2),n=A.value.substring(2,4),s=A.value.substring(4,6),a=A.value.substring(6,8);return XA(parseInt(r,16),parseInt(n,16),parseInt(s,16),parseInt(a,16)/255)}}if(A.type===20){var o=TA[A.value.toUpperCase()];if(typeof o<"u")return o}return TA.TRANSPARENT}},YA=function(e){return(255&e)===0},z=function(e){var A=255&e,t=255&e>>8,r=255&e>>16,n=255&e>>24;return A<255?"rgba("+n+","+r+","+t+","+A/255+")":"rgb("+n+","+r+","+t+")"},XA=function(e,A,t,r){return(e<<24|A<<16|t<<8|Math.round(r*255)<<0)>>>0},Un=function(e,A){if(e.type===17)return e.number;if(e.type===16){var t=A===3?1:255;return A===3?e.number/100*t:Math.round(e.number/100*t)}return 0},Fn=function(e,A){var t=A.filter(ve);if(t.length===3){var r=t.map(Un),n=r[0],s=r[1],a=r[2];return XA(n,s,a,1)}if(t.length===4){var o=t.map(Un),n=o[0],s=o[1],a=o[2],i=o[3];return XA(n,s,a,i)}return 0};function rr(e,A,t){return t<0&&(t+=1),t>=1&&(t-=1),t<1/6?(A-e)*t*6+e:t<1/2?A:t<2/3?(A-e)*6*(2/3-t)+e:e}var mn=function(e,A){var t=A.filter(ve),r=t[0],n=t[1],s=t[2],a=t[3],o=(r.type===17?gA(r.number):Rt.parse(e,r))/(Math.PI*2),i=W(n)?n.number/100:0,l=W(s)?s.number/100:0,c=typeof a<"u"&&W(a)?P(a,1):1;if(i===0)return XA(l*255,l*255,l*255,1);var x=l<=.5?l*(i+1):l+i-l*i,d=l*2-x,f=rr(d,x,o+1/3),u=rr(d,x,o),h=rr(d,x,o-1/3);return XA(f*255,u*255,h*255,c)},oo={hsl:mn,hsla:mn,rgb:Fn,rgba:Fn},Ge=function(e,A){return WA.parse(e,ms.create(A).parseComponentValue())},TA={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},lo={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(t){if(G(t))switch(t.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},co={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Gt=function(e,A){var t=WA.parse(e,A[0]),r=A[1];return r&&W(r)?{color:t,stop:r}:{color:t,stop:null}},vn=function(e,A){var t=e[0],r=e[e.length-1];t.stop===null&&(t.stop=AA),r.stop===null&&(r.stop=JA);for(var n=[],s=0,a=0;a<e.length;a++){var o=e[a].stop;if(o!==null){var i=P(o,A);i>s?n.push(i):n.push(s),s=i}else n.push(null)}for(var l=null,a=0;a<n.length;a++){var c=n[a];if(c===null)l===null&&(l=a);else if(l!==null){for(var x=a-l,d=n[l-1],f=(c-d)/(x+1),u=1;u<=x;u++)n[l+u-1]=f*u;l=null}}return e.map(function(h,v){var w=h.color;return{color:w,stop:Math.max(Math.min(1,n[v]/A),0)}})},Bo=function(e,A,t){var r=A/2,n=t/2,s=P(e[0],A)-r,a=n-P(e[1],t);return(Math.atan2(a,s)+Math.PI*2)%(Math.PI*2)},xo=function(e,A,t){var r=typeof e=="number"?e:Bo(e,A,t),n=Math.abs(A*Math.sin(r))+Math.abs(t*Math.cos(r)),s=A/2,a=t/2,o=n/2,i=Math.sin(r-Math.PI/2)*o,l=Math.cos(r-Math.PI/2)*o;return[n,s-l,s+l,a-i,a+i]},QA=function(e,A){return Math.sqrt(e*e+A*A)},yn=function(e,A,t,r,n){var s=[[0,0],[0,A],[e,0],[e,A]];return s.reduce(function(a,o){var i=o[0],l=o[1],c=QA(t-i,r-l);return(n?c<a.optimumDistance:c>a.optimumDistance)?{optimumCorner:o,optimumDistance:c}:a},{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},uo=function(e,A,t,r,n){var s=0,a=0;switch(e.size){case 0:e.shape===0?s=a=Math.min(Math.abs(A),Math.abs(A-r),Math.abs(t),Math.abs(t-n)):e.shape===1&&(s=Math.min(Math.abs(A),Math.abs(A-r)),a=Math.min(Math.abs(t),Math.abs(t-n)));break;case 2:if(e.shape===0)s=a=Math.min(QA(A,t),QA(A,t-n),QA(A-r,t),QA(A-r,t-n));else if(e.shape===1){var o=Math.min(Math.abs(t),Math.abs(t-n))/Math.min(Math.abs(A),Math.abs(A-r)),i=yn(r,n,A,t,!0),l=i[0],c=i[1];s=QA(l-A,(c-t)/o),a=o*s}break;case 1:e.shape===0?s=a=Math.max(Math.abs(A),Math.abs(A-r),Math.abs(t),Math.abs(t-n)):e.shape===1&&(s=Math.max(Math.abs(A),Math.abs(A-r)),a=Math.max(Math.abs(t),Math.abs(t-n)));break;case 3:if(e.shape===0)s=a=Math.max(QA(A,t),QA(A,t-n),QA(A-r,t),QA(A-r,t-n));else if(e.shape===1){var o=Math.max(Math.abs(t),Math.abs(t-n))/Math.max(Math.abs(A),Math.abs(A-r)),x=yn(r,n,A,t,!1),l=x[0],c=x[1];s=QA(l-A,(c-t)/o),a=o*s}break}return Array.isArray(e.size)&&(s=P(e.size[0],r),a=e.size.length===2?P(e.size[1],n):s),[s,a]},ho=function(e,A){var t=gA(180),r=[];return EA(A).forEach(function(n,s){if(s===0){var a=n[0];if(a.type===20&&a.value==="to"){t=Ls(n);return}else if(Ss(a)){t=Rt.parse(e,a);return}}var o=Gt(e,n);r.push(o)}),{angle:t,stops:r,type:1}},ht=function(e,A){var t=gA(180),r=[];return EA(A).forEach(function(n,s){if(s===0){var a=n[0];if(a.type===20&&["top","left","right","bottom"].indexOf(a.value)!==-1){t=Ls(n);return}else if(Ss(a)){t=(Rt.parse(e,a)+gA(270))%gA(360);return}}var o=Gt(e,n);r.push(o)}),{angle:t,stops:r,type:1}},go=function(e,A){var t=gA(180),r=[],n=1,s=0,a=3,o=[];return EA(A).forEach(function(i,l){var c=i[0];if(l===0){if(G(c)&&c.value==="linear"){n=1;return}else if(G(c)&&c.value==="radial"){n=2;return}}if(c.type===18){if(c.name==="from"){var x=WA.parse(e,c.values[0]);r.push({stop:AA,color:x})}else if(c.name==="to"){var x=WA.parse(e,c.values[0]);r.push({stop:JA,color:x})}else if(c.name==="color-stop"){var d=c.values.filter(ve);if(d.length===2){var x=WA.parse(e,d[1]),f=d[0];ye(f)&&r.push({stop:{type:16,number:f.number*100,flags:f.flags},color:x})}}}}),n===1?{angle:(t+gA(180))%gA(360),stops:r,type:n}:{size:a,shape:s,stops:r,position:o,type:n}},Ds="closest-side",Ks="farthest-side",Ts="closest-corner",Ms="farthest-corner",_s="circle",Os="ellipse",ks="cover",Rs="contain",wo=function(e,A){var t=0,r=3,n=[],s=[];return EA(A).forEach(function(a,o){var i=!0;if(o===0){var l=!1;i=a.reduce(function(x,d){if(l)if(G(d))switch(d.value){case"center":return s.push(zr),x;case"top":case"left":return s.push(AA),x;case"right":case"bottom":return s.push(JA),x}else(W(d)||ZA(d))&&s.push(d);else if(G(d))switch(d.value){case _s:return t=0,!1;case Os:return t=1,!1;case"at":return l=!0,!1;case Ds:return r=0,!1;case ks:case Ks:return r=1,!1;case Rs:case Ts:return r=2,!1;case Ms:return r=3,!1}else if(ZA(d)||W(d))return Array.isArray(r)||(r=[]),r.push(d),!1;return x},i)}if(i){var c=Gt(e,a);n.push(c)}}),{size:r,shape:t,stops:n,position:s,type:2}},gt=function(e,A){var t=0,r=3,n=[],s=[];return EA(A).forEach(function(a,o){var i=!0;if(o===0?i=a.reduce(function(c,x){if(G(x))switch(x.value){case"center":return s.push(zr),!1;case"top":case"left":return s.push(AA),!1;case"right":case"bottom":return s.push(JA),!1}else if(W(x)||ZA(x))return s.push(x),!1;return c},i):o===1&&(i=a.reduce(function(c,x){if(G(x))switch(x.value){case _s:return t=0,!1;case Os:return t=1,!1;case Rs:case Ds:return r=0,!1;case Ks:return r=1,!1;case Ts:return r=2,!1;case ks:case Ms:return r=3,!1}else if(ZA(x)||W(x))return Array.isArray(r)||(r=[]),r.push(x),!1;return c},i)),i){var l=Gt(e,a);n.push(l)}}),{size:r,shape:t,stops:n,position:s,type:2}},fo=function(e){return e.type===1},Qo=function(e){return e.type===2},qr={name:"image",parse:function(e,A){if(A.type===22){var t={url:A.value,type:0};return e.cache.addImage(A.value),t}if(A.type===18){var r=Gs[A.name];if(typeof r>"u")throw new Error('Attempting to parse an unsupported image function "'+A.name+'"');return r(e,A.values)}throw new Error("Unsupported image type "+A.type)}};function Co(e){return!(e.type===20&&e.value==="none")&&(e.type!==18||!!Gs[e.name])}var Gs={"linear-gradient":ho,"-moz-linear-gradient":ht,"-ms-linear-gradient":ht,"-o-linear-gradient":ht,"-webkit-linear-gradient":ht,"radial-gradient":wo,"-moz-radial-gradient":gt,"-ms-radial-gradient":gt,"-o-radial-gradient":gt,"-webkit-radial-gradient":gt,"-webkit-gradient":go},po={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var t=A[0];return t.type===20&&t.value==="none"?[]:A.filter(function(r){return ve(r)&&Co(r)}).map(function(r){return qr.parse(e,r)})}},Uo={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(t){if(G(t))switch(t.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},Fo={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(e,A){return EA(A).map(function(t){return t.filter(W)}).map(ys)}},mo={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(e,A){return EA(A).map(function(t){return t.filter(G).map(function(r){return r.value}).join(" ")}).map(vo)}},vo=function(e){switch(e){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}},me;(function(e){e.AUTO="auto",e.CONTAIN="contain",e.COVER="cover"})(me||(me={}));var yo={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(e,A){return EA(A).map(function(t){return t.filter(Eo)})}},Eo=function(e){return G(e)||W(e)},Pt=function(e){return{name:"border-"+e+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},Ho=Pt("top"),Io=Pt("right"),bo=Pt("bottom"),So=Pt("left"),Vt=function(e){return{name:"border-radius-"+e,initialValue:"0 0",prefix:!1,type:1,parse:function(A,t){return ys(t.filter(W))}}},Lo=Vt("top-left"),Do=Vt("top-right"),Ko=Vt("bottom-right"),To=Vt("bottom-left"),Nt=function(e){return{name:"border-"+e+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,t){switch(t){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},Mo=Nt("top"),_o=Nt("right"),Oo=Nt("bottom"),ko=Nt("left"),Jt=function(e){return{name:"border-"+e+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,t){return je(t)?t.number:0}}},Ro=Jt("top"),Go=Jt("right"),Po=Jt("bottom"),Vo=Jt("left"),No={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Jo={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(e,A){switch(A){case"rtl":return 1;case"ltr":default:return 0}}},Xo={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(e,A){return A.filter(G).reduce(function(t,r){return t|Wo(r.value)},0)}},Wo=function(e){switch(e){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},Yo={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},Zo={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(e,A){return A.type===20&&A.value==="normal"?0:A.type===17||A.type===15?A.number:0}},bt;(function(e){e.NORMAL="normal",e.STRICT="strict"})(bt||(bt={}));var zo={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"strict":return bt.STRICT;case"normal":default:return bt.NORMAL}}},qo={name:"line-height",initialValue:"normal",prefix:!1,type:4},En=function(e,A){return G(e)&&e.value==="normal"?1.2*A:e.type===17?A*e.number:W(e)?P(e,A):A},jo={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(e,A){return A.type===20&&A.value==="none"?null:qr.parse(e,A)}},$o={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(e,A){switch(A){case"inside":return 0;case"outside":default:return 1}}},Lr={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},Xt=function(e){return{name:"margin-"+e,initialValue:"0",prefix:!1,type:4}},Al=Xt("top"),el=Xt("right"),tl=Xt("bottom"),rl=Xt("left"),nl={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(e,A){return A.filter(G).map(function(t){switch(t.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}})}},sl={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-word":return"break-word";case"normal":default:return"normal"}}},Wt=function(e){return{name:"padding-"+e,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},al=Wt("top"),il=Wt("right"),ol=Wt("bottom"),ll=Wt("left"),cl={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(e,A){switch(A){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},Bl={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(e,A){switch(A){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},xl={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&Sr(A[0],"none")?[]:EA(A).map(function(t){for(var r={color:TA.TRANSPARENT,offsetX:AA,offsetY:AA,blur:AA},n=0,s=0;s<t.length;s++){var a=t[s];ZA(a)?(n===0?r.offsetX=a:n===1?r.offsetY=a:r.blur=a,n++):r.color=WA.parse(e,a)}return r})}},dl={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},ul={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(e,A){if(A.type===20&&A.value==="none")return null;if(A.type===18){var t=wl[A.name];if(typeof t>"u")throw new Error('Attempting to parse an unsupported transform function "'+A.name+'"');return t(A.values)}return null}},hl=function(e){var A=e.filter(function(t){return t.type===17}).map(function(t){return t.number});return A.length===6?A:null},gl=function(e){var A=e.filter(function(i){return i.type===17}).map(function(i){return i.number}),t=A[0],r=A[1];A[2],A[3];var n=A[4],s=A[5];A[6],A[7],A[8],A[9],A[10],A[11];var a=A[12],o=A[13];return A[14],A[15],A.length===16?[t,r,n,s,a,o]:null},wl={matrix:hl,matrix3d:gl},Hn={type:16,number:50,flags:qe},fl=[Hn,Hn],Ql={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(e,A){var t=A.filter(W);return t.length!==2?fl:[t[0],t[1]]}},Cl={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}},Pe;(function(e){e.NORMAL="normal",e.BREAK_ALL="break-all",e.KEEP_ALL="keep-all"})(Pe||(Pe={}));var pl={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-all":return Pe.BREAK_ALL;case"keep-all":return Pe.KEEP_ALL;case"normal":default:return Pe.NORMAL}}},Ul={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(e,A){if(A.type===20)return{auto:!0,order:0};if(ye(A))return{auto:!1,order:A.number};throw new Error("Invalid z-index number parsed")}},Ps={name:"time",parse:function(e,A){if(A.type===15)switch(A.unit.toLowerCase()){case"s":return 1e3*A.number;case"ms":return A.number}throw new Error("Unsupported time type")}},Fl={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(e,A){return ye(A)?A.number:1}},ml={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},vl={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(e,A){return A.filter(G).map(function(t){switch(t.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(t){return t!==0})}},yl={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(e,A){var t=[],r=[];return A.forEach(function(n){switch(n.type){case 20:case 0:t.push(n.value);break;case 17:t.push(n.number.toString());break;case 4:r.push(t.join(" ")),t.length=0;break}}),t.length&&r.push(t.join(" ")),r.map(function(n){return n.indexOf(" ")===-1?n:"'"+n+"'"})}},El={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},Hl={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(e,A){if(ye(A))return A.number;if(G(A))switch(A.value){case"bold":return 700;case"normal":default:return 400}return 400}},Il={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.filter(G).map(function(t){return t.value})}},bl={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},Z=function(e,A){return(e&A)!==0},Sl={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var t=A[0];return t.type===20&&t.value==="none"?[]:A}},Ll={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var t=A[0];if(t.type===20&&t.value==="none")return null;for(var r=[],n=A.filter(vs),s=0;s<n.length;s++){var a=n[s],o=n[s+1];if(a.type===20){var i=o&&ye(o)?o.number:1;r.push({counter:a.value,increment:i})}}return r}},Dl={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return[];for(var t=[],r=A.filter(vs),n=0;n<r.length;n++){var s=r[n],a=r[n+1];if(G(s)&&s.value!=="none"){var o=a&&ye(a)?a.number:0;t.push({counter:s.value,reset:o})}}return t}},Kl={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(e,A){return A.filter(je).map(function(t){return Ps.parse(e,t)})}},Tl={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var t=A[0];if(t.type===20&&t.value==="none")return null;var r=[],n=A.filter(ao);if(n.length%2!==0)return null;for(var s=0;s<n.length;s+=2){var a=n[s].value,o=n[s+1].value;r.push({open:a,close:o})}return r}},In=function(e,A,t){if(!e)return"";var r=e[Math.min(A,e.length-1)];return r?t?r.open:r.close:""},Ml={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&Sr(A[0],"none")?[]:EA(A).map(function(t){for(var r={color:255,offsetX:AA,offsetY:AA,blur:AA,spread:AA,inset:!1},n=0,s=0;s<t.length;s++){var a=t[s];Sr(a,"inset")?r.inset=!0:ZA(a)?(n===0?r.offsetX=a:n===1?r.offsetY=a:n===2?r.blur=a:r.spread=a,n++):r.color=WA.parse(e,a)}return r})}},_l={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(e,A){var t=[0,1,2],r=[];return A.filter(G).forEach(function(n){switch(n.value){case"stroke":r.push(1);break;case"fill":r.push(0);break;case"markers":r.push(2);break}}),t.forEach(function(n){r.indexOf(n)===-1&&r.push(n)}),r}},Ol={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},kl={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(e,A){return je(A)?A.number:0}},Rl=function(){function e(A,t){var r,n;this.animationDuration=F(A,Kl,t.animationDuration),this.backgroundClip=F(A,lo,t.backgroundClip),this.backgroundColor=F(A,co,t.backgroundColor),this.backgroundImage=F(A,po,t.backgroundImage),this.backgroundOrigin=F(A,Uo,t.backgroundOrigin),this.backgroundPosition=F(A,Fo,t.backgroundPosition),this.backgroundRepeat=F(A,mo,t.backgroundRepeat),this.backgroundSize=F(A,yo,t.backgroundSize),this.borderTopColor=F(A,Ho,t.borderTopColor),this.borderRightColor=F(A,Io,t.borderRightColor),this.borderBottomColor=F(A,bo,t.borderBottomColor),this.borderLeftColor=F(A,So,t.borderLeftColor),this.borderTopLeftRadius=F(A,Lo,t.borderTopLeftRadius),this.borderTopRightRadius=F(A,Do,t.borderTopRightRadius),this.borderBottomRightRadius=F(A,Ko,t.borderBottomRightRadius),this.borderBottomLeftRadius=F(A,To,t.borderBottomLeftRadius),this.borderTopStyle=F(A,Mo,t.borderTopStyle),this.borderRightStyle=F(A,_o,t.borderRightStyle),this.borderBottomStyle=F(A,Oo,t.borderBottomStyle),this.borderLeftStyle=F(A,ko,t.borderLeftStyle),this.borderTopWidth=F(A,Ro,t.borderTopWidth),this.borderRightWidth=F(A,Go,t.borderRightWidth),this.borderBottomWidth=F(A,Po,t.borderBottomWidth),this.borderLeftWidth=F(A,Vo,t.borderLeftWidth),this.boxShadow=F(A,Ml,t.boxShadow),this.color=F(A,No,t.color),this.direction=F(A,Jo,t.direction),this.display=F(A,Xo,t.display),this.float=F(A,Yo,t.cssFloat),this.fontFamily=F(A,yl,t.fontFamily),this.fontSize=F(A,El,t.fontSize),this.fontStyle=F(A,bl,t.fontStyle),this.fontVariant=F(A,Il,t.fontVariant),this.fontWeight=F(A,Hl,t.fontWeight),this.letterSpacing=F(A,Zo,t.letterSpacing),this.lineBreak=F(A,zo,t.lineBreak),this.lineHeight=F(A,qo,t.lineHeight),this.listStyleImage=F(A,jo,t.listStyleImage),this.listStylePosition=F(A,$o,t.listStylePosition),this.listStyleType=F(A,Lr,t.listStyleType),this.marginTop=F(A,Al,t.marginTop),this.marginRight=F(A,el,t.marginRight),this.marginBottom=F(A,tl,t.marginBottom),this.marginLeft=F(A,rl,t.marginLeft),this.opacity=F(A,Fl,t.opacity);var s=F(A,nl,t.overflow);this.overflowX=s[0],this.overflowY=s[s.length>1?1:0],this.overflowWrap=F(A,sl,t.overflowWrap),this.paddingTop=F(A,al,t.paddingTop),this.paddingRight=F(A,il,t.paddingRight),this.paddingBottom=F(A,ol,t.paddingBottom),this.paddingLeft=F(A,ll,t.paddingLeft),this.paintOrder=F(A,_l,t.paintOrder),this.position=F(A,Bl,t.position),this.textAlign=F(A,cl,t.textAlign),this.textDecorationColor=F(A,ml,(r=t.textDecorationColor)!==null&&r!==void 0?r:t.color),this.textDecorationLine=F(A,vl,(n=t.textDecorationLine)!==null&&n!==void 0?n:t.textDecoration),this.textShadow=F(A,xl,t.textShadow),this.textTransform=F(A,dl,t.textTransform),this.transform=F(A,ul,t.transform),this.transformOrigin=F(A,Ql,t.transformOrigin),this.visibility=F(A,Cl,t.visibility),this.webkitTextStrokeColor=F(A,Ol,t.webkitTextStrokeColor),this.webkitTextStrokeWidth=F(A,kl,t.webkitTextStrokeWidth),this.wordBreak=F(A,pl,t.wordBreak),this.zIndex=F(A,Ul,t.zIndex)}return e.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===0},e.prototype.isTransparent=function(){return YA(this.backgroundColor)},e.prototype.isTransformed=function(){return this.transform!==null},e.prototype.isPositioned=function(){return this.position!==0},e.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},e.prototype.isFloating=function(){return this.float!==0},e.prototype.isInlineLevel=function(){return Z(this.display,4)||Z(this.display,33554432)||Z(this.display,268435456)||Z(this.display,536870912)||Z(this.display,67108864)||Z(this.display,134217728)},e}(),Gl=function(){function e(A,t){this.content=F(A,Sl,t.content),this.quotes=F(A,Tl,t.quotes)}return e}(),bn=function(){function e(A,t){this.counterIncrement=F(A,Ll,t.counterIncrement),this.counterReset=F(A,Dl,t.counterReset)}return e}(),F=function(e,A,t){var r=new Fs,n=t!==null&&typeof t<"u"?t.toString():A.initialValue;r.write(n);var s=new ms(r.read());switch(A.type){case 2:var a=s.parseComponentValue();return A.parse(e,G(a)?a.value:A.initialValue);case 0:return A.parse(e,s.parseComponentValue());case 1:return A.parse(e,s.parseComponentValues());case 4:return s.parseComponentValue();case 3:switch(A.format){case"angle":return Rt.parse(e,s.parseComponentValue());case"color":return WA.parse(e,s.parseComponentValue());case"image":return qr.parse(e,s.parseComponentValue());case"length":var o=s.parseComponentValue();return ZA(o)?o:AA;case"length-percentage":var i=s.parseComponentValue();return W(i)?i:AA;case"time":return Ps.parse(e,s.parseComponentValue())}break}},Pl="data-html2canvas-debug",Vl=function(e){var A=e.getAttribute(Pl);switch(A){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},Dr=function(e,A){var t=Vl(e);return t===1||A===t},HA=function(){function e(A,t){if(this.context=A,this.textNodes=[],this.elements=[],this.flags=0,Dr(t,3))debugger;this.styles=new Rl(A,window.getComputedStyle(t,null)),Mr(t)&&(this.styles.animationDuration.some(function(r){return r>0})&&(t.style.animationDuration="0s"),this.styles.transform!==null&&(t.style.transform="none")),this.bounds=Ot(this.context,t),Dr(t,4)&&(this.flags|=16)}return e}(),Nl="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",Sn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Oe=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var wt=0;wt<Sn.length;wt++)Oe[Sn.charCodeAt(wt)]=wt;var Jl=function(e){var A=e.length*.75,t=e.length,r,n=0,s,a,o,i;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var l=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),c=Array.isArray(l)?l:new Uint8Array(l);for(r=0;r<t;r+=4)s=Oe[e.charCodeAt(r)],a=Oe[e.charCodeAt(r+1)],o=Oe[e.charCodeAt(r+2)],i=Oe[e.charCodeAt(r+3)],c[n++]=s<<2|a>>4,c[n++]=(a&15)<<4|o>>2,c[n++]=(o&3)<<6|i&63;return l},Xl=function(e){for(var A=e.length,t=[],r=0;r<A;r+=2)t.push(e[r+1]<<8|e[r]);return t},Wl=function(e){for(var A=e.length,t=[],r=0;r<A;r+=4)t.push(e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]);return t},oe=5,jr=11,nr=2,Yl=jr-oe,Vs=65536>>oe,Zl=1<<oe,sr=Zl-1,zl=1024>>oe,ql=Vs+zl,jl=ql,$l=32,Ac=jl+$l,ec=65536>>jr,tc=1<<Yl,rc=tc-1,Ln=function(e,A,t){return e.slice?e.slice(A,t):new Uint16Array(Array.prototype.slice.call(e,A,t))},nc=function(e,A,t){return e.slice?e.slice(A,t):new Uint32Array(Array.prototype.slice.call(e,A,t))},sc=function(e,A){var t=Jl(e),r=Array.isArray(t)?Wl(t):new Uint32Array(t),n=Array.isArray(t)?Xl(t):new Uint16Array(t),s=24,a=Ln(n,s/2,r[4]/2),o=r[5]===2?Ln(n,(s+r[4])/2):nc(r,Math.ceil((s+r[4])/4));return new ac(r[0],r[1],r[2],r[3],a,o)},ac=function(){function e(A,t,r,n,s,a){this.initialValue=A,this.errorValue=t,this.highStart=r,this.highValueIndex=n,this.index=s,this.data=a}return e.prototype.get=function(A){var t;if(A>=0){if(A<55296||A>56319&&A<=65535)return t=this.index[A>>oe],t=(t<<nr)+(A&sr),this.data[t];if(A<=65535)return t=this.index[Vs+(A-55296>>oe)],t=(t<<nr)+(A&sr),this.data[t];if(A<this.highStart)return t=Ac-ec+(A>>jr),t=this.index[t],t+=A>>oe&rc,t=this.index[t],t=(t<<nr)+(A&sr),this.data[t];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),Dn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ic=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var ft=0;ft<Dn.length;ft++)ic[Dn.charCodeAt(ft)]=ft;var oc=1,ar=2,ir=3,Kn=4,Tn=5,lc=7,Mn=8,or=9,lr=10,_n=11,On=12,kn=13,Rn=14,cr=15,cc=function(e){for(var A=[],t=0,r=e.length;t<r;){var n=e.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var s=e.charCodeAt(t++);(s&64512)===56320?A.push(((n&1023)<<10)+(s&1023)+65536):(A.push(n),t--)}else A.push(n)}return A},Bc=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var t=e.length;if(!t)return"";for(var r=[],n=-1,s="";++n<t;){var a=e[n];a<=65535?r.push(a):(a-=65536,r.push((a>>10)+55296,a%1024+56320)),(n+1===t||r.length>16384)&&(s+=String.fromCharCode.apply(String,r),r.length=0)}return s},xc=sc(Nl),uA="×",Br="÷",dc=function(e){return xc.get(e)},uc=function(e,A,t){var r=t-2,n=A[r],s=A[t-1],a=A[t];if(s===ar&&a===ir)return uA;if(s===ar||s===ir||s===Kn||a===ar||a===ir||a===Kn)return Br;if(s===Mn&&[Mn,or,_n,On].indexOf(a)!==-1||(s===_n||s===or)&&(a===or||a===lr)||(s===On||s===lr)&&a===lr||a===kn||a===Tn||a===lc||s===oc)return uA;if(s===kn&&a===Rn){for(;n===Tn;)n=A[--r];if(n===Rn)return uA}if(s===cr&&a===cr){for(var o=0;n===cr;)o++,n=A[--r];if(o%2===0)return uA}return Br},hc=function(e){var A=cc(e),t=A.length,r=0,n=0,s=A.map(dc);return{next:function(){if(r>=t)return{done:!0,value:null};for(var a=uA;r<t&&(a=uc(A,s,++r))===uA;);if(a!==uA||r===t){var o=Bc.apply(null,A.slice(n,r));return n=r,{value:o,done:!1}}return{done:!0,value:null}}}},gc=function(e){for(var A=hc(e),t=[],r;!(r=A.next()).done;)r.value&&t.push(r.value.slice());return t},wc=function(e){var A=123;if(e.createRange){var t=e.createRange();if(t.getBoundingClientRect){var r=e.createElement("boundtest");r.style.height=A+"px",r.style.display="block",e.body.appendChild(r),t.selectNode(r);var n=t.getBoundingClientRect(),s=Math.round(n.height);if(e.body.removeChild(r),s===A)return!0}}return!1},fc=function(e){var A=e.createElement("boundtest");A.style.width="50px",A.style.display="block",A.style.fontSize="12px",A.style.letterSpacing="0px",A.style.wordSpacing="0px",e.body.appendChild(A);var t=e.createRange();A.innerHTML=typeof"".repeat=="function"?"&#128104;".repeat(10):"";var r=A.firstChild,n=kt(r.data).map(function(i){return X(i)}),s=0,a={},o=n.every(function(i,l){t.setStart(r,s),t.setEnd(r,s+i.length);var c=t.getBoundingClientRect();s+=i.length;var x=c.x>a.x||c.y>a.y;return a=c,l===0?!0:x});return e.body.removeChild(A),o},Qc=function(){return typeof new Image().crossOrigin<"u"},Cc=function(){return typeof new XMLHttpRequest().responseType=="string"},pc=function(e){var A=new Image,t=e.createElement("canvas"),r=t.getContext("2d");if(!r)return!1;A.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{r.drawImage(A,0,0),t.toDataURL()}catch{return!1}return!0},Gn=function(e){return e[0]===0&&e[1]===255&&e[2]===0&&e[3]===255},Uc=function(e){var A=e.createElement("canvas"),t=100;A.width=t,A.height=t;var r=A.getContext("2d");if(!r)return Promise.reject(!1);r.fillStyle="rgb(0, 255, 0)",r.fillRect(0,0,t,t);var n=new Image,s=A.toDataURL();n.src=s;var a=Kr(t,t,0,0,n);return r.fillStyle="red",r.fillRect(0,0,t,t),Pn(a).then(function(o){r.drawImage(o,0,0);var i=r.getImageData(0,0,t,t).data;r.fillStyle="red",r.fillRect(0,0,t,t);var l=e.createElement("div");return l.style.backgroundImage="url("+s+")",l.style.height=t+"px",Gn(i)?Pn(Kr(t,t,0,0,l)):Promise.reject(!1)}).then(function(o){return r.drawImage(o,0,0),Gn(r.getImageData(0,0,t,t).data)}).catch(function(){return!1})},Kr=function(e,A,t,r,n){var s="http://www.w3.org/2000/svg",a=document.createElementNS(s,"svg"),o=document.createElementNS(s,"foreignObject");return a.setAttributeNS(null,"width",e.toString()),a.setAttributeNS(null,"height",A.toString()),o.setAttributeNS(null,"width","100%"),o.setAttributeNS(null,"height","100%"),o.setAttributeNS(null,"x",t.toString()),o.setAttributeNS(null,"y",r.toString()),o.setAttributeNS(null,"externalResourcesRequired","true"),a.appendChild(o),o.appendChild(n),a},Pn=function(e){return new Promise(function(A,t){var r=new Image;r.onload=function(){return A(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},$={get SUPPORT_RANGE_BOUNDS(){var e=wc(document);return Object.defineProperty($,"SUPPORT_RANGE_BOUNDS",{value:e}),e},get SUPPORT_WORD_BREAKING(){var e=$.SUPPORT_RANGE_BOUNDS&&fc(document);return Object.defineProperty($,"SUPPORT_WORD_BREAKING",{value:e}),e},get SUPPORT_SVG_DRAWING(){var e=pc(document);return Object.defineProperty($,"SUPPORT_SVG_DRAWING",{value:e}),e},get SUPPORT_FOREIGNOBJECT_DRAWING(){var e=typeof Array.from=="function"&&typeof window.fetch=="function"?Uc(document):Promise.resolve(!1);return Object.defineProperty($,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:e}),e},get SUPPORT_CORS_IMAGES(){var e=Qc();return Object.defineProperty($,"SUPPORT_CORS_IMAGES",{value:e}),e},get SUPPORT_RESPONSE_TYPE(){var e=Cc();return Object.defineProperty($,"SUPPORT_RESPONSE_TYPE",{value:e}),e},get SUPPORT_CORS_XHR(){var e="withCredentials"in new XMLHttpRequest;return Object.defineProperty($,"SUPPORT_CORS_XHR",{value:e}),e},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var e=!!(typeof Intl<"u"&&Intl.Segmenter);return Object.defineProperty($,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:e}),e}},Ve=function(){function e(A,t){this.text=A,this.bounds=t}return e}(),Fc=function(e,A,t,r){var n=yc(A,t),s=[],a=0;return n.forEach(function(o){if(t.textDecorationLine.length||o.trim().length>0)if($.SUPPORT_RANGE_BOUNDS){var i=Vn(r,a,o.length).getClientRects();if(i.length>1){var l=$r(o),c=0;l.forEach(function(d){s.push(new Ve(d,MA.fromDOMRectList(e,Vn(r,c+a,d.length).getClientRects()))),c+=d.length})}else s.push(new Ve(o,MA.fromDOMRectList(e,i)))}else{var x=r.splitText(o.length);s.push(new Ve(o,mc(e,r))),r=x}else $.SUPPORT_RANGE_BOUNDS||(r=r.splitText(o.length));a+=o.length}),s},mc=function(e,A){var t=A.ownerDocument;if(t){var r=t.createElement("html2canvaswrapper");r.appendChild(A.cloneNode(!0));var n=A.parentNode;if(n){n.replaceChild(r,A);var s=Ot(e,r);return r.firstChild&&n.replaceChild(r.firstChild,r),s}}return MA.EMPTY},Vn=function(e,A,t){var r=e.ownerDocument;if(!r)throw new Error("Node has no owner document");var n=r.createRange();return n.setStart(e,A),n.setEnd(e,A+t),n},$r=function(e){if($.SUPPORT_NATIVE_TEXT_SEGMENTATION){var A=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(A.segment(e)).map(function(t){return t.segment})}return gc(e)},vc=function(e,A){if($.SUPPORT_NATIVE_TEXT_SEGMENTATION){var t=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(t.segment(e)).map(function(r){return r.segment})}return Hc(e,A)},yc=function(e,A){return A.letterSpacing!==0?$r(e):vc(e,A)},Ec=[32,160,4961,65792,65793,4153,4241],Hc=function(e,A){for(var t=Ai(e,{lineBreak:A.lineBreak,wordBreak:A.overflowWrap==="break-word"?"break-word":A.wordBreak}),r=[],n,s=function(){if(n.value){var a=n.value.slice(),o=kt(a),i="";o.forEach(function(l){Ec.indexOf(l)===-1?i+=X(l):(i.length&&r.push(i),r.push(X(l)),i="")}),i.length&&r.push(i)}};!(n=t.next()).done;)s();return r},Ic=function(){function e(A,t,r){this.text=bc(t.data,r.textTransform),this.textBounds=Fc(A,this.text,r,t)}return e}(),bc=function(e,A){switch(A){case 1:return e.toLowerCase();case 3:return e.replace(Sc,Lc);case 2:return e.toUpperCase();default:return e}},Sc=/(^|\s|:|-|\(|\))([a-z])/g,Lc=function(e,A,t){return e.length>0?A+t.toUpperCase():e},Ns=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.src=r.currentSrc||r.src,n.intrinsicWidth=r.naturalWidth,n.intrinsicHeight=r.naturalHeight,n.context.cache.addImage(n.src),n}return A}(HA),Js=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.canvas=r,n.intrinsicWidth=r.width,n.intrinsicHeight=r.height,n}return A}(HA),Xs=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this,s=new XMLSerializer,a=Ot(t,r);return r.setAttribute("width",a.width+"px"),r.setAttribute("height",a.height+"px"),n.svg="data:image/svg+xml,"+encodeURIComponent(s.serializeToString(r)),n.intrinsicWidth=r.width.baseVal.value,n.intrinsicHeight=r.height.baseVal.value,n.context.cache.addImage(n.svg),n}return A}(HA),Ws=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.value=r.value,n}return A}(HA),Tr=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.start=r.start,n.reversed=typeof r.reversed=="boolean"&&r.reversed===!0,n}return A}(HA),Dc=[{type:15,flags:0,unit:"px",number:3}],Kc=[{type:16,flags:0,number:50}],Tc=function(e){return e.width>e.height?new MA(e.left+(e.width-e.height)/2,e.top,e.height,e.height):e.width<e.height?new MA(e.left,e.top+(e.height-e.width)/2,e.width,e.width):e},Mc=function(e){var A=e.type===_c?new Array(e.value.length+1).join("•"):e.value;return A.length===0?e.placeholder||"":A},St="checkbox",Lt="radio",_c="password",Nn=707406591,An=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;switch(n.type=r.type.toLowerCase(),n.checked=r.checked,n.value=Mc(r),(n.type===St||n.type===Lt)&&(n.styles.backgroundColor=3739148031,n.styles.borderTopColor=n.styles.borderRightColor=n.styles.borderBottomColor=n.styles.borderLeftColor=2779096575,n.styles.borderTopWidth=n.styles.borderRightWidth=n.styles.borderBottomWidth=n.styles.borderLeftWidth=1,n.styles.borderTopStyle=n.styles.borderRightStyle=n.styles.borderBottomStyle=n.styles.borderLeftStyle=1,n.styles.backgroundClip=[0],n.styles.backgroundOrigin=[0],n.bounds=Tc(n.bounds)),n.type){case St:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=Dc;break;case Lt:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=Kc;break}return n}return A}(HA),Ys=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this,s=r.options[r.selectedIndex||0];return n.value=s&&s.text||"",n}return A}(HA),Zs=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.value=r.value,n}return A}(HA),zs=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;n.src=r.src,n.width=parseInt(r.width,10)||0,n.height=parseInt(r.height,10)||0,n.backgroundColor=n.styles.backgroundColor;try{if(r.contentWindow&&r.contentWindow.document&&r.contentWindow.document.documentElement){n.tree=js(t,r.contentWindow.document.documentElement);var s=r.contentWindow.document.documentElement?Ge(t,getComputedStyle(r.contentWindow.document.documentElement).backgroundColor):TA.TRANSPARENT,a=r.contentWindow.document.body?Ge(t,getComputedStyle(r.contentWindow.document.body).backgroundColor):TA.TRANSPARENT;n.backgroundColor=YA(s)?YA(a)?n.styles.backgroundColor:a:s}}catch{}return n}return A}(HA),Oc=["OL","UL","MENU"],yt=function(e,A,t,r){for(var n=A.firstChild,s=void 0;n;n=s)if(s=n.nextSibling,$s(n)&&n.data.trim().length>0)t.textNodes.push(new Ic(e,n,t.styles));else if(Fe(n))if(ra(n)&&n.assignedNodes)n.assignedNodes().forEach(function(o){return yt(e,o,t,r)});else{var a=qs(e,n);a.styles.isVisible()&&(kc(n,a,r)?a.flags|=4:Rc(a.styles)&&(a.flags|=2),Oc.indexOf(n.tagName)!==-1&&(a.flags|=8),t.elements.push(a),n.slot,n.shadowRoot?yt(e,n.shadowRoot,a,r):!Dt(n)&&!Aa(n)&&!Kt(n)&&yt(e,n,a,r))}},qs=function(e,A){return _r(A)?new Ns(e,A):ea(A)?new Js(e,A):Aa(A)?new Xs(e,A):Gc(A)?new Ws(e,A):Pc(A)?new Tr(e,A):Vc(A)?new An(e,A):Kt(A)?new Ys(e,A):Dt(A)?new Zs(e,A):ta(A)?new zs(e,A):new HA(e,A)},js=function(e,A){var t=qs(e,A);return t.flags|=4,yt(e,A,t,t),t},kc=function(e,A,t){return A.styles.isPositionedWithZIndex()||A.styles.opacity<1||A.styles.isTransformed()||en(e)&&t.styles.isTransparent()},Rc=function(e){return e.isPositioned()||e.isFloating()},$s=function(e){return e.nodeType===Node.TEXT_NODE},Fe=function(e){return e.nodeType===Node.ELEMENT_NODE},Mr=function(e){return Fe(e)&&typeof e.style<"u"&&!Et(e)},Et=function(e){return typeof e.className=="object"},Gc=function(e){return e.tagName==="LI"},Pc=function(e){return e.tagName==="OL"},Vc=function(e){return e.tagName==="INPUT"},Nc=function(e){return e.tagName==="HTML"},Aa=function(e){return e.tagName==="svg"},en=function(e){return e.tagName==="BODY"},ea=function(e){return e.tagName==="CANVAS"},Jn=function(e){return e.tagName==="VIDEO"},_r=function(e){return e.tagName==="IMG"},ta=function(e){return e.tagName==="IFRAME"},Xn=function(e){return e.tagName==="STYLE"},Jc=function(e){return e.tagName==="SCRIPT"},Dt=function(e){return e.tagName==="TEXTAREA"},Kt=function(e){return e.tagName==="SELECT"},ra=function(e){return e.tagName==="SLOT"},Wn=function(e){return e.tagName.indexOf("-")>0},Xc=function(){function e(){this.counters={}}return e.prototype.getCounterValue=function(A){var t=this.counters[A];return t&&t.length?t[t.length-1]:1},e.prototype.getCounterValues=function(A){var t=this.counters[A];return t||[]},e.prototype.pop=function(A){var t=this;A.forEach(function(r){return t.counters[r].pop()})},e.prototype.parse=function(A){var t=this,r=A.counterIncrement,n=A.counterReset,s=!0;r!==null&&r.forEach(function(o){var i=t.counters[o.counter];i&&o.increment!==0&&(s=!1,i.length||i.push(1),i[Math.max(0,i.length-1)]+=o.increment)});var a=[];return s&&n.forEach(function(o){var i=t.counters[o.counter];a.push(o.counter),i||(i=t.counters[o.counter]=[]),i.push(o.reset)}),a},e}(),Yn={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},Zn={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},Wc={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},Yc={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},ue=function(e,A,t,r,n,s){return e<A||e>t?We(e,n,s.length>0):r.integers.reduce(function(a,o,i){for(;e>=o;)e-=o,a+=r.values[i];return a},"")+s},na=function(e,A,t,r){var n="";do t||e--,n=r(e)+n,e/=A;while(e*A>=A);return n},N=function(e,A,t,r,n){var s=t-A+1;return(e<0?"-":"")+(na(Math.abs(e),s,r,function(a){return X(Math.floor(a%s)+A)})+n)},ee=function(e,A,t){t===void 0&&(t=". ");var r=A.length;return na(Math.abs(e),r,!1,function(n){return A[Math.floor(n%r)]})+t},Ce=1,GA=2,PA=4,ke=8,LA=function(e,A,t,r,n,s){if(e<-9999||e>9999)return We(e,4,n.length>0);var a=Math.abs(e),o=n;if(a===0)return A[0]+o;for(var i=0;a>0&&i<=4;i++){var l=a%10;l===0&&Z(s,Ce)&&o!==""?o=A[l]+o:l>1||l===1&&i===0||l===1&&i===1&&Z(s,GA)||l===1&&i===1&&Z(s,PA)&&e>100||l===1&&i>1&&Z(s,ke)?o=A[l]+(i>0?t[i-1]:"")+o:l===1&&i>0&&(o=t[i-1]+o),a=Math.floor(a/10)}return(e<0?r:"")+o},zn="十百千萬",qn="拾佰仟萬",jn="マイナス",xr="마이너스",We=function(e,A,t){var r=t?". ":"",n=t?"、":"",s=t?", ":"",a=t?" ":"";switch(A){case 0:return"•"+a;case 1:return"◦"+a;case 2:return"◾"+a;case 5:var o=N(e,48,57,!0,r);return o.length<4?"0"+o:o;case 4:return ee(e,"〇一二三四五六七八九",n);case 6:return ue(e,1,3999,Yn,3,r).toLowerCase();case 7:return ue(e,1,3999,Yn,3,r);case 8:return N(e,945,969,!1,r);case 9:return N(e,97,122,!1,r);case 10:return N(e,65,90,!1,r);case 11:return N(e,1632,1641,!0,r);case 12:case 49:return ue(e,1,9999,Zn,3,r);case 35:return ue(e,1,9999,Zn,3,r).toLowerCase();case 13:return N(e,2534,2543,!0,r);case 14:case 30:return N(e,6112,6121,!0,r);case 15:return ee(e,"子丑寅卯辰巳午未申酉戌亥",n);case 16:return ee(e,"甲乙丙丁戊己庚辛壬癸",n);case 17:case 48:return LA(e,"零一二三四五六七八九",zn,"負",n,GA|PA|ke);case 47:return LA(e,"零壹貳參肆伍陸柒捌玖",qn,"負",n,Ce|GA|PA|ke);case 42:return LA(e,"零一二三四五六七八九",zn,"负",n,GA|PA|ke);case 41:return LA(e,"零壹贰叁肆伍陆柒捌玖",qn,"负",n,Ce|GA|PA|ke);case 26:return LA(e,"〇一二三四五六七八九","十百千万",jn,n,0);case 25:return LA(e,"零壱弐参四伍六七八九","拾百千万",jn,n,Ce|GA|PA);case 31:return LA(e,"영일이삼사오육칠팔구","십백천만",xr,s,Ce|GA|PA);case 33:return LA(e,"零一二三四五六七八九","十百千萬",xr,s,0);case 32:return LA(e,"零壹貳參四五六七八九","拾百千",xr,s,Ce|GA|PA);case 18:return N(e,2406,2415,!0,r);case 20:return ue(e,1,19999,Yc,3,r);case 21:return N(e,2790,2799,!0,r);case 22:return N(e,2662,2671,!0,r);case 22:return ue(e,1,10999,Wc,3,r);case 23:return ee(e,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return ee(e,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return N(e,3302,3311,!0,r);case 28:return ee(e,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",n);case 29:return ee(e,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",n);case 34:return N(e,3792,3801,!0,r);case 37:return N(e,6160,6169,!0,r);case 38:return N(e,4160,4169,!0,r);case 39:return N(e,2918,2927,!0,r);case 40:return N(e,1776,1785,!0,r);case 43:return N(e,3046,3055,!0,r);case 44:return N(e,3174,3183,!0,r);case 45:return N(e,3664,3673,!0,r);case 46:return N(e,3872,3881,!0,r);case 3:default:return N(e,48,57,!0,r)}},sa="data-html2canvas-ignore",$n=function(){function e(A,t,r){if(this.context=A,this.options=r,this.scrolledElements=[],this.referenceElement=t,this.counters=new Xc,this.quoteDepth=0,!t.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(t.ownerDocument.documentElement,!1)}return e.prototype.toIFrame=function(A,t){var r=this,n=Zc(A,t);if(!n.contentWindow)return Promise.reject("Unable to find iframe window");var s=A.defaultView.pageXOffset,a=A.defaultView.pageYOffset,o=n.contentWindow,i=o.document,l=jc(n).then(function(){return nA(r,void 0,void 0,function(){var c,x;return tA(this,function(d){switch(d.label){case 0:return this.scrolledElements.forEach(t0),o&&(o.scrollTo(t.left,t.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(o.scrollY!==t.top||o.scrollX!==t.left)&&(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(o.scrollX-t.left,o.scrollY-t.top,0,0))),c=this.options.onclone,x=this.clonedReferenceElement,typeof x>"u"?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:i.fonts&&i.fonts.ready?[4,i.fonts.ready]:[3,2];case 1:d.sent(),d.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,qc(i)]:[3,4];case 3:d.sent(),d.label=4;case 4:return typeof c=="function"?[2,Promise.resolve().then(function(){return c(i,x)}).then(function(){return n})]:[2,n]}})})});return i.open(),i.write(A0(document.doctype)+"<html></html>"),e0(this.referenceElement.ownerDocument,s,a),i.replaceChild(i.adoptNode(this.documentElement),i.documentElement),i.close(),l},e.prototype.createElementClone=function(A){if(Dr(A,2))debugger;if(ea(A))return this.createCanvasClone(A);if(Jn(A))return this.createVideoClone(A);if(Xn(A))return this.createStyleClone(A);var t=A.cloneNode(!1);return _r(t)&&(_r(A)&&A.currentSrc&&A.currentSrc!==A.src&&(t.src=A.currentSrc,t.srcset=""),t.loading==="lazy"&&(t.loading="eager")),Wn(t)?this.createCustomElementClone(t):t},e.prototype.createCustomElementClone=function(A){var t=document.createElement("html2canvascustomelement");return dr(A.style,t),t},e.prototype.createStyleClone=function(A){try{var t=A.sheet;if(t&&t.cssRules){var r=[].slice.call(t.cssRules,0).reduce(function(s,a){return a&&typeof a.cssText=="string"?s+a.cssText:s},""),n=A.cloneNode(!1);return n.textContent=r,n}}catch(s){if(this.context.logger.error("Unable to access cssRules property",s),s.name!=="SecurityError")throw s}return A.cloneNode(!1)},e.prototype.createCanvasClone=function(A){var t;if(this.options.inlineImages&&A.ownerDocument){var r=A.ownerDocument.createElement("img");try{return r.src=A.toDataURL(),r}catch{this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var n=A.cloneNode(!1);try{n.width=A.width,n.height=A.height;var s=A.getContext("2d"),a=n.getContext("2d");if(a)if(!this.options.allowTaint&&s)a.putImageData(s.getImageData(0,0,A.width,A.height),0,0);else{var o=(t=A.getContext("webgl2"))!==null&&t!==void 0?t:A.getContext("webgl");if(o){var i=o.getContextAttributes();(i==null?void 0:i.preserveDrawingBuffer)===!1&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}a.drawImage(A,0,0)}return n}catch{this.context.logger.info("Unable to clone canvas as it is tainted",A)}return n},e.prototype.createVideoClone=function(A){var t=A.ownerDocument.createElement("canvas");t.width=A.offsetWidth,t.height=A.offsetHeight;var r=t.getContext("2d");try{return r&&(r.drawImage(A,0,0,t.width,t.height),this.options.allowTaint||r.getImageData(0,0,t.width,t.height)),t}catch{this.context.logger.info("Unable to clone video as it is tainted",A)}var n=A.ownerDocument.createElement("canvas");return n.width=A.offsetWidth,n.height=A.offsetHeight,n},e.prototype.appendChildNode=function(A,t,r){(!Fe(t)||!Jc(t)&&!t.hasAttribute(sa)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(t)))&&(!this.options.copyStyles||!Fe(t)||!Xn(t))&&A.appendChild(this.cloneNode(t,r))},e.prototype.cloneChildNodes=function(A,t,r){for(var n=this,s=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;s;s=s.nextSibling)if(Fe(s)&&ra(s)&&typeof s.assignedNodes=="function"){var a=s.assignedNodes();a.length&&a.forEach(function(o){return n.appendChildNode(t,o,r)})}else this.appendChildNode(t,s,r)},e.prototype.cloneNode=function(A,t){if($s(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var r=A.ownerDocument.defaultView;if(r&&Fe(A)&&(Mr(A)||Et(A))){var n=this.createElementClone(A);n.style.transitionProperty="none";var s=r.getComputedStyle(A),a=r.getComputedStyle(A,":before"),o=r.getComputedStyle(A,":after");this.referenceElement===A&&Mr(n)&&(this.clonedReferenceElement=n),en(n)&&s0(n);var i=this.counters.parse(new bn(this.context,s)),l=this.resolvePseudoContent(A,n,a,Ne.BEFORE);Wn(A)&&(t=!0),Jn(A)||this.cloneChildNodes(A,n,t),l&&n.insertBefore(l,n.firstChild);var c=this.resolvePseudoContent(A,n,o,Ne.AFTER);return c&&n.appendChild(c),this.counters.pop(i),(s&&(this.options.copyStyles||Et(A))&&!ta(A)||t)&&dr(s,n),(A.scrollTop!==0||A.scrollLeft!==0)&&this.scrolledElements.push([n,A.scrollLeft,A.scrollTop]),(Dt(A)||Kt(A))&&(Dt(n)||Kt(n))&&(n.value=A.value),n}return A.cloneNode(!1)},e.prototype.resolvePseudoContent=function(A,t,r,n){var s=this;if(r){var a=r.content,o=t.ownerDocument;if(!(!o||!a||a==="none"||a==="-moz-alt-content"||r.display==="none")){this.counters.parse(new bn(this.context,r));var i=new Gl(this.context,r),l=o.createElement("html2canvaspseudoelement");dr(r,l),i.content.forEach(function(x){if(x.type===0)l.appendChild(o.createTextNode(x.value));else if(x.type===22){var d=o.createElement("img");d.src=x.value,d.style.opacity="1",l.appendChild(d)}else if(x.type===18){if(x.name==="attr"){var f=x.values.filter(G);f.length&&l.appendChild(o.createTextNode(A.getAttribute(f[0].value)||""))}else if(x.name==="counter"){var u=x.values.filter(ve),h=u[0],v=u[1];if(h&&G(h)){var w=s.counters.getCounterValue(h.value),Q=v&&G(v)?Lr.parse(s.context,v.value):3;l.appendChild(o.createTextNode(We(w,Q,!1)))}}else if(x.name==="counters"){var y=x.values.filter(ve),h=y[0],p=y[1],v=y[2];if(h&&G(h)){var C=s.counters.getCounterValues(h.value),g=v&&G(v)?Lr.parse(s.context,v.value):3,b=p&&p.type===0?p.value:"",L=C.map(function(T){return We(T,g,!1)}).join(b);l.appendChild(o.createTextNode(L))}}}else if(x.type===20)switch(x.value){case"open-quote":l.appendChild(o.createTextNode(In(i.quotes,s.quoteDepth++,!0)));break;case"close-quote":l.appendChild(o.createTextNode(In(i.quotes,--s.quoteDepth,!1)));break;default:l.appendChild(o.createTextNode(x.value))}}),l.className=Or+" "+kr;var c=n===Ne.BEFORE?" "+Or:" "+kr;return Et(t)?t.className.baseValue+=c:t.className+=c,l}}},e.destroy=function(A){return A.parentNode?(A.parentNode.removeChild(A),!0):!1},e}(),Ne;(function(e){e[e.BEFORE=0]="BEFORE",e[e.AFTER=1]="AFTER"})(Ne||(Ne={}));var Zc=function(e,A){var t=e.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=A.width.toString(),t.height=A.height.toString(),t.scrolling="no",t.setAttribute(sa,"true"),e.body.appendChild(t),t},zc=function(e){return new Promise(function(A){if(e.complete){A();return}if(!e.src){A();return}e.onload=A,e.onerror=A})},qc=function(e){return Promise.all([].slice.call(e.images,0).map(zc))},jc=function(e){return new Promise(function(A,t){var r=e.contentWindow;if(!r)return t("No window assigned for iframe");var n=r.document;r.onload=e.onload=function(){r.onload=e.onload=null;var s=setInterval(function(){n.body.childNodes.length>0&&n.readyState==="complete"&&(clearInterval(s),A(e))},50)}})},$c=["all","d","content"],dr=function(e,A){for(var t=e.length-1;t>=0;t--){var r=e.item(t);$c.indexOf(r)===-1&&A.style.setProperty(r,e.getPropertyValue(r))}return A},A0=function(e){var A="";return e&&(A+="<!DOCTYPE ",e.name&&(A+=e.name),e.internalSubset&&(A+=e.internalSubset),e.publicId&&(A+='"'+e.publicId+'"'),e.systemId&&(A+='"'+e.systemId+'"'),A+=">"),A},e0=function(e,A,t){e&&e.defaultView&&(A!==e.defaultView.pageXOffset||t!==e.defaultView.pageYOffset)&&e.defaultView.scrollTo(A,t)},t0=function(e){var A=e[0],t=e[1],r=e[2];A.scrollLeft=t,A.scrollTop=r},r0=":before",n0=":after",Or="___html2canvas___pseudoelement_before",kr="___html2canvas___pseudoelement_after",As=`{
    content: "" !important;
    display: none !important;
}`,s0=function(e){a0(e,"."+Or+r0+As+`
         .`+kr+n0+As)},a0=function(e,A){var t=e.ownerDocument;if(t){var r=t.createElement("style");r.textContent=A,e.appendChild(r)}},aa=function(){function e(){}return e.getOrigin=function(A){var t=e._link;return t?(t.href=A,t.href=t.href,t.protocol+t.hostname+t.port):"about:blank"},e.isSameOrigin=function(A){return e.getOrigin(A)===e._origin},e.setContext=function(A){e._link=A.document.createElement("a"),e._origin=e.getOrigin(A.location.href)},e._origin="about:blank",e}(),i0=function(){function e(A,t){this.context=A,this._options=t,this._cache={}}return e.prototype.addImage=function(A){var t=Promise.resolve();return this.has(A)||(hr(A)||B0(A))&&(this._cache[A]=this.loadImage(A)).catch(function(){}),t},e.prototype.match=function(A){return this._cache[A]},e.prototype.loadImage=function(A){return nA(this,void 0,void 0,function(){var t,r,n,s,a=this;return tA(this,function(o){switch(o.label){case 0:return t=aa.isSameOrigin(A),r=!ur(A)&&this._options.useCORS===!0&&$.SUPPORT_CORS_IMAGES&&!t,n=!ur(A)&&!t&&!hr(A)&&typeof this._options.proxy=="string"&&$.SUPPORT_CORS_XHR&&!r,!t&&this._options.allowTaint===!1&&!ur(A)&&!hr(A)&&!n&&!r?[2]:(s=A,n?[4,this.proxy(s)]:[3,2]);case 1:s=o.sent(),o.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise(function(i,l){var c=new Image;c.onload=function(){return i(c)},c.onerror=l,(x0(s)||r)&&(c.crossOrigin="anonymous"),c.src=s,c.complete===!0&&setTimeout(function(){return i(c)},500),a._options.imageTimeout>0&&setTimeout(function(){return l("Timed out ("+a._options.imageTimeout+"ms) loading image")},a._options.imageTimeout)})];case 3:return[2,o.sent()]}})})},e.prototype.has=function(A){return typeof this._cache[A]<"u"},e.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},e.prototype.proxy=function(A){var t=this,r=this._options.proxy;if(!r)throw new Error("No proxy defined");var n=A.substring(0,256);return new Promise(function(s,a){var o=$.SUPPORT_RESPONSE_TYPE?"blob":"text",i=new XMLHttpRequest;i.onload=function(){if(i.status===200)if(o==="text")s(i.response);else{var x=new FileReader;x.addEventListener("load",function(){return s(x.result)},!1),x.addEventListener("error",function(d){return a(d)},!1),x.readAsDataURL(i.response)}else a("Failed to proxy resource "+n+" with status code "+i.status)},i.onerror=a;var l=r.indexOf("?")>-1?"&":"?";if(i.open("GET",""+r+l+"url="+encodeURIComponent(A)+"&responseType="+o),o!=="text"&&i instanceof XMLHttpRequest&&(i.responseType=o),t._options.imageTimeout){var c=t._options.imageTimeout;i.timeout=c,i.ontimeout=function(){return a("Timed out ("+c+"ms) proxying "+n)}}i.send()})},e}(),o0=/^data:image\/svg\+xml/i,l0=/^data:image\/.*;base64,/i,c0=/^data:image\/.*/i,B0=function(e){return $.SUPPORT_SVG_DRAWING||!d0(e)},ur=function(e){return c0.test(e)},x0=function(e){return l0.test(e)},hr=function(e){return e.substr(0,4)==="blob"},d0=function(e){return e.substr(-3).toLowerCase()==="svg"||o0.test(e)},U=function(){function e(A,t){this.type=0,this.x=A,this.y=t}return e.prototype.add=function(A,t){return new e(this.x+A,this.y+t)},e}(),he=function(e,A,t){return new U(e.x+(A.x-e.x)*t,e.y+(A.y-e.y)*t)},Qt=function(){function e(A,t,r,n){this.type=1,this.start=A,this.startControl=t,this.endControl=r,this.end=n}return e.prototype.subdivide=function(A,t){var r=he(this.start,this.startControl,A),n=he(this.startControl,this.endControl,A),s=he(this.endControl,this.end,A),a=he(r,n,A),o=he(n,s,A),i=he(a,o,A);return t?new e(this.start,r,a,i):new e(i,o,s,this.end)},e.prototype.add=function(A,t){return new e(this.start.add(A,t),this.startControl.add(A,t),this.endControl.add(A,t),this.end.add(A,t))},e.prototype.reverse=function(){return new e(this.end,this.endControl,this.startControl,this.start)},e}(),hA=function(e){return e.type===1},u0=function(){function e(A){var t=A.styles,r=A.bounds,n=_e(t.borderTopLeftRadius,r.width,r.height),s=n[0],a=n[1],o=_e(t.borderTopRightRadius,r.width,r.height),i=o[0],l=o[1],c=_e(t.borderBottomRightRadius,r.width,r.height),x=c[0],d=c[1],f=_e(t.borderBottomLeftRadius,r.width,r.height),u=f[0],h=f[1],v=[];v.push((s+i)/r.width),v.push((u+x)/r.width),v.push((a+h)/r.height),v.push((l+d)/r.height);var w=Math.max.apply(Math,v);w>1&&(s/=w,a/=w,i/=w,l/=w,x/=w,d/=w,u/=w,h/=w);var Q=r.width-i,y=r.height-d,p=r.width-x,C=r.height-h,g=t.borderTopWidth,b=t.borderRightWidth,L=t.borderBottomWidth,E=t.borderLeftWidth,k=P(t.paddingTop,A.bounds.width),T=P(t.paddingRight,A.bounds.width),q=P(t.paddingBottom,A.bounds.width),R=P(t.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=s>0||a>0?V(r.left+E/3,r.top+g/3,s-E/3,a-g/3,O.TOP_LEFT):new U(r.left+E/3,r.top+g/3),this.topRightBorderDoubleOuterBox=s>0||a>0?V(r.left+Q,r.top+g/3,i-b/3,l-g/3,O.TOP_RIGHT):new U(r.left+r.width-b/3,r.top+g/3),this.bottomRightBorderDoubleOuterBox=x>0||d>0?V(r.left+p,r.top+y,x-b/3,d-L/3,O.BOTTOM_RIGHT):new U(r.left+r.width-b/3,r.top+r.height-L/3),this.bottomLeftBorderDoubleOuterBox=u>0||h>0?V(r.left+E/3,r.top+C,u-E/3,h-L/3,O.BOTTOM_LEFT):new U(r.left+E/3,r.top+r.height-L/3),this.topLeftBorderDoubleInnerBox=s>0||a>0?V(r.left+E*2/3,r.top+g*2/3,s-E*2/3,a-g*2/3,O.TOP_LEFT):new U(r.left+E*2/3,r.top+g*2/3),this.topRightBorderDoubleInnerBox=s>0||a>0?V(r.left+Q,r.top+g*2/3,i-b*2/3,l-g*2/3,O.TOP_RIGHT):new U(r.left+r.width-b*2/3,r.top+g*2/3),this.bottomRightBorderDoubleInnerBox=x>0||d>0?V(r.left+p,r.top+y,x-b*2/3,d-L*2/3,O.BOTTOM_RIGHT):new U(r.left+r.width-b*2/3,r.top+r.height-L*2/3),this.bottomLeftBorderDoubleInnerBox=u>0||h>0?V(r.left+E*2/3,r.top+C,u-E*2/3,h-L*2/3,O.BOTTOM_LEFT):new U(r.left+E*2/3,r.top+r.height-L*2/3),this.topLeftBorderStroke=s>0||a>0?V(r.left+E/2,r.top+g/2,s-E/2,a-g/2,O.TOP_LEFT):new U(r.left+E/2,r.top+g/2),this.topRightBorderStroke=s>0||a>0?V(r.left+Q,r.top+g/2,i-b/2,l-g/2,O.TOP_RIGHT):new U(r.left+r.width-b/2,r.top+g/2),this.bottomRightBorderStroke=x>0||d>0?V(r.left+p,r.top+y,x-b/2,d-L/2,O.BOTTOM_RIGHT):new U(r.left+r.width-b/2,r.top+r.height-L/2),this.bottomLeftBorderStroke=u>0||h>0?V(r.left+E/2,r.top+C,u-E/2,h-L/2,O.BOTTOM_LEFT):new U(r.left+E/2,r.top+r.height-L/2),this.topLeftBorderBox=s>0||a>0?V(r.left,r.top,s,a,O.TOP_LEFT):new U(r.left,r.top),this.topRightBorderBox=i>0||l>0?V(r.left+Q,r.top,i,l,O.TOP_RIGHT):new U(r.left+r.width,r.top),this.bottomRightBorderBox=x>0||d>0?V(r.left+p,r.top+y,x,d,O.BOTTOM_RIGHT):new U(r.left+r.width,r.top+r.height),this.bottomLeftBorderBox=u>0||h>0?V(r.left,r.top+C,u,h,O.BOTTOM_LEFT):new U(r.left,r.top+r.height),this.topLeftPaddingBox=s>0||a>0?V(r.left+E,r.top+g,Math.max(0,s-E),Math.max(0,a-g),O.TOP_LEFT):new U(r.left+E,r.top+g),this.topRightPaddingBox=i>0||l>0?V(r.left+Math.min(Q,r.width-b),r.top+g,Q>r.width+b?0:Math.max(0,i-b),Math.max(0,l-g),O.TOP_RIGHT):new U(r.left+r.width-b,r.top+g),this.bottomRightPaddingBox=x>0||d>0?V(r.left+Math.min(p,r.width-E),r.top+Math.min(y,r.height-L),Math.max(0,x-b),Math.max(0,d-L),O.BOTTOM_RIGHT):new U(r.left+r.width-b,r.top+r.height-L),this.bottomLeftPaddingBox=u>0||h>0?V(r.left+E,r.top+Math.min(C,r.height-L),Math.max(0,u-E),Math.max(0,h-L),O.BOTTOM_LEFT):new U(r.left+E,r.top+r.height-L),this.topLeftContentBox=s>0||a>0?V(r.left+E+R,r.top+g+k,Math.max(0,s-(E+R)),Math.max(0,a-(g+k)),O.TOP_LEFT):new U(r.left+E+R,r.top+g+k),this.topRightContentBox=i>0||l>0?V(r.left+Math.min(Q,r.width+E+R),r.top+g+k,Q>r.width+E+R?0:i-E+R,l-(g+k),O.TOP_RIGHT):new U(r.left+r.width-(b+T),r.top+g+k),this.bottomRightContentBox=x>0||d>0?V(r.left+Math.min(p,r.width-(E+R)),r.top+Math.min(y,r.height+g+k),Math.max(0,x-(b+T)),d-(L+q),O.BOTTOM_RIGHT):new U(r.left+r.width-(b+T),r.top+r.height-(L+q)),this.bottomLeftContentBox=u>0||h>0?V(r.left+E+R,r.top+C,Math.max(0,u-(E+R)),h-(L+q),O.BOTTOM_LEFT):new U(r.left+E+R,r.top+r.height-(L+q))}return e}(),O;(function(e){e[e.TOP_LEFT=0]="TOP_LEFT",e[e.TOP_RIGHT=1]="TOP_RIGHT",e[e.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",e[e.BOTTOM_LEFT=3]="BOTTOM_LEFT"})(O||(O={}));var V=function(e,A,t,r,n){var s=4*((Math.sqrt(2)-1)/3),a=t*s,o=r*s,i=e+t,l=A+r;switch(n){case O.TOP_LEFT:return new Qt(new U(e,l),new U(e,l-o),new U(i-a,A),new U(i,A));case O.TOP_RIGHT:return new Qt(new U(e,A),new U(e+a,A),new U(i,l-o),new U(i,l));case O.BOTTOM_RIGHT:return new Qt(new U(i,A),new U(i,A+o),new U(e+a,l),new U(e,l));case O.BOTTOM_LEFT:default:return new Qt(new U(i,l),new U(i-a,l),new U(e,A+o),new U(e,A))}},Tt=function(e){return[e.topLeftBorderBox,e.topRightBorderBox,e.bottomRightBorderBox,e.bottomLeftBorderBox]},h0=function(e){return[e.topLeftContentBox,e.topRightContentBox,e.bottomRightContentBox,e.bottomLeftContentBox]},Mt=function(e){return[e.topLeftPaddingBox,e.topRightPaddingBox,e.bottomRightPaddingBox,e.bottomLeftPaddingBox]},g0=function(){function e(A,t,r){this.offsetX=A,this.offsetY=t,this.matrix=r,this.type=0,this.target=6}return e}(),Ct=function(){function e(A,t){this.path=A,this.target=t,this.type=1}return e}(),w0=function(){function e(A){this.opacity=A,this.type=2,this.target=6}return e}(),f0=function(e){return e.type===0},ia=function(e){return e.type===1},Q0=function(e){return e.type===2},es=function(e,A){return e.length===A.length?e.some(function(t,r){return t===A[r]}):!1},C0=function(e,A,t,r,n){return e.map(function(s,a){switch(a){case 0:return s.add(A,t);case 1:return s.add(A+r,t);case 2:return s.add(A+r,t+n);case 3:return s.add(A,t+n)}return s})},oa=function(){function e(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return e}(),la=function(){function e(A,t){if(this.container=A,this.parent=t,this.effects=[],this.curves=new u0(this.container),this.container.styles.opacity<1&&this.effects.push(new w0(this.container.styles.opacity)),this.container.styles.transform!==null){var r=this.container.bounds.left+this.container.styles.transformOrigin[0].number,n=this.container.bounds.top+this.container.styles.transformOrigin[1].number,s=this.container.styles.transform;this.effects.push(new g0(r,n,s))}if(this.container.styles.overflowX!==0){var a=Tt(this.curves),o=Mt(this.curves);es(a,o)?this.effects.push(new Ct(a,6)):(this.effects.push(new Ct(a,2)),this.effects.push(new Ct(o,4)))}}return e.prototype.getEffects=function(A){for(var t=[2,3].indexOf(this.container.styles.position)===-1,r=this.parent,n=this.effects.slice(0);r;){var s=r.effects.filter(function(i){return!ia(i)});if(t||r.container.styles.position!==0||!r.parent){if(n.unshift.apply(n,s),t=[2,3].indexOf(r.container.styles.position)===-1,r.container.styles.overflowX!==0){var a=Tt(r.curves),o=Mt(r.curves);es(a,o)||n.unshift(new Ct(o,6))}}else n.unshift.apply(n,s);r=r.parent}return n.filter(function(i){return Z(i.target,A)})},e}(),Rr=function(e,A,t,r){e.container.elements.forEach(function(n){var s=Z(n.flags,4),a=Z(n.flags,2),o=new la(n,e);Z(n.styles.display,2048)&&r.push(o);var i=Z(n.flags,8)?[]:r;if(s||a){var l=s||n.styles.isPositioned()?t:A,c=new oa(o);if(n.styles.isPositioned()||n.styles.opacity<1||n.styles.isTransformed()){var x=n.styles.zIndex.order;if(x<0){var d=0;l.negativeZIndex.some(function(u,h){return x>u.element.container.styles.zIndex.order?(d=h,!1):d>0}),l.negativeZIndex.splice(d,0,c)}else if(x>0){var f=0;l.positiveZIndex.some(function(u,h){return x>=u.element.container.styles.zIndex.order?(f=h+1,!1):f>0}),l.positiveZIndex.splice(f,0,c)}else l.zeroOrAutoZIndexOrTransformedOrOpacity.push(c)}else n.styles.isFloating()?l.nonPositionedFloats.push(c):l.nonPositionedInlineLevel.push(c);Rr(o,c,s?c:t,i)}else n.styles.isInlineLevel()?A.inlineLevel.push(o):A.nonInlineLevel.push(o),Rr(o,A,t,i);Z(n.flags,8)&&ca(n,i)})},ca=function(e,A){for(var t=e instanceof Tr?e.start:1,r=e instanceof Tr?e.reversed:!1,n=0;n<A.length;n++){var s=A[n];s.container instanceof Ws&&typeof s.container.value=="number"&&s.container.value!==0&&(t=s.container.value),s.listValue=We(t,s.container.styles.listStyleType,!0),t+=r?-1:1}},p0=function(e){var A=new la(e,null),t=new oa(A),r=[];return Rr(A,t,t,r),ca(A.container,r),t},ts=function(e,A){switch(A){case 0:return wA(e.topLeftBorderBox,e.topLeftPaddingBox,e.topRightBorderBox,e.topRightPaddingBox);case 1:return wA(e.topRightBorderBox,e.topRightPaddingBox,e.bottomRightBorderBox,e.bottomRightPaddingBox);case 2:return wA(e.bottomRightBorderBox,e.bottomRightPaddingBox,e.bottomLeftBorderBox,e.bottomLeftPaddingBox);case 3:default:return wA(e.bottomLeftBorderBox,e.bottomLeftPaddingBox,e.topLeftBorderBox,e.topLeftPaddingBox)}},U0=function(e,A){switch(A){case 0:return wA(e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox,e.topRightBorderBox,e.topRightBorderDoubleOuterBox);case 1:return wA(e.topRightBorderBox,e.topRightBorderDoubleOuterBox,e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox);case 2:return wA(e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox,e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox);case 3:default:return wA(e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox,e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox)}},F0=function(e,A){switch(A){case 0:return wA(e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox,e.topRightBorderDoubleInnerBox,e.topRightPaddingBox);case 1:return wA(e.topRightBorderDoubleInnerBox,e.topRightPaddingBox,e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox);case 2:return wA(e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox,e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox);case 3:default:return wA(e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox,e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox)}},m0=function(e,A){switch(A){case 0:return pt(e.topLeftBorderStroke,e.topRightBorderStroke);case 1:return pt(e.topRightBorderStroke,e.bottomRightBorderStroke);case 2:return pt(e.bottomRightBorderStroke,e.bottomLeftBorderStroke);case 3:default:return pt(e.bottomLeftBorderStroke,e.topLeftBorderStroke)}},pt=function(e,A){var t=[];return hA(e)?t.push(e.subdivide(.5,!1)):t.push(e),hA(A)?t.push(A.subdivide(.5,!0)):t.push(A),t},wA=function(e,A,t,r){var n=[];return hA(e)?n.push(e.subdivide(.5,!1)):n.push(e),hA(t)?n.push(t.subdivide(.5,!0)):n.push(t),hA(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),hA(A)?n.push(A.subdivide(.5,!1).reverse()):n.push(A),n},Ba=function(e){var A=e.bounds,t=e.styles;return A.add(t.borderLeftWidth,t.borderTopWidth,-(t.borderRightWidth+t.borderLeftWidth),-(t.borderTopWidth+t.borderBottomWidth))},_t=function(e){var A=e.styles,t=e.bounds,r=P(A.paddingLeft,t.width),n=P(A.paddingRight,t.width),s=P(A.paddingTop,t.width),a=P(A.paddingBottom,t.width);return t.add(r+A.borderLeftWidth,s+A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth+r+n),-(A.borderTopWidth+A.borderBottomWidth+s+a))},v0=function(e,A){return e===0?A.bounds:e===2?_t(A):Ba(A)},y0=function(e,A){return e===0?A.bounds:e===2?_t(A):Ba(A)},gr=function(e,A,t){var r=v0(pe(e.styles.backgroundOrigin,A),e),n=y0(pe(e.styles.backgroundClip,A),e),s=E0(pe(e.styles.backgroundSize,A),t,r),a=s[0],o=s[1],i=_e(pe(e.styles.backgroundPosition,A),r.width-a,r.height-o),l=H0(pe(e.styles.backgroundRepeat,A),i,s,r,n),c=Math.round(r.left+i[0]),x=Math.round(r.top+i[1]);return[l,c,x,a,o]},ge=function(e){return G(e)&&e.value===me.AUTO},Ut=function(e){return typeof e=="number"},E0=function(e,A,t){var r=A[0],n=A[1],s=A[2],a=e[0],o=e[1];if(!a)return[0,0];if(W(a)&&o&&W(o))return[P(a,t.width),P(o,t.height)];var i=Ut(s);if(G(a)&&(a.value===me.CONTAIN||a.value===me.COVER)){if(Ut(s)){var l=t.width/t.height;return l<s!=(a.value===me.COVER)?[t.width,t.width/s]:[t.height*s,t.height]}return[t.width,t.height]}var c=Ut(r),x=Ut(n),d=c||x;if(ge(a)&&(!o||ge(o))){if(c&&x)return[r,n];if(!i&&!d)return[t.width,t.height];if(d&&i){var f=c?r:n*s,u=x?n:r/s;return[f,u]}var h=c?r:t.width,v=x?n:t.height;return[h,v]}if(i){var w=0,Q=0;return W(a)?w=P(a,t.width):W(o)&&(Q=P(o,t.height)),ge(a)?w=Q*s:(!o||ge(o))&&(Q=w/s),[w,Q]}var y=null,p=null;if(W(a)?y=P(a,t.width):o&&W(o)&&(p=P(o,t.height)),y!==null&&(!o||ge(o))&&(p=c&&x?y/r*n:t.height),p!==null&&ge(a)&&(y=c&&x?p/n*r:t.width),y!==null&&p!==null)return[y,p];throw new Error("Unable to calculate background-size for element")},pe=function(e,A){var t=e[A];return typeof t>"u"?e[0]:t},H0=function(e,A,t,r,n){var s=A[0],a=A[1],o=t[0],i=t[1];switch(e){case 2:return[new U(Math.round(r.left),Math.round(r.top+a)),new U(Math.round(r.left+r.width),Math.round(r.top+a)),new U(Math.round(r.left+r.width),Math.round(i+r.top+a)),new U(Math.round(r.left),Math.round(i+r.top+a))];case 3:return[new U(Math.round(r.left+s),Math.round(r.top)),new U(Math.round(r.left+s+o),Math.round(r.top)),new U(Math.round(r.left+s+o),Math.round(r.height+r.top)),new U(Math.round(r.left+s),Math.round(r.height+r.top))];case 1:return[new U(Math.round(r.left+s),Math.round(r.top+a)),new U(Math.round(r.left+s+o),Math.round(r.top+a)),new U(Math.round(r.left+s+o),Math.round(r.top+a+i)),new U(Math.round(r.left+s),Math.round(r.top+a+i))];default:return[new U(Math.round(n.left),Math.round(n.top)),new U(Math.round(n.left+n.width),Math.round(n.top)),new U(Math.round(n.left+n.width),Math.round(n.height+n.top)),new U(Math.round(n.left),Math.round(n.height+n.top))]}},I0="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",rs="Hidden Text",b0=function(){function e(A){this._data={},this._document=A}return e.prototype.parseMetrics=function(A,t){var r=this._document.createElement("div"),n=this._document.createElement("img"),s=this._document.createElement("span"),a=this._document.body;r.style.visibility="hidden",r.style.fontFamily=A,r.style.fontSize=t,r.style.margin="0",r.style.padding="0",r.style.whiteSpace="nowrap",a.appendChild(r),n.src=I0,n.width=1,n.height=1,n.style.margin="0",n.style.padding="0",n.style.verticalAlign="baseline",s.style.fontFamily=A,s.style.fontSize=t,s.style.margin="0",s.style.padding="0",s.appendChild(this._document.createTextNode(rs)),r.appendChild(s),r.appendChild(n);var o=n.offsetTop-s.offsetTop+2;r.removeChild(s),r.appendChild(this._document.createTextNode(rs)),r.style.lineHeight="normal",n.style.verticalAlign="super";var i=n.offsetTop-r.offsetTop+2;return a.removeChild(r),{baseline:o,middle:i}},e.prototype.getMetrics=function(A,t){var r=A+" "+t;return typeof this._data[r]>"u"&&(this._data[r]=this.parseMetrics(A,t)),this._data[r]},e}(),xa=function(){function e(A,t){this.context=A,this.options=t}return e}(),S0=1e4,L0=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n._activeEffects=[],n.canvas=r.canvas?r.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),r.canvas||(n.canvas.width=Math.floor(r.width*r.scale),n.canvas.height=Math.floor(r.height*r.scale),n.canvas.style.width=r.width+"px",n.canvas.style.height=r.height+"px"),n.fontMetrics=new b0(document),n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-r.x,-r.y),n.ctx.textBaseline="bottom",n._activeEffects=[],n.context.logger.debug("Canvas renderer initialized ("+r.width+"x"+r.height+") with scale "+r.scale),n}return A.prototype.applyEffects=function(t){for(var r=this;this._activeEffects.length;)this.popEffect();t.forEach(function(n){return r.applyEffect(n)})},A.prototype.applyEffect=function(t){this.ctx.save(),Q0(t)&&(this.ctx.globalAlpha=t.opacity),f0(t)&&(this.ctx.translate(t.offsetX,t.offsetY),this.ctx.transform(t.matrix[0],t.matrix[1],t.matrix[2],t.matrix[3],t.matrix[4],t.matrix[5]),this.ctx.translate(-t.offsetX,-t.offsetY)),ia(t)&&(this.path(t.path),this.ctx.clip()),this._activeEffects.push(t)},A.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},A.prototype.renderStack=function(t){return nA(this,void 0,void 0,function(){var r;return tA(this,function(n){switch(n.label){case 0:return r=t.element.container.styles,r.isVisible()?[4,this.renderStackContent(t)]:[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}})})},A.prototype.renderNode=function(t){return nA(this,void 0,void 0,function(){return tA(this,function(r){switch(r.label){case 0:if(Z(t.container.flags,16))debugger;return t.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(t)]:[3,3];case 1:return r.sent(),[4,this.renderNodeContent(t)];case 2:r.sent(),r.label=3;case 3:return[2]}})})},A.prototype.renderTextWithLetterSpacing=function(t,r,n){var s=this;if(r===0)this.ctx.fillText(t.text,t.bounds.left,t.bounds.top+n);else{var a=$r(t.text);a.reduce(function(o,i){return s.ctx.fillText(i,o,t.bounds.top+n),o+s.ctx.measureText(i).width},t.bounds.left)}},A.prototype.createFontStyle=function(t){var r=t.fontVariant.filter(function(a){return a==="normal"||a==="small-caps"}).join(""),n=_0(t.fontFamily).join(", "),s=je(t.fontSize)?""+t.fontSize.number+t.fontSize.unit:t.fontSize.number+"px";return[[t.fontStyle,r,t.fontWeight,s,n].join(" "),n,s]},A.prototype.renderTextNode=function(t,r){return nA(this,void 0,void 0,function(){var n,s,a,o,i,l,c,x,d=this;return tA(this,function(f){return n=this.createFontStyle(r),s=n[0],a=n[1],o=n[2],this.ctx.font=s,this.ctx.direction=r.direction===1?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",i=this.fontMetrics.getMetrics(a,o),l=i.baseline,c=i.middle,x=r.paintOrder,t.textBounds.forEach(function(u){x.forEach(function(h){switch(h){case 0:d.ctx.fillStyle=z(r.color),d.renderTextWithLetterSpacing(u,r.letterSpacing,l);var v=r.textShadow;v.length&&u.text.trim().length&&(v.slice(0).reverse().forEach(function(w){d.ctx.shadowColor=z(w.color),d.ctx.shadowOffsetX=w.offsetX.number*d.options.scale,d.ctx.shadowOffsetY=w.offsetY.number*d.options.scale,d.ctx.shadowBlur=w.blur.number,d.renderTextWithLetterSpacing(u,r.letterSpacing,l)}),d.ctx.shadowColor="",d.ctx.shadowOffsetX=0,d.ctx.shadowOffsetY=0,d.ctx.shadowBlur=0),r.textDecorationLine.length&&(d.ctx.fillStyle=z(r.textDecorationColor||r.color),r.textDecorationLine.forEach(function(w){switch(w){case 1:d.ctx.fillRect(u.bounds.left,Math.round(u.bounds.top+l),u.bounds.width,1);break;case 2:d.ctx.fillRect(u.bounds.left,Math.round(u.bounds.top),u.bounds.width,1);break;case 3:d.ctx.fillRect(u.bounds.left,Math.ceil(u.bounds.top+c),u.bounds.width,1);break}}));break;case 1:r.webkitTextStrokeWidth&&u.text.trim().length&&(d.ctx.strokeStyle=z(r.webkitTextStrokeColor),d.ctx.lineWidth=r.webkitTextStrokeWidth,d.ctx.lineJoin=window.chrome?"miter":"round",d.ctx.strokeText(u.text,u.bounds.left,u.bounds.top+l)),d.ctx.strokeStyle="",d.ctx.lineWidth=0,d.ctx.lineJoin="miter";break}})}),[2]})})},A.prototype.renderReplacedElement=function(t,r,n){if(n&&t.intrinsicWidth>0&&t.intrinsicHeight>0){var s=_t(t),a=Mt(r);this.path(a),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(n,0,0,t.intrinsicWidth,t.intrinsicHeight,s.left,s.top,s.width,s.height),this.ctx.restore()}},A.prototype.renderNodeContent=function(t){return nA(this,void 0,void 0,function(){var r,n,s,a,o,i,Q,Q,l,c,x,d,p,f,u,C,h,v,w,Q,y,p,C;return tA(this,function(g){switch(g.label){case 0:this.applyEffects(t.getEffects(4)),r=t.container,n=t.curves,s=r.styles,a=0,o=r.textNodes,g.label=1;case 1:return a<o.length?(i=o[a],[4,this.renderTextNode(i,s)]):[3,4];case 2:g.sent(),g.label=3;case 3:return a++,[3,1];case 4:if(!(r instanceof Ns))return[3,8];g.label=5;case 5:return g.trys.push([5,7,,8]),[4,this.context.cache.match(r.src)];case 6:return Q=g.sent(),this.renderReplacedElement(r,n,Q),[3,8];case 7:return g.sent(),this.context.logger.error("Error loading image "+r.src),[3,8];case 8:if(r instanceof Js&&this.renderReplacedElement(r,n,r.canvas),!(r instanceof Xs))return[3,12];g.label=9;case 9:return g.trys.push([9,11,,12]),[4,this.context.cache.match(r.svg)];case 10:return Q=g.sent(),this.renderReplacedElement(r,n,Q),[3,12];case 11:return g.sent(),this.context.logger.error("Error loading svg "+r.svg.substring(0,255)),[3,12];case 12:return r instanceof zs&&r.tree?(l=new A(this.context,{scale:this.options.scale,backgroundColor:r.backgroundColor,x:0,y:0,width:r.width,height:r.height}),[4,l.render(r.tree)]):[3,14];case 13:c=g.sent(),r.width&&r.height&&this.ctx.drawImage(c,0,0,r.width,r.height,r.bounds.left,r.bounds.top,r.bounds.width,r.bounds.height),g.label=14;case 14:if(r instanceof An&&(x=Math.min(r.bounds.width,r.bounds.height),r.type===St?r.checked&&(this.ctx.save(),this.path([new U(r.bounds.left+x*.39363,r.bounds.top+x*.79),new U(r.bounds.left+x*.16,r.bounds.top+x*.5549),new U(r.bounds.left+x*.27347,r.bounds.top+x*.44071),new U(r.bounds.left+x*.39694,r.bounds.top+x*.5649),new U(r.bounds.left+x*.72983,r.bounds.top+x*.23),new U(r.bounds.left+x*.84,r.bounds.top+x*.34085),new U(r.bounds.left+x*.39363,r.bounds.top+x*.79)]),this.ctx.fillStyle=z(Nn),this.ctx.fill(),this.ctx.restore()):r.type===Lt&&r.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(r.bounds.left+x/2,r.bounds.top+x/2,x/4,0,Math.PI*2,!0),this.ctx.fillStyle=z(Nn),this.ctx.fill(),this.ctx.restore())),D0(r)&&r.value.length){switch(d=this.createFontStyle(s),p=d[0],f=d[1],u=this.fontMetrics.getMetrics(p,f).baseline,this.ctx.font=p,this.ctx.fillStyle=z(s.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=T0(r.styles.textAlign),C=_t(r),h=0,r.styles.textAlign){case 1:h+=C.width/2;break;case 2:h+=C.width;break}v=C.add(h,0,0,-C.height/2+1),this.ctx.save(),this.path([new U(C.left,C.top),new U(C.left+C.width,C.top),new U(C.left+C.width,C.top+C.height),new U(C.left,C.top+C.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new Ve(r.value,v),s.letterSpacing,u),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!Z(r.styles.display,2048))return[3,20];if(r.styles.listStyleImage===null)return[3,19];if(w=r.styles.listStyleImage,w.type!==0)return[3,18];Q=void 0,y=w.url,g.label=15;case 15:return g.trys.push([15,17,,18]),[4,this.context.cache.match(y)];case 16:return Q=g.sent(),this.ctx.drawImage(Q,r.bounds.left-(Q.width+10),r.bounds.top),[3,18];case 17:return g.sent(),this.context.logger.error("Error loading list-style-image "+y),[3,18];case 18:return[3,20];case 19:t.listValue&&r.styles.listStyleType!==-1&&(p=this.createFontStyle(s)[0],this.ctx.font=p,this.ctx.fillStyle=z(s.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",C=new MA(r.bounds.left,r.bounds.top+P(r.styles.paddingTop,r.bounds.width),r.bounds.width,En(s.lineHeight,s.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new Ve(t.listValue,C),s.letterSpacing,En(s.lineHeight,s.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),g.label=20;case 20:return[2]}})})},A.prototype.renderStackContent=function(t){return nA(this,void 0,void 0,function(){var r,n,w,s,a,w,o,i,w,l,c,w,x,d,w,f,u,w,h,v,w;return tA(this,function(Q){switch(Q.label){case 0:if(Z(t.element.container.flags,16))debugger;return[4,this.renderNodeBackgroundAndBorders(t.element)];case 1:Q.sent(),r=0,n=t.negativeZIndex,Q.label=2;case 2:return r<n.length?(w=n[r],[4,this.renderStack(w)]):[3,5];case 3:Q.sent(),Q.label=4;case 4:return r++,[3,2];case 5:return[4,this.renderNodeContent(t.element)];case 6:Q.sent(),s=0,a=t.nonInlineLevel,Q.label=7;case 7:return s<a.length?(w=a[s],[4,this.renderNode(w)]):[3,10];case 8:Q.sent(),Q.label=9;case 9:return s++,[3,7];case 10:o=0,i=t.nonPositionedFloats,Q.label=11;case 11:return o<i.length?(w=i[o],[4,this.renderStack(w)]):[3,14];case 12:Q.sent(),Q.label=13;case 13:return o++,[3,11];case 14:l=0,c=t.nonPositionedInlineLevel,Q.label=15;case 15:return l<c.length?(w=c[l],[4,this.renderStack(w)]):[3,18];case 16:Q.sent(),Q.label=17;case 17:return l++,[3,15];case 18:x=0,d=t.inlineLevel,Q.label=19;case 19:return x<d.length?(w=d[x],[4,this.renderNode(w)]):[3,22];case 20:Q.sent(),Q.label=21;case 21:return x++,[3,19];case 22:f=0,u=t.zeroOrAutoZIndexOrTransformedOrOpacity,Q.label=23;case 23:return f<u.length?(w=u[f],[4,this.renderStack(w)]):[3,26];case 24:Q.sent(),Q.label=25;case 25:return f++,[3,23];case 26:h=0,v=t.positiveZIndex,Q.label=27;case 27:return h<v.length?(w=v[h],[4,this.renderStack(w)]):[3,30];case 28:Q.sent(),Q.label=29;case 29:return h++,[3,27];case 30:return[2]}})})},A.prototype.mask=function(t){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(t.slice(0).reverse()),this.ctx.closePath()},A.prototype.path=function(t){this.ctx.beginPath(),this.formatPath(t),this.ctx.closePath()},A.prototype.formatPath=function(t){var r=this;t.forEach(function(n,s){var a=hA(n)?n.start:n;s===0?r.ctx.moveTo(a.x,a.y):r.ctx.lineTo(a.x,a.y),hA(n)&&r.ctx.bezierCurveTo(n.startControl.x,n.startControl.y,n.endControl.x,n.endControl.y,n.end.x,n.end.y)})},A.prototype.renderRepeat=function(t,r,n,s){this.path(t),this.ctx.fillStyle=r,this.ctx.translate(n,s),this.ctx.fill(),this.ctx.translate(-n,-s)},A.prototype.resizeImage=function(t,r,n){var s;if(t.width===r&&t.height===n)return t;var a=(s=this.canvas.ownerDocument)!==null&&s!==void 0?s:document,o=a.createElement("canvas");o.width=Math.max(1,r),o.height=Math.max(1,n);var i=o.getContext("2d");return i.drawImage(t,0,0,t.width,t.height,0,0,r,n),o},A.prototype.renderBackgroundImage=function(t){return nA(this,void 0,void 0,function(){var r,n,s,a,o,i;return tA(this,function(l){switch(l.label){case 0:r=t.styles.backgroundImage.length-1,n=function(c){var x,d,f,k,aA,iA,R,j,L,u,k,aA,iA,R,j,h,v,w,Q,y,p,C,g,b,L,E,k,T,q,R,j,_A,aA,iA,zA,pA,OA,qA,jA,IA,$A,bA;return tA(this,function(le){switch(le.label){case 0:if(c.type!==0)return[3,5];x=void 0,d=c.url,le.label=1;case 1:return le.trys.push([1,3,,4]),[4,s.context.cache.match(d)];case 2:return x=le.sent(),[3,4];case 3:return le.sent(),s.context.logger.error("Error loading background-image "+d),[3,4];case 4:return x&&(f=gr(t,r,[x.width,x.height,x.width/x.height]),k=f[0],aA=f[1],iA=f[2],R=f[3],j=f[4],L=s.ctx.createPattern(s.resizeImage(x,R,j),"repeat"),s.renderRepeat(k,L,aA,iA)),[3,6];case 5:fo(c)?(u=gr(t,r,[null,null,null]),k=u[0],aA=u[1],iA=u[2],R=u[3],j=u[4],h=xo(c.angle,R,j),v=h[0],w=h[1],Q=h[2],y=h[3],p=h[4],C=document.createElement("canvas"),C.width=R,C.height=j,g=C.getContext("2d"),b=g.createLinearGradient(w,y,Q,p),vn(c.stops,v).forEach(function(Ee){return b.addColorStop(Ee.stop,z(Ee.color))}),g.fillStyle=b,g.fillRect(0,0,R,j),R>0&&j>0&&(L=s.ctx.createPattern(C,"repeat"),s.renderRepeat(k,L,aA,iA))):Qo(c)&&(E=gr(t,r,[null,null,null]),k=E[0],T=E[1],q=E[2],R=E[3],j=E[4],_A=c.position.length===0?[zr]:c.position,aA=P(_A[0],R),iA=P(_A[_A.length-1],j),zA=uo(c,aA,iA,R,j),pA=zA[0],OA=zA[1],pA>0&&OA>0&&(qA=s.ctx.createRadialGradient(T+aA,q+iA,0,T+aA,q+iA,pA),vn(c.stops,pA*2).forEach(function(Ee){return qA.addColorStop(Ee.stop,z(Ee.color))}),s.path(k),s.ctx.fillStyle=qA,pA!==OA?(jA=t.bounds.left+.5*t.bounds.width,IA=t.bounds.top+.5*t.bounds.height,$A=OA/pA,bA=1/$A,s.ctx.save(),s.ctx.translate(jA,IA),s.ctx.transform(1,0,0,$A,0,0),s.ctx.translate(-jA,-IA),s.ctx.fillRect(T,bA*(q-IA)+IA,R,j*bA),s.ctx.restore()):s.ctx.fill())),le.label=6;case 6:return r--,[2]}})},s=this,a=0,o=t.styles.backgroundImage.slice(0).reverse(),l.label=1;case 1:return a<o.length?(i=o[a],[5,n(i)]):[3,4];case 2:l.sent(),l.label=3;case 3:return a++,[3,1];case 4:return[2]}})})},A.prototype.renderSolidBorder=function(t,r,n){return nA(this,void 0,void 0,function(){return tA(this,function(s){return this.path(ts(n,r)),this.ctx.fillStyle=z(t),this.ctx.fill(),[2]})})},A.prototype.renderDoubleBorder=function(t,r,n,s){return nA(this,void 0,void 0,function(){var a,o;return tA(this,function(i){switch(i.label){case 0:return r<3?[4,this.renderSolidBorder(t,n,s)]:[3,2];case 1:return i.sent(),[2];case 2:return a=U0(s,n),this.path(a),this.ctx.fillStyle=z(t),this.ctx.fill(),o=F0(s,n),this.path(o),this.ctx.fill(),[2]}})})},A.prototype.renderNodeBackgroundAndBorders=function(t){return nA(this,void 0,void 0,function(){var r,n,s,a,o,i,l,c,x=this;return tA(this,function(d){switch(d.label){case 0:return this.applyEffects(t.getEffects(2)),r=t.container.styles,n=!YA(r.backgroundColor)||r.backgroundImage.length,s=[{style:r.borderTopStyle,color:r.borderTopColor,width:r.borderTopWidth},{style:r.borderRightStyle,color:r.borderRightColor,width:r.borderRightWidth},{style:r.borderBottomStyle,color:r.borderBottomColor,width:r.borderBottomWidth},{style:r.borderLeftStyle,color:r.borderLeftColor,width:r.borderLeftWidth}],a=K0(pe(r.backgroundClip,0),t.curves),n||r.boxShadow.length?(this.ctx.save(),this.path(a),this.ctx.clip(),YA(r.backgroundColor)||(this.ctx.fillStyle=z(r.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(t.container)]):[3,2];case 1:d.sent(),this.ctx.restore(),r.boxShadow.slice(0).reverse().forEach(function(f){x.ctx.save();var u=Tt(t.curves),h=f.inset?0:S0,v=C0(u,-h+(f.inset?1:-1)*f.spread.number,(f.inset?1:-1)*f.spread.number,f.spread.number*(f.inset?-2:2),f.spread.number*(f.inset?-2:2));f.inset?(x.path(u),x.ctx.clip(),x.mask(v)):(x.mask(u),x.ctx.clip(),x.path(v)),x.ctx.shadowOffsetX=f.offsetX.number+h,x.ctx.shadowOffsetY=f.offsetY.number,x.ctx.shadowColor=z(f.color),x.ctx.shadowBlur=f.blur.number,x.ctx.fillStyle=f.inset?z(f.color):"rgba(0,0,0,1)",x.ctx.fill(),x.ctx.restore()}),d.label=2;case 2:o=0,i=0,l=s,d.label=3;case 3:return i<l.length?(c=l[i],c.style!==0&&!YA(c.color)&&c.width>0?c.style!==2?[3,5]:[4,this.renderDashedDottedBorder(c.color,c.width,o,t.curves,2)]:[3,11]):[3,13];case 4:return d.sent(),[3,11];case 5:return c.style!==3?[3,7]:[4,this.renderDashedDottedBorder(c.color,c.width,o,t.curves,3)];case 6:return d.sent(),[3,11];case 7:return c.style!==4?[3,9]:[4,this.renderDoubleBorder(c.color,c.width,o,t.curves)];case 8:return d.sent(),[3,11];case 9:return[4,this.renderSolidBorder(c.color,o,t.curves)];case 10:d.sent(),d.label=11;case 11:o++,d.label=12;case 12:return i++,[3,3];case 13:return[2]}})})},A.prototype.renderDashedDottedBorder=function(t,r,n,s,a){return nA(this,void 0,void 0,function(){var o,i,l,c,x,d,f,u,h,v,w,Q,y,p,C,g,C,g;return tA(this,function(b){return this.ctx.save(),o=m0(s,n),i=ts(s,n),a===2&&(this.path(i),this.ctx.clip()),hA(i[0])?(l=i[0].start.x,c=i[0].start.y):(l=i[0].x,c=i[0].y),hA(i[1])?(x=i[1].end.x,d=i[1].end.y):(x=i[1].x,d=i[1].y),n===0||n===2?f=Math.abs(l-x):f=Math.abs(c-d),this.ctx.beginPath(),a===3?this.formatPath(o):this.formatPath(i.slice(0,2)),u=r<3?r*3:r*2,h=r<3?r*2:r,a===3&&(u=r,h=r),v=!0,f<=u*2?v=!1:f<=u*2+h?(w=f/(2*u+h),u*=w,h*=w):(Q=Math.floor((f+h)/(u+h)),y=(f-Q*u)/(Q-1),p=(f-(Q+1)*u)/Q,h=p<=0||Math.abs(h-y)<Math.abs(h-p)?y:p),v&&(a===3?this.ctx.setLineDash([0,u+h]):this.ctx.setLineDash([u,h])),a===3?(this.ctx.lineCap="round",this.ctx.lineWidth=r):this.ctx.lineWidth=r*2+1.1,this.ctx.strokeStyle=z(t),this.ctx.stroke(),this.ctx.setLineDash([]),a===2&&(hA(i[0])&&(C=i[3],g=i[0],this.ctx.beginPath(),this.formatPath([new U(C.end.x,C.end.y),new U(g.start.x,g.start.y)]),this.ctx.stroke()),hA(i[1])&&(C=i[1],g=i[2],this.ctx.beginPath(),this.formatPath([new U(C.end.x,C.end.y),new U(g.start.x,g.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},A.prototype.render=function(t){return nA(this,void 0,void 0,function(){var r;return tA(this,function(n){switch(n.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=z(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),r=p0(t),[4,this.renderStack(r)];case 1:return n.sent(),this.applyEffects([]),[2,this.canvas]}})})},A}(xa),D0=function(e){return e instanceof Zs||e instanceof Ys?!0:e instanceof An&&e.type!==Lt&&e.type!==St},K0=function(e,A){switch(e){case 0:return Tt(A);case 2:return h0(A);case 1:default:return Mt(A)}},T0=function(e){switch(e){case 1:return"center";case 2:return"right";case 0:default:return"left"}},M0=["-apple-system","system-ui"],_0=function(e){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?e.filter(function(A){return M0.indexOf(A)===-1}):e},O0=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.canvas=r.canvas?r.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),n.options=r,n.canvas.width=Math.floor(r.width*r.scale),n.canvas.height=Math.floor(r.height*r.scale),n.canvas.style.width=r.width+"px",n.canvas.style.height=r.height+"px",n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-r.x,-r.y),n.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+r.width+"x"+r.height+" at "+r.x+","+r.y+") with scale "+r.scale),n}return A.prototype.render=function(t){return nA(this,void 0,void 0,function(){var r,n;return tA(this,function(s){switch(s.label){case 0:return r=Kr(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,t),[4,k0(r)];case 1:return n=s.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=z(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(n,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},A}(xa),k0=function(e){return new Promise(function(A,t){var r=new Image;r.onload=function(){A(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},R0=function(){function e(A){var t=A.id,r=A.enabled;this.id=t,this.enabled=r,this.start=Date.now()}return e.prototype.debug=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,At([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.getTime=function(){return Date.now()-this.start},e.prototype.info=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,At([this.id,this.getTime()+"ms"],A))},e.prototype.warn=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.warn=="function"?console.warn.apply(console,At([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.error=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,At([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.instances={},e}(),G0=function(){function e(A,t){var r;this.windowBounds=t,this.instanceName="#"+e.instanceCount++,this.logger=new R0({id:this.instanceName,enabled:A.logging}),this.cache=(r=A.cache)!==null&&r!==void 0?r:new i0(this,A)}return e.instanceCount=1,e}(),P0=function(e,A){return A===void 0&&(A={}),V0(e,A)};typeof window<"u"&&aa.setContext(window);var V0=function(e,A){return nA(void 0,void 0,void 0,function(){var t,r,n,s,a,o,i,l,c,x,d,f,u,h,v,w,Q,y,p,C,b,g,b,L,E,k,T,q,R,j,_A,aA,iA,zA,pA,OA,qA,jA,IA,$A;return tA(this,function(bA){switch(bA.label){case 0:if(!e||typeof e!="object")return[2,Promise.reject("Invalid element provided as first argument")];if(t=e.ownerDocument,!t)throw new Error("Element is not attached to a Document");if(r=t.defaultView,!r)throw new Error("Document is not attached to a Window");return n={allowTaint:(L=A.allowTaint)!==null&&L!==void 0?L:!1,imageTimeout:(E=A.imageTimeout)!==null&&E!==void 0?E:15e3,proxy:A.proxy,useCORS:(k=A.useCORS)!==null&&k!==void 0?k:!1},s=Qr({logging:(T=A.logging)!==null&&T!==void 0?T:!0,cache:A.cache},n),a={windowWidth:(q=A.windowWidth)!==null&&q!==void 0?q:r.innerWidth,windowHeight:(R=A.windowHeight)!==null&&R!==void 0?R:r.innerHeight,scrollX:(j=A.scrollX)!==null&&j!==void 0?j:r.pageXOffset,scrollY:(_A=A.scrollY)!==null&&_A!==void 0?_A:r.pageYOffset},o=new MA(a.scrollX,a.scrollY,a.windowWidth,a.windowHeight),i=new G0(s,o),l=(aA=A.foreignObjectRendering)!==null&&aA!==void 0?aA:!1,c={allowTaint:(iA=A.allowTaint)!==null&&iA!==void 0?iA:!1,onclone:A.onclone,ignoreElements:A.ignoreElements,inlineImages:l,copyStyles:l},i.logger.debug("Starting document clone with size "+o.width+"x"+o.height+" scrolled to "+-o.left+","+-o.top),x=new $n(i,e,c),d=x.clonedReferenceElement,d?[4,x.toIFrame(t,o)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return f=bA.sent(),u=en(d)||Nc(d)?Qa(d.ownerDocument):Ot(i,d),h=u.width,v=u.height,w=u.left,Q=u.top,y=N0(i,d,A.backgroundColor),p={canvas:A.canvas,backgroundColor:y,scale:(pA=(zA=A.scale)!==null&&zA!==void 0?zA:r.devicePixelRatio)!==null&&pA!==void 0?pA:1,x:((OA=A.x)!==null&&OA!==void 0?OA:0)+w,y:((qA=A.y)!==null&&qA!==void 0?qA:0)+Q,width:(jA=A.width)!==null&&jA!==void 0?jA:Math.ceil(h),height:(IA=A.height)!==null&&IA!==void 0?IA:Math.ceil(v)},l?(i.logger.debug("Document cloned, using foreign object rendering"),b=new O0(i,p),[4,b.render(d)]):[3,3];case 2:return C=bA.sent(),[3,5];case 3:return i.logger.debug("Document cloned, element located at "+w+","+Q+" with size "+h+"x"+v+" using computed rendering"),i.logger.debug("Starting DOM parsing"),g=js(i,d),y===g.styles.backgroundColor&&(g.styles.backgroundColor=TA.TRANSPARENT),i.logger.debug("Starting renderer for element at "+p.x+","+p.y+" with size "+p.width+"x"+p.height),b=new L0(i,p),[4,b.render(g)];case 4:C=bA.sent(),bA.label=5;case 5:return(!(($A=A.removeContainer)!==null&&$A!==void 0)||$A)&&($n.destroy(f)||i.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),i.logger.debug("Finished rendering"),[2,C]}})})},N0=function(e,A,t){var r=A.ownerDocument,n=r.documentElement?Ge(e,getComputedStyle(r.documentElement).backgroundColor):TA.TRANSPARENT,s=r.body?Ge(e,getComputedStyle(r.body).backgroundColor):TA.TRANSPARENT,a=typeof t=="string"?Ge(e,t):t===null?TA.TRANSPARENT:4294967295;return A===r.documentElement?YA(n)?YA(s)?a:s:n:a};function Gr(){const e=["error","地图初始化失败: ","AMap.PlaceSearch","length","querySelectorAll",".custom-suggest-item","input","addEventListener","removeEventListener","top","left","createElement","adname","selectSuggestion","appendChild","block","style","parentNode","clearSelection","important","querySelector","setProperty","box-shadow","address","city","AMap","地图脚本加载失败，请检查网络连接","head","location","获取驾车数据失败：","none","parentElement","resize","image/png","地图图片数据无效或过小","map-container-","visibility","classList","contains",".png","destroy","cleanupCustomAutoComplete","_cleanup","mapInstances","地图服务未初始化，返回空对象","高德地理编码失败 - 地址: ","地理编码外层异常:","calculateDistance","sin","value","未知错误"];return Gr=function(){return e},Gr()}const lA=Y;function Y(e,A){const t=Gr();return Y=function(r,n){return r=r-0,t[r]},Y(e,A)}const wr="http://localhost:3999";class J0{constructor(){this.AMap=null,this.mapInstances=[]}async initialize(){const A=Y;if(typeof window>"u")return;let t="",r="";try{const n=await fetch(wr+"/api/amap_keys",{method:"GET",credentials:"include"});if(!n.ok)throw D.error("获取API密钥失败，请检查网络连接",n),new Error("获取API密钥失败，请检查网络连接");const{AMAP_CODE:s,AMAP_KEY:a}=await n.json();t=s,r=a}catch(n){throw D[A(0)]("获取API密钥异常，请检查网络连接",n),n}window._AMapSecurityConfig={securityJsCode:t};try{return this.AMap=await this.loadAMapScript(r),this.AMap}catch(n){throw new Error(A(1)+n.message)}}setupAutoComplete(A,t,r,n){if(!this.AMap)throw new Error("地图服务未初始化");this.createCustomAutoComplete(A,r),this.createCustomAutoComplete(t,n)}createCustomAutoComplete(A,t){const r=Y,n=document.getElementById(A);if(!n)return;const s=this.createSuggestContainer(A);this.AMap.plugin([r(2)],()=>{const a=r,o=new this.AMap.PlaceSearch({pageSize:10,pageIndex:1,citylimit:!1,extensions:"all"});let i=null,l=-1;const c=h=>{const v=Y,w=h.target.value.trim();if(w[v(3)]<2){this.hideSuggestions(s);return}i&&clearTimeout(i),i=setTimeout(()=>{this.searchPlaces(o,w,s,t,n)},300)},x=h=>{const v=Y,w=s[v(4)](v(5));switch(h.key){case"ArrowDown":h.preventDefault(),l=Math.min(l+1,w.length-1),this.updateSelection(w,l);break;case"ArrowUp":h.preventDefault(),l=Math.max(l-1,-1),this.updateSelection(w,l);break;case"Enter":h.preventDefault(),l>=0&&w[l]&&this.selectSuggestion(w[l],t,n,s);break;case"Escape":this.hideSuggestions(s),n.blur();break}},d=()=>{setTimeout(()=>{!s.matches(":hover")&&this.hideSuggestions(s)},150)},f=()=>{s.style.display!=="none"&&this.positionSuggestContainer(s,n)},u=()=>{s.style.display!=="none"&&this.positionSuggestContainer(s,n)};n.addEventListener(a(6),c),n.addEventListener("keydown",x),n.addEventListener("blur",d),n[a(7)]("focus",()=>{if(n.value.trim().length>=2){const h=n.value.trim();this.searchPlaces(o,h,s,t,n)}}),window.addEventListener("resize",f),window.addEventListener("scroll",u,!0),n._cleanup=()=>{n[a(8)]("input",c),n.removeEventListener("keydown",x),n.removeEventListener("blur",d),window.removeEventListener("resize",f),window.removeEventListener("scroll",u,!0),i&&clearTimeout(i),s.parentNode&&s.parentNode.removeChild(s)}})}createSuggestContainer(A){document.getElementById(A);const t=document.createElement("div");return t.className="custom-amap-suggest",t.id=A+"-suggest",t.style.cssText=`
            position: fixed;
            background: var(--vp-c-bg);
            border: 2px solid var(--vp-c-divider);
            border-radius: 12px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
            z-index: 99999;
            max-height: 300px;
            overflow-y: auto;
            display: none;
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            min-width: 300px;
        `,document.body.appendChild(t),t}positionSuggestContainer(A,t){const r=Y,n=t.getBoundingClientRect(),s=window.innerHeight,a=300,o=s-n.bottom,i=n[r(9)];o>=a||o>=i?(A.style.top=n.bottom+4+"px",A.style.maxHeight=Math.min(a,o-20)+"px"):(A.style.top=n.top-Math.min(a,i-20)+"px",A.style.maxHeight=Math.min(a,i-20)+"px"),A.style[r(10)]=n.left+"px",A.style.width=Math.max(n.width,300)+"px"}searchPlaces(A,t,r,n,s){A.search(t,(a,o)=>{a==="complete"&&o.poiList&&o.poiList.pois?this.showSuggestions(r,o.poiList.pois,n,s):this.hideSuggestions(r)})}showSuggestions(A,t,r,n){const s=Y;A.innerHTML="",this.positionSuggestContainer(A,n),t.slice(0,8).forEach((a,o)=>{const i=Y,l=document.createElement("div");l.className="custom-suggest-item",l.style.cssText=`
                padding: 12px 16px;
                cursor: pointer;
                border-bottom: 1px solid var(--vp-c-divider-light);
                transition: all 0.2s ease;
                display: flex;
                flex-direction: column;
                gap: 4px;
            `;const c=document[i(11)]("div");c.style.cssText=`
                font-weight: 500;
                color: var(--vp-c-text-1);
                font-size: 0.875rem;
            `,c.textContent=a.name;const x=document[i(11)]("div");x.style.cssText=`
                color: var(--vp-c-text-2);
                font-size: 0.8rem;
                line-height: 1.3;
            `,x.textContent=a.address||a.pname+a.cityname+a[i(12)],l.appendChild(c),l.appendChild(x),l[i(7)]("mouseenter",()=>{this.clearSelection(A)}),l.addEventListener("click",d=>{const f=i;d.preventDefault(),d.stopPropagation(),this[f(13)](l,r,n,A,a)}),(o===t.length-1||o===7)&&(l.style.borderBottom="none"),A[i(14)](l)}),A.style.display=s(15),this.updateThemeStyles(A)}hideSuggestions(A){const t=Y;A[t(16)].display="none",A.innerHTML=""}updateSelection(A,t){const r=Y;this.clearSelection(A[0][r(17)]),t>=0&&A[t]&&(A[t].classList.add("selected"),A[t].scrollIntoView({block:"nearest",behavior:"smooth"}))}[lA(18)](A){A[lA(4)](".custom-suggest-item").forEach(n=>{n.style.removeProperty("background"),n.style.removeProperty("color"),n.classList.remove("selected")})}selectSuggestion(A,t,r,n,s=null){if(!s){const o=A.querySelector("div:first-child").textContent,i=A.querySelector("div:last-child").textContent;s={name:o,address:i}}r.value=s.name;const a=this.extractLocationInfo(s);t(s.name,a),this.hideSuggestions(n),r.blur()}updateThemeStyles(A){const t=lA;document.documentElement.classList.contains("dark")?(A.style.setProperty("background","var(--vp-c-bg-soft)","important"),A.style.setProperty("border-color","var(--vp-c-divider)","important"),A.style.setProperty("box-shadow","0 12px 40px rgba(0, 0, 0, 0.5)",t(19)),A.querySelectorAll(t(5)).forEach(s=>{const a=t,o=s[a(20)]("div:first-child"),i=s.querySelector("div:last-child");o&&o.style.setProperty("color","var(--vp-c-text-1)","important"),i&&i.style.setProperty("color","var(--vp-c-text-2)","important")})):(A.style.setProperty("background","var(--vp-c-bg)",t(19)),A.style[t(21)]("border-color","var(--vp-c-divider)","important"),A.style.setProperty(t(22),"0 12px 40px rgba(0, 0, 0, 0.2)",t(19)),A.querySelectorAll(".custom-suggest-item").forEach(s=>{const a=s.querySelector("div:first-child"),o=s.querySelector("div:last-child");a&&a.style.setProperty("color","var(--vp-c-text-1)","important"),o&&o.style.setProperty("color","var(--vp-c-text-2)","important")}))}extractLocationInfo(A){var s,a;const t=lA;if(!A)return null;const r=A.district||A[t(23)]||"",n=this.parseAddress(r);return{lng:((s=A.location)==null?void 0:s.lng)||null,lat:((a=A.location)==null?void 0:a.lat)||null,province:n.province||null,city:n[t(24)]||null,district:n.district||null,address:r,adcode:A.adcode||null}}parseAddress(A){if(!A)return{province:null,city:null,district:null};const t=/(.*?)(省|自治区|市|特别行政区)/,r=/(.*?)(市|地区|州|盟)/,n=/(.*?)(区|县|市|旗)/;let s=null,a=null,o=null;const i=A.match(t);i&&(s=i[0],A=A.replace(s,""));const l=A.match(r);l&&(a=l[0],A=A.replace(a,""));const c=A.match(n);return c&&(o=c[0]),{province:s,city:a,district:o}}loadAMapScript(A){return new Promise(async(t,r)=>{const n=Y;if(window[n(25)])return t(window.AMap);try{const s=await fetch(wr+"/api/amap",{method:"GET",credentials:"include"});if(!s.ok)throw new Error("地图脚本加载失败，请检查网络连接");const{scriptUrl:a}=await s.json(),o=document.createElement("script");o.src=a,o.onload=()=>t(window.AMap),o.onerror=i=>r(new Error(n(26),{cause:i})),document[n(27)].appendChild(o)}catch(s){r(new Error("地图脚本加载失败，请检查网络连接",{cause:s}))}})}async getGeocodePosition(A,t){return new Promise((r,n)=>{A.getLocation(t,(s,a)=>{const o=Y;s==="complete"&&a.geocodes.length?r(a.geocodes[0][o(28)]):n(new Error("无法解析地址: "+t))})})}async initMap(A,t,r,n,s){const a=lA;if(!this.AMap)throw new Error("地图服务未初始化");const{s_lng:o,s_lat:i}=t,{e_lng:l,e_lat:c}=r;try{const x=new this.AMap.Map("map-container-"+A,{renderer:"canvas",resizeEnable:!0,viewMode:"2D",crossOrigin:"anonymous",WebGLParams:{preserveDrawingBuffer:!0},zoom:12,center:[o,i]});for(;this.mapInstances.length<=A;)this.mapInstances.push(null);this.mapInstances[A]=x;const d=new this.AMap.Driving({map:x,panel:"",renderer:"canvas",policy:n});return await new Promise((f,u)=>{const h=setTimeout(()=>{u(new Error("路线规划超时: "+A))},15e3);d.search(new AMap.LngLat(o,i),new AMap.LngLat(l,c),{waypoints:s},function(v,w){const Q=Y;clearTimeout(h),v!=="complete"?(D.error(Q(29)+w),u(new Error("路线规划失败: "+w))):setTimeout(()=>{f()},1e3)})}),x}catch(x){throw D.error("地图初始化失败:",x),new Error(a(1)+x.message)}}async drivingPlanning(A,t,r,n=0,s=[]){try{if(!document.getElementById("map-container-"+A))throw D.error("地图容器未找到"),new Error("地图容器未找到");this.destroyMapInstance(A),await this.initMap(A,t,r,n,s)}catch(a){throw D.error("路线生成错误",a),new Error(a.message||"路线生成失败")}}async saveMapAsImage(A,t,r,n=null){const s=lA;let a=null,o=null,i=!1;try{const l=document.getElementById("map-container-"+A);if(!l)throw new Error("地图容器未找到");const c=this.mapInstances[A];if(!c)throw new Error("地图实例 "+A+" 未找到");const x=l.closest(".planning-box");if(x&&n){const w=window.getComputedStyle(x);(w.display===s(30)||w.visibility==="hidden")&&(a=n.expandedSectionType,o=n.selectedDayIndex,i=!0,n.expandedSectionType="driving",n.selectedDayIndex=A,await n.$nextTick(),await new Promise(y=>setTimeout(y,1e3)))}const d=l.getBoundingClientRect();if(d.width===0||d.height===0){const w=[];let Q=l;for(;Q&&Q!==document.body;){const y=window.getComputedStyle(Q);y.display==="none"&&(w.push({element:Q,originalDisplay:Q.style.display}),Q.style.display="block"),y.visibility==="hidden"&&(w.push({element:Q,originalVisibility:Q.style.visibility}),Q.style.visibility="visible"),Q=Q[s(31)]}await new Promise(y=>setTimeout(y,500)),typeof c[s(32)]=="function"&&(c.resize(),await new Promise(y=>setTimeout(y,1e3)));try{const y=await this.captureMapCanvas(l,A),p=y.toDataURL(s(33));if(!p||p[s(3)]<1e3)throw new Error(s(34));return await this.uploadMapImage(p,t,r,A)}finally{w.forEach(({element:y,originalDisplay:p,originalVisibility:C})=>{const g=s;p!==void 0&&(y[g(16)].display=p),C!==void 0&&(y.style.visibility=C)})}}await new Promise(w=>setTimeout(w,2e3)),!l.querySelector(".amap-container")&&await new Promise(w=>setTimeout(w,3e3)),typeof c.resize=="function"&&(c.resize(),await new Promise(w=>setTimeout(w,500)));const u=await this.captureMapCanvas(l,A),h=u.toDataURL("image/png");if(!h||h.length<1e3)throw new Error("地图图片数据无效或过小");return await this.uploadMapImage(h,t,r,A)}catch(l){throw D.error("保存地图为图片失败:",l),l}finally{i&&n&&setTimeout(()=>{n.expandedSectionType=a,n.selectedDayIndex=o},500)}}async captureMapCanvas(A,t){const r=await P0(A,{useCORS:!0,allowTaint:!0,logging:!1,scale:2,backgroundColor:"#f5f5f5",onclone:n=>{const s=Y,a=n.getElementById(s(35)+t);a&&(a.style.display="block",a.style[s(36)]="visible",a.style.opacity="1",a.style.position="relative",a.style.zIndex="1");let o=a==null?void 0:a.parentElement;for(;o&&o!==n.body;)o.style.display="block",o.style.visibility="visible",o.style.opacity="1",o=o.parentElement},ignoreElements:n=>{const s=Y;return n.classList.contains("map-controls")||n[s(37)].contains("amap-copyright")||n.classList[s(38)]("header-toggle")||n.id==="some-obstructive-element"}});if(!r||r.width===0||r.height===0)throw new Error("地图截图失败: 画布尺寸为 "+((r==null?void 0:r.width)||0)+"x"+((r==null?void 0:r.height)||0));return r}async uploadMapImage(A,t,r,n){const s=lA,a=await fetch(wr+"/api/save_amap_img",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({image:A,user:t,filename:"map-"+r+"-"+n+s(39)})});if(!a.ok)throw D.error("图片保存失败，请检查网络连接",a),new Error("图片保存失败");return await a.json()}destroyMapInstance(A){const t=lA;if(this.mapInstances[A]){try{this.mapInstances[A][t(40)]()}catch(r){D.warn("销毁地图实例 "+A+" 时出错:",r)}this.mapInstances[A]=null}}cleanup(){const A=lA;this.mapInstances.forEach((t,r)=>{if(t&&t.destroy)try{t.destroy()}catch(n){D.warn("清理地图实例 "+r+" 时出错:",n)}}),this.mapInstances=[],this[A(41)]()}cleanupCustomAutoComplete(){["start-tipinput","end-tipinput"].forEach(t=>{const r=Y,n=document.getElementById(t);n&&n._cleanup&&(n._cleanup(),delete n[r(42)]);const s=document.getElementById(t+"-suggest");s&&s.parentNode&&s.parentNode.removeChild(s)})}getMapInstance(A){return this.mapInstances[A]}getAllMapInstances(){return this[lA(43)]}async getAccurateCoordinates(A,t,r){const n=lA;try{return this.AMap?new Promise((s,a)=>{const o=setTimeout(()=>{console.warn("地理编码超时: "+A+"，返回空对象"),s({})},1e4);try{this.AMap.plugin("AMap.Geocoder",()=>{const i=Y;try{new this[i(25)].Geocoder({city:r,radius:500,extensions:"all"}).getLocation(A,(c,x)=>{const d=i;try{if(c==="complete"&&x.geocodes&&x.geocodes.length>0)clearTimeout(o),s(x.geocodes[0].location);else{const f=d(45)+A+", 状态: "+c;console.warn(f,x),clearTimeout(o),s({})}}catch(f){console.warn("地理编码回调处理错误:",f),clearTimeout(o),s({})}})}catch(l){console.warn("地理编码插件加载错误:",l),clearTimeout(o),s({})}})}catch(i){console.warn("地理编码Promise创建错误:",i),clearTimeout(o),s({})}}):(console.warn(n(44)),{})}catch(s){return console.warn(n(46),s),{}}}[lA(47)](A,t,r,n){const s=lA,a=6371,o=(n-t)*Math.PI/180,i=(r-A)*Math.PI/180,l=Math[s(48)](o/2)*Math.sin(o/2)+Math.cos(t*Math.PI/180)*Math.cos(n*Math.PI/180)*Math[s(48)](i/2)*Math.sin(i/2),c=2*Math.atan2(Math.sqrt(l),Math.sqrt(1-l));return a*c}async getBatchAccurateCoordinates(A,t=null){var o;const r=lA;if(!this.AMap)throw new Error("地图服务未初始化");if(!Array.isArray(A)||A.length===0)return[];const n=[],s=A.map(i=>this.getAccurateCoordinates(i,t).catch(l=>({error:l.message,address:i}))),a=await Promise.allSettled(s);for(let i=0;i<a.length;i++){const l=a[i];l.status==="fulfilled"?l.value.error?n.push({success:!1,address:A[i],error:l[r(49)][r(0)],lng:null,lat:null}):n.push({success:!0,address:A[i],...l.value}):n.push({success:!1,address:A[i],error:((o=l.reason)==null?void 0:o.message)||r(50),lng:null,lat:null})}return n.filter(i=>i.success)[r(3)],n}}const BA=new J0,we=Ye;function Ye(e,A){const t=Pr();return Ye=function(r,n){return r=r-0,t[r]},Ye(e,A)}class X0{constructor(){this.autoScrollEnabled=!0,this.userScrollTimeout=null,this.lastScrollTop=0,this.isUserScrolling=!1,this.scrollObserver=null,this.handleUserScroll=null,this.lastUserScrollTime=0,this.scrollVelocity=0,this.consecutiveScrollCount=0}initScrollListener(){typeof window>"u"||(this.handleUserScroll=this.throttle(()=>{const A=Ye,t=Date.now(),r=window.pageYOffset||document.documentElement[A(0)],n=Math.abs(r-this.lastScrollTop),s=t-this.lastUserScrollTime;if(this.scrollVelocity=s>0?n/s:0,n>5){this.consecutiveScrollCount++;const a=n>30,o=r<this.lastScrollTop,i=this.scrollVelocity>.5,l=this.consecutiveScrollCount>=2;o||a||i||l&&n>15?(!this.isUserScrolling&&(this.isUserScrolling=!0,this.autoScrollEnabled=!1),this.userScrollTimeout&&clearTimeout(this.userScrollTimeout),this.userScrollTimeout=setTimeout(()=>{this.isUserScrolling=!1,this.consecutiveScrollCount=0,!this.checkAllContentCompleted()&&(this.autoScrollEnabled=!0,setTimeout(()=>{this.smartScrollToContent()},200))},5e3)):this.isUserScrolling&&n>8&&(this.userScrollTimeout&&clearTimeout(this.userScrollTimeout),this.userScrollTimeout=setTimeout(()=>{this.isUserScrolling=!1,this.consecutiveScrollCount=0,!this.checkAllContentCompleted()&&(this.autoScrollEnabled=!0,setTimeout(()=>{this.smartScrollToContent()},200))},5e3)),this.lastScrollTop=r,this[A(1)]=t}else this[A(2)]=Math.max(0,this.consecutiveScrollCount-1)},80),window.addEventListener("scroll",this.handleUserScroll,{passive:!0}))}cleanupScrollListener(){typeof window>"u"||(this.handleUserScroll&&window.removeEventListener("scroll",this.handleUserScroll),this.userScrollTimeout&&clearTimeout(this.userScrollTimeout))}throttle(A,t){let r;return function(){const n=arguments,s=this;!r&&(A.apply(s,n),r=!0,setTimeout(()=>r=!1,t))}}setContentCompletedChecker(A){this.checkAllContentCompleted=A}setDisplayStateChecker(A){this.getDisplayState=A}checkAllContentCompleted(){return!1}getDisplayState(){return{selectedDayIndex:-1,isAnyLoading:!1}}smartScrollToContent(){const A=Ye;if(!(!this.autoScrollEnabled||typeof window===A(3))){if(this[A(4)]()){this.autoScrollEnabled=!1;return}try{const t=document.querySelectorAll(".answer-area-container");if(t.length===0)return;let r=null,n=!1;n=this.getDisplayState().isAnyLoading;const a=document.querySelector(".global-loading-banner");if(!n&&a&&window.getComputedStyle(a).display!=="none"&&(n=!0),n)for(let o=t.length-1;o>=0;o--){const i=t[o];if(i.offsetParent!==null&&window.getComputedStyle(i).display!==A(5)){r=i;break}}if(!n){if(this[A(4)]()){this.autoScrollEnabled=!1;return}for(let o=t.length-1;o>=0;o--){const i=t[o];if(i.offsetParent!==null&&window.getComputedStyle(i).display!==A(5)){r=i;break}}}if(r){const o=r.getBoundingClientRect(),i=window.pageYOffset||document[A(6)].scrollTop,l=window[A(7)],c=a&&n?a.offsetHeight:0,x=i+o.bottom-l*.7+c;window.scrollTo({top:Math.max(0,x),behavior:"smooth"})}else window.scrollTo({top:document.body.scrollHeight,behavior:"smooth"})}catch{window.scrollTo({top:document.body.scrollHeight,behavior:"smooth"})}}}scrollPageToBottom(){this.smartScrollToContent()}[we(8)](){const A=we;this.autoScrollEnabled=!0,this.isUserScrolling=!1,typeof window<"u"?this.lastScrollTop=window.pageYOffset||document.documentElement.scrollTop||0:this.lastScrollTop=0,this.userScrollTimeout&&(clearTimeout(this.userScrollTimeout),this[A(9)]=null),this.lastUserScrollTime=Date.now(),this.scrollVelocity=0,this.consecutiveScrollCount=0}getScrollState(){return{autoScrollEnabled:this.autoScrollEnabled,isUserScrolling:this.isUserScrolling,lastScrollTop:this.lastScrollTop}}[we(10)](A){this.autoScrollEnabled=A}isUserScrollingActive(){return this.isUserScrolling}forceScrollToBottom(){const A=we;typeof window>"u"||window.scrollTo({top:document[A(11)].scrollHeight,behavior:A(12)})}[we(13)](A,t="smooth"){!A||typeof window>"u"||A.scrollIntoView({behavior:t,block:"center"})}cleanup(){const A=we;this.cleanupScrollListener(),this.autoScrollEnabled=!0,this.isUserScrolling=!1,this.lastScrollTop=0,this.userScrollTimeout=null,this.scrollObserver=null,this[A(14)]=null,this.lastUserScrollTime=0,this.scrollVelocity=0,this.consecutiveScrollCount=0}}function Pr(){const e=["scrollTop","lastUserScrollTime","consecutiveScrollCount","undefined","checkAllContentCompleted","none","documentElement","innerHeight","resetScrollState","userScrollTimeout","setAutoScrollEnabled","body","smooth","scrollToElement","handleUserScroll"];return Pr=function(){return e},Pr()}const mA=new X0;function Ze(e,A){const t=Vr();return Ze=function(r,n){return r=r-0,t[r]},Ze(e,A)}function Vr(){const e=["topmeans_form_data","undefined","resetFormData","length","出于规划耗时考虑，请勿一次性规划超过7天的行程，可以分多次规划","plan_mode","hasLocationChanged","e_location","未设置","join","地址信息不完整","getItem","loadFormData"];return Vr=function(){return e},Vr()}const kA=Ze;class W0{constructor(){const A=Ze;this.formDataKey=A(0)}saveFormData(A){if(!(typeof window>"u"))try{const t={...A,timestamp:Date.now()};localStorage.setItem(this.formDataKey,JSON.stringify(t))}catch{}}loadFormData(){if(typeof window===Ze(1))return null;try{const t=localStorage.getItem(this.formDataKey);if(!t)return null;const r=JSON.parse(t),n=7*24*60*60*1e3;return r.timestamp&&Date.now()-r.timestamp>n?(this.clearFormData(),null):r}catch{return this.clearFormData(),null}}clearFormData(){if(!(typeof window>"u"))try{localStorage.removeItem(this.formDataKey)}catch{}}getDefaultFormData(){return{s_address:null,e_address:null,startDate:null,dates:3,plan_mode:"往返",travel_mode:"自驾",s_location:{lng:null,lat:null,province:null,city:null,district:null,address:null,adcode:null},e_location:{lng:null,lat:null,province:null,city:null,district:null,address:null,adcode:null}}}[kA(2)](){return this.clearFormData(),this.getDefaultFormData()}validateFormData(A){const t=kA,{s_address:r,e_address:n,startDate:s,dates:a}=A,o=[r,n].every(l=>typeof l=="string"&&l.trim()[t(3)]>=2),i=s!==null;return a>7?{isValid:!1,message:t(4)}:o?i?{isValid:!0,message:"表单验证通过"}:{isValid:!1,message:"请选择开始日期"}:{isValid:!1,message:"请输入有效的起点和终点地址（至少2个字符）"}}mergeFormData(A,t){const r=kA;return t?{s_address:t.s_address!==void 0?t.s_address:A.s_address,e_address:t.e_address!==void 0?t.e_address:A.e_address,startDate:t.startDate!==void 0?t.startDate:A.startDate,dates:t.dates!==void 0?t.dates:A.dates,plan_mode:t.plan_mode!==void 0?t.plan_mode:A[r(5)],travel_mode:t.travel_mode!==void 0?t.travel_mode:A.travel_mode,s_location:t.s_location!==void 0?t.s_location:A.s_location,e_location:t.e_location!==void 0?t.e_location:A.e_location}:A}hasFormDataChanged(A,t){const r=kA;return!!(["s_address","e_address","startDate","dates","plan_mode","travel_mode"].some(s=>A[s]!==t[s])||this.hasLocationChanged(A.s_location,t.s_location)||this[r(6)](A.e_location,t[r(7)]))}hasLocationChanged(A,t){return!A&&!t?!1:!A||!t?!0:["lng","lat","province","city","district","address","adcode"].some(n=>A[n]!==t[n])}getFormDataSummary(A){const t=kA,{s_address:r,e_address:n,startDate:s,dates:a,plan_mode:o,travel_mode:i,s_location:l,e_location:c}=A;return{route:(r||"未设置")+" → "+(n||t(8)),duration:a+"天",mode:o+" - "+i,startDate:s||"未设置",startLocationInfo:this.getLocationSummary(l),endLocationInfo:this.getLocationSummary(c),hasCoordinates:this.hasCompleteCoordinates(l,c),isComplete:!!(r&&n&&s&&a>0)}}getLocationSummary(A){const t=kA;if(!A)return"位置信息未设置";const r=[];A.province&&r.push(A.province),A.city&&r.push(A.city),A.district&&r.push(A.district);const n=r.length>0?r[t(9)](" "):A.address||t(10),s=A.lng&&A.lat?"("+A.lng+", "+A.lat+")":"(坐标未获取)";return n+" "+s}hasCompleteCoordinates(A,t){return!!(A!=null&&A.lng&&(A!=null&&A.lat)&&(t!=null&&t.lng)&&(t!=null&&t.lat))}exportFormData(A){try{return JSON.stringify(A,null,2)}catch{return null}}importFormData(A){try{const t=JSON.parse(A),r=this.validateFormData(t);if(r.isValid)return t;throw new Error(r.message)}catch(t){throw t}}setStorageKey(A){this.formDataKey=A}getStorageKey(){return this.formDataKey}isLocalStorageAvailable(){if(typeof window>"u")return!1;try{const A="__localStorage_test__";return localStorage.setItem(A,A),localStorage.removeItem(A),!0}catch{return!1}}getStorageSize(){const A=kA;if(!this.isLocalStorageAvailable())return 0;try{const t=localStorage[A(11)](this.formDataKey);return t?new Blob([t]).size:0}catch{return 0}}cleanupExpiredData(A=7*24*60*60*1e3){const t=kA,r=this[t(12)]();return r&&r.timestamp&&Date.now()-r.timestamp>A?(this.clearFormData(),!0):!1}}const RA=new W0;function KA(e,A){const t=Nr();return KA=function(r,n){return r=r-0,t[r]},KA(e,A)}const xA="http://localhost:3999";class Y0{constructor(){this.abortController=null}async getHotelUrl(A,t,r,n,s){try{const a=await fetch(xA+"/api/hotel",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:""+s+A.replace("*",""),user:t,create_time:r,day:""+n})});if(!a.ok)throw new Error("酒店信息获取失败");const{success:o,url:i}=await a.json();return i}catch(a){throw a}}async getFoodImgUrl(A,t){try{const r=await fetch(xA+"/api/ai_img",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:"一种食物或一个著名饭店，请根据后续描述来进行写实风格的图片生成，名字："+A+",相关信息："+t})});if(!r.ok)throw new Error("美食图片获取失败");const{success:n,url:s}=await r.json();return s}catch(r){throw r}}async getAIImg(A,t,r,n){const s=KA;try{const a=await fetch(xA+"/api/ai_img2",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:A,location:t,name:r,prompt:r+"："+n})});if(!a.ok)throw new Error("美食图片获取失败");const{success:o,url:i}=await a[s(0)]();return i}catch(a){throw a}}async getViewUrl(A){const t=KA;try{const r=await fetch(xA+t(1),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:A})});if(!r.ok)throw new Error("景点信息获取失败");const{success:n,url:s}=await r.json();return s}catch(r){throw r}}async savePlanToDB(A){const t=KA,{content:r,account:n,filename:s}=A;try{let a=await fetch(xA+t(2),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:r,user:n,filename:s})});if(!a.ok)throw new Error(t(3));return await a.json()}catch(a){throw a}}async addPlanToUser(A){const{account:t,create_time:r,days:n}=A;try{const s=await fetch(xA+"/api/user/add_plan",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({account:t,create_time:r,days:n})});if(!s.ok)throw new Error("计划存库失败，请检查网络连接");return await s.json()}catch(s){throw s}}async sendMessage(A,t,r,n){const s=KA;this.abortController&&this.abortController.abort(),this.abortController=new AbortController;try{const a=await fetch(xA+"/api/ds",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({msg:A}),signal:this[s(4)].signal});if(!a.ok)throw new Error("DS API 请求失败!");const o=a.body.getReader(),i=new TextDecoder("utf-8");let l="";for(;;){const{done:c,value:x}=await o[s(5)]();if(c)break;const d=i.decode(x),f=d.split(`
`).filter(u=>u.trim());for(const u of f)try{if(!u.startsWith("data: "))continue;const h=u.slice(6);if(h==="[DONE]")break;const v=JSON.parse(h),w=v.choices[0].delta.content;w&&(l+=w,t&&t(l,w))}catch{}}return r&&r(l),l}catch(a){if(a.name==="AbortError")return null;throw n&&n(a),a}}cancelCurrentRequest(){this.abortController&&(this.abortController.abort(),this.abortController=null)}async getApiKeys(){try{const A=await fetch(xA+"/api/amap_keys",{method:"GET",credentials:"include"});if(!A.ok)throw new Error("获取API密钥失败，请检查网络连接");return await A.json()}catch(A){throw A}}async getMapScriptUrl(){const A=KA;try{const t=await fetch(xA+"/api/amap",{method:A(6),credentials:"include"});if(!t.ok)throw new Error("地图脚本加载失败，请检查网络连接");return await t[A(0)]()}catch{throw new Error("地图脚本加载失败，请检查网络连接")}}async saveMapImage(A){const t=KA,{image:r,user:n,filename:s}=A;try{const a=await fetch(xA+"/api/save_amap_img",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON[t(7)]({image:r,user:n,filename:s})});if(!a.ok)throw new Error("图片保存失败");return await a.json()}catch(a){throw a}}async get(A,t={}){const r=KA;try{const n=await fetch(""+xA+A,{method:"GET",credentials:r(8),...t});if(!n.ok)throw new Error("GET请求失败: "+n[r(9)]);return await n.json()}catch(n){throw n}}async post(A,t,r={}){try{const n=await fetch(""+xA+A,{method:"POST",headers:{"Content-Type":"application/json",...r.headers},body:JSON.stringify(t),credentials:"include",...r});if(!n.ok)throw new Error("POST请求失败: "+n.status);return await n.json()}catch(n){throw n}}async checkConnection(){try{return(await fetch(xA+"/api/health",{method:"GET",timeout:5e3})).ok}catch{return!1}}cleanup(){this.cancelCurrentRequest()}}function Nr(){const e=["json","/api/view","/api/save_plan","保存计划失败，请检查网络连接","abortController","read","GET","stringify","include","status"];return Nr=function(){return e},Nr()}const fe=new Y0;function ze(e,A){const t=Jr();return ze=function(r,n){return r=r-0,t[r]},ze(e,A)}const te=ze;function Jr(){const e=["ensureAmapSuggestStyles","startAmapStyleMonitor","includes",".amap-ui-autocomplete",'div[class*="amap"][class*="sug"]',"style","important","1px solid var(--vp-c-divider)","setProperty","color","contains","getPropertyValue","--vp-c-divider-light","--vp-c-brand","head","remove"];return Jr=function(){return e},Jr()}class Z0{constructor(){this.amapObserver=null,this.amapStyleInterval=null}[te(0)](){const A=te;typeof window>"u"||(this.injectAmapStyles(),this[A(1)]())}injectAmapStyles(){const A=document.getElementById("amap-dark-theme-fix");A&&A.remove();const t=document.createElement("style");t.id="amap-dark-theme-fix",t.innerHTML=`
            /* 高德地图提示框强制样式 - 最高优先级 */
            .amap-sug-result,
            .amap-ui-autocomplete,
            div[class*="amap"][class*="sug"],
            div[class*="amap"][class*="auto"] {
                background: var(--vp-c-bg) !important;
                border: 1px solid var(--vp-c-divider) !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
                z-index: 99999 !important;
            }

            .amap-sug-result .auto-item,
            .amap-sug-result li,
            .amap-ui-autocomplete .amap-ui-autocomplete-item,
            .amap-ui-autocomplete li,
            div[class*="amap"] .auto-item,
            div[class*="amap"] li {
                background: var(--vp-c-bg) !important;
                color: var(--vp-c-text-1) !important;
                border-bottom: 1px solid var(--vp-c-divider-light) !important;
                padding: 12px 16px !important;
                font-size: 0.875rem !important;
                line-height: 1.4 !important;
            }

            .amap-sug-result .auto-item *,
            .amap-sug-result li *,
            .amap-ui-autocomplete .amap-ui-autocomplete-item *,
            .amap-ui-autocomplete li *,
            div[class*="amap"] .auto-item *,
            div[class*="amap"] li * {
                color: var(--vp-c-text-1) !important;
            }

            /* 深色主题特殊处理 */
            .dark .amap-sug-result,
            .dark .amap-ui-autocomplete,
            .dark div[class*="amap"][class*="sug"],
            .dark div[class*="amap"][class*="auto"] {
                background: var(--vp-c-bg-soft) !important;
                border-color: var(--vp-c-divider) !important;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3) !important;
            }

            .dark .amap-sug-result .auto-item,
            .dark .amap-sug-result li,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item,
            .dark .amap-ui-autocomplete li,
            .dark div[class*="amap"] .auto-item,
            .dark div[class*="amap"] li {
                background: var(--vp-c-bg-soft) !important;
                color: #ffffff !important;
                border-bottom-color: var(--vp-c-divider) !important;
            }

            .dark .amap-sug-result .auto-item *,
            .dark .amap-sug-result li *,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item *,
            .dark .amap-ui-autocomplete li *,
            .dark div[class*="amap"] .auto-item *,
            .dark div[class*="amap"] li * {
                color: #ffffff !important;
            }

            /* 悬停效果 */
            .amap-sug-result .auto-item:hover,
            .amap-sug-result li:hover,
            .amap-ui-autocomplete .amap-ui-autocomplete-item:hover,
            .amap-ui-autocomplete li:hover {
                background: var(--vp-c-brand-soft) !important;
                color: var(--vp-c-brand-1) !important;
            }

            .dark .amap-sug-result .auto-item:hover,
            .dark .amap-sug-result li:hover,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item:hover,
            .dark .amap-ui-autocomplete li:hover {
                background: var(--vp-c-brand-dimm) !important;
                color: #ffffff !important;
            }

            .amap-sug-result .auto-item:hover *,
            .amap-sug-result li:hover *,
            .amap-ui-autocomplete .amap-ui-autocomplete-item:hover *,
            .amap-ui-autocomplete li:hover *,
            .dark .amap-sug-result .auto-item:hover *,
            .dark .amap-sug-result li:hover *,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item:hover *,
            .dark .amap-ui-autocomplete li:hover * {
                color: inherit !important;
            }
        `,document.head.appendChild(t)}startAmapStyleMonitor(){this.amapStyleInterval&&clearInterval(this.amapStyleInterval),this.amapStyleInterval=setInterval(()=>{this.forceApplyAmapStyles()},500);const A=new MutationObserver(t=>{let r=!1;t.forEach(n=>{n.addedNodes.forEach(s=>{const a=ze;if(s.nodeType===Node.ELEMENT_NODE){const o=s.className||"";(o[a(2)]("amap")||o.includes("sug")||o[a(2)]("auto"))&&(r=!0)}})}),r&&setTimeout(()=>this.forceApplyAmapStyles(),100)});A.observe(document.body,{childList:!0,subtree:!0}),this.amapObserver=A}forceApplyAmapStyles(){const A=te;if(!(typeof window>"u"))try{const t=document.documentElement.classList.contains("dark"),r=t?"#ffffff":"var(--vp-c-text-1)",n=t?"var(--vp-c-bg-soft)":"var(--vp-c-bg)";[".amap-sug-result",A(3),A(4),'div[class*="amap"][class*="auto"]','[class*="amap-sug"]','[class*="amap-auto"]'].forEach(a=>{document.querySelectorAll(a).forEach(i=>{const l=ze;i&&i.style&&(i[l(5)].setProperty("background",n,l(6)),i.style.setProperty("border",l(7),"important"),i.style.setProperty("border-radius","8px","important"),i.style.setProperty("z-index","99999","important")),i.querySelectorAll('.auto-item, li, .amap-ui-autocomplete-item, [class*="item"]').forEach(x=>{const d=l;x&&x[d(5)]&&(x.style.setProperty("background",n,"important"),x.style[d(8)](d(9),r,"important"),x.style.setProperty("padding","12px 16px",d(6)),x.querySelectorAll("*").forEach(u=>{u&&u[d(5)]&&u.style.setProperty("color",r,"important")}))})})})}catch{}}isDarkMode(){const A=te;return typeof window>"u"?!1:document.documentElement.classList[A(10)]("dark")}getThemeColors(){const A=te;if(typeof window>"u")return{};const t=getComputedStyle(document.documentElement);return{bg:t[A(11)]("--vp-c-bg").trim(),bgSoft:t.getPropertyValue("--vp-c-bg-soft").trim(),text1:t.getPropertyValue("--vp-c-text-1").trim(),divider:t.getPropertyValue("--vp-c-divider").trim(),dividerLight:t.getPropertyValue(A(12)).trim(),brand:t.getPropertyValue(A(13)).trim(),brandSoft:t.getPropertyValue("--vp-c-brand-soft").trim(),brandDimm:t.getPropertyValue("--vp-c-brand-dimm").trim()}}watchThemeChange(A){if(typeof window>"u")return;const t=new MutationObserver(r=>{r.forEach(n=>{if(n.type==="attributes"&&n.attributeName==="class"){const s=document.documentElement.classList.contains("dark");A(s)}})});return t.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),t}applyCustomStyles(A,t){const r=te,n=document.getElementById(A);n&&n.remove();const s=document.createElement(r(5));s.id=A,s.innerHTML=t,document[r(14)].appendChild(s)}removeCustomStyles(A){const t=te,r=document.getElementById(A);r&&r[t(15)]()}cleanup(){this.amapObserver&&(this.amapObserver.disconnect(),this.amapObserver=null),this.amapStyleInterval&&(clearInterval(this.amapStyleInterval),this.amapStyleInterval=null),this.removeCustomStyles("amap-dark-theme-fix")}reinitialize(){this.cleanup(),this.ensureAmapSuggestStyles()}getStyleState(){return{hasAmapObserver:!!this.amapObserver,hasStyleInterval:!!this.amapStyleInterval,isDarkMode:this.isDarkMode(),themeColors:this.getThemeColors()}}}const ns=new Z0,z0={key:0,class:"journal-recommendation"},q0={class:"recommendation-header"},j0={class:"header-title"},$0={class:"recommendation-content"},AB={key:0,class:"loading-container"},eB={key:1,class:"empty-state"},tB={class:"empty-hint"},rB={key:2,class:"journals-container"},nB={class:"journals-list"},sB=["onClick"],aB={class:"journal-cover"},iB=["src","alt"],oB={key:1,class:"cover-placeholder"},lB={class:"journal-overlay"},cB={class:"overlay-content"},BB={class:"journal-title"},xB={class:"journal-meta"},dB={class:"travel-days"},uB={class:"travel-mode"},hB={class:"journal-info"},gB={class:"author-info"},wB=["src","alt"],fB={class:"author-details"},QB={class:"author-name"},CB={class:"publish-time"},pB={class:"journal-stats"},UB={class:"stat-item"},FB={class:"stat-item"},mB={class:"journal-preview"},vB={class:"content-preview"},yB={key:0,class:"tags"},EB={key:0,class:"view-more"},HB={key:0,class:"journal-detail"},IB={class:"detail-header"},bB={class:"travel-info"},SB={class:"travel-route"},LB={class:"travel-days"},DB={class:"travel-mode"},KB={class:"travel-date"},TB={class:"author-info"},MB=["src","alt"],_B={class:"author-details"},OB={class:"author-name"},kB={class:"publish-time"},RB={class:"detail-content"},GB={key:0,class:"cover-image"},PB=["src","alt"],VB=["innerHTML"],NB={class:"detail-actions"},JB={__name:"JournalRecommendation",props:{destination:{type:String,required:!0},maxCount:{type:Number,default:6}},emits:["journalClick"],setup(e,{emit:A}){const t=e,r=Qe(),n="http://localhost:3999/api",s=Ae(!1),a=Ae(!1),o=Ae([]),i=Ae(!0),l=Ae(1),c=Ae(!1),x=Ae(null);ga(()=>{t.destination&&d()});const d=async(p=1)=>{if(r.isLoggedIn)try{p===1?s.value=!0:a.value=!0;const g=await(await fetch(`${n}/journals/recommendations/${encodeURIComponent(t.destination)}?page=${p}&pageSize=6`,{headers:{Authorization:`Bearer ${r.token}`}})).json();g.success?(p===1?o.value=g.data||[]:o.value.push(...g.data||[]),i.value=g.data&&g.data.length===6,l.value=p):(p===1&&(o.value=[]),i.value=!1)}catch(C){console.error("加载推荐游记失败:",C),p===1&&(o.value=[]),i.value=!1}finally{s.value=!1,a.value=!1}},f=()=>{!i.value||a.value||d(l.value+1)},u=p=>new Date(p).toLocaleDateString("zh-CN",{month:"short",day:"numeric"}),h=p=>{if(!p)return"";const C=p.replace(/!\[.*?\]\(.*?\)/g,"").replace(/\[.*?\]\(.*?\)/g,"").replace(/[#*>`-]/g,"").replace(/\n/g," ").trim();return C.length>80?C.substring(0,80)+"...":C},v=p=>os.parseMarkdown(p),w=async p=>{try{const g=await(await fetch(`${n}/journals/${p.id}`,{headers:r.token?{Authorization:`Bearer ${r.token}`}:{}})).json();g.success?(x.value=g.data,c.value=!0):ce.error(g.message||"加载游记详情失败")}catch(C){console.error("获取游记详情失败:",C),ce.error("加载失败，请稍后重试")}},Q=async()=>{if(!r.isLoggedIn){ce.warning("请先登录");return}if(x.value)try{const C=await(await fetch(`${n}/journals/${x.value.id}/like`,{method:"POST",headers:{Authorization:`Bearer ${r.token}`}})).json();if(C.success){x.value.is_liked=C.isLiked,x.value.like_count+=C.isLiked?1:-1;const g=o.value.find(b=>b.id===x.value.id);g&&(g.is_liked=C.isLiked,g.like_count=x.value.like_count)}else ce.error(C.message||"操作失败")}catch(p){console.error("点赞失败:",p),ce.error("操作失败，请稍后重试")}},y=()=>{ce.info("评论功能正在开发中")};return(p,C)=>{var k;const g=Ue("el-icon"),b=Ue("el-tag"),L=Ue("el-button"),E=Ue("el-dialog");return e.destination?(M(),_("div",z0,[B("div",q0,[C[1]||(C[1]=B("div",{class:"header-icon"},"✨",-1)),B("h3",j0,"发现更多 "+I(e.destination)+" 的精彩游记",1),C[2]||(C[2]=B("p",{class:"header-subtitle"},"看看其他旅行者的真实体验",-1))]),B("div",$0,[s.value?(M(),_("div",AB,C[3]||(C[3]=[B("div",{class:"loading-spinner"},[B("div",{class:"spinner"}),B("span",null,"正在寻找相关游记...")],-1)]))):o.value.length===0?(M(),_("div",eB,[C[4]||(C[4]=B("div",{class:"empty-icon"},"📖",-1)),C[5]||(C[5]=B("div",{class:"empty-text"},"暂时没有找到相关游记",-1)),B("div",tB,"成为第一个分享 "+I(e.destination)+" 游记的人吧！",1)])):(M(),_("div",rB,[B("div",nB,[(M(!0),_(vA,null,DA(o.value,T=>(M(),_("div",{key:T.id,class:"journal-item",onClick:q=>w(T)},[B("div",aB,[T.cover_image?(M(),_("img",{key:0,src:T.cover_image,alt:T.title,class:"cover-image"},null,8,iB)):(M(),_("div",oB,C[6]||(C[6]=[B("div",{class:"placeholder-icon"},"📷",-1)]))),B("div",lB,[B("div",cB,[B("div",BB,I(T.title),1),B("div",xB,[B("span",dB,I(T.travel_days)+"天",1),B("span",uB,I(T.travel_mode),1)])])])]),B("div",hB,[B("div",gB,[B("img",{src:T.avatar,alt:T.nickname,class:"author-avatar"},null,8,wB),B("div",fB,[B("div",QB,I(T.nickname),1),B("div",CB,I(u(T.created_at)),1)])]),B("div",pB,[B("div",UB,[eA(g,null,{default:UA(()=>[eA(He(tn))]),_:1}),B("span",null,I(T.like_count||0),1)]),B("div",FB,[eA(g,null,{default:UA(()=>[eA(He(da))]),_:1}),B("span",null,I(T.view_count||0),1)])])]),B("div",mB,[B("div",vB,I(h(T.content)),1),T.tags&&T.tags.length>0?(M(),_("div",yB,[(M(!0),_(vA,null,DA(T.tags.slice(0,2),q=>(M(),wa(b,{key:q,size:"small",class:"tag-item"},{default:UA(()=>[J(I(q),1)]),_:2},1024))),128))])):yA("",!0)])],8,sB))),128))]),i.value?(M(),_("div",EB,[eA(L,{onClick:f,loading:a.value,type:"primary",plain:""},{default:UA(()=>C[7]||(C[7]=[J(" 查看更多游记 ")])),_:1},8,["loading"])])):yA("",!0)]))]),eA(E,{modelValue:c.value,"onUpdate:modelValue":C[0]||(C[0]=T=>c.value=T),title:(k=x.value)==null?void 0:k.title,width:"80%","close-on-click-modal":!0},{default:UA(()=>[x.value?(M(),_("div",HB,[B("div",IB,[B("div",bB,[B("span",SB,[eA(g,null,{default:UA(()=>[eA(He(ua))]),_:1}),J(" "+I(x.value.start_location)+" → "+I(x.value.destination),1)]),B("span",LB,I(x.value.travel_days)+"天",1),B("span",DB,I(x.value.travel_mode),1),B("span",KB,I(u(x.value.travel_date)),1)]),B("div",TB,[B("img",{src:x.value.avatar,alt:x.value.nickname,class:"author-avatar"},null,8,MB),B("div",_B,[B("div",OB,I(x.value.nickname),1),B("div",kB,I(u(x.value.created_at)),1)])])]),B("div",RB,[x.value.cover_image?(M(),_("div",GB,[B("img",{src:x.value.cover_image,alt:x.value.title},null,8,PB)])):yA("",!0),B("div",{class:"content-markdown",innerHTML:v(x.value.content)},null,8,VB)]),B("div",NB,[eA(L,{onClick:Q,type:x.value.is_liked?"primary":"default"},{default:UA(()=>[eA(g,null,{default:UA(()=>[eA(He(tn))]),_:1}),J(" "+I(x.value.is_liked?"已点赞":"点赞")+" ("+I(x.value.like_count||0)+") ",1)]),_:1},8,["type"]),eA(L,{onClick:y},{default:UA(()=>[eA(g,null,{default:UA(()=>[eA(He(ha))]),_:1}),J(" 评论 ("+I(x.value.comment_count||0)+") ",1)]),_:1})])])):yA("",!0)]),_:1},8,["modelValue","title"])])):yA("",!0)}}},XB=ls(JB,[["__scopeId","data-v-2eead541"]]);function Xr(){const e=["不走高速且避免收费","getDefaultFormData","getCurrentProcessingDay","天的路线，请稍等...","天的美食信息，请稍等...","contents","drivingCompleted","weatherCompleted","s_location","ensureAmapSuggestStyles","error","setDisplayStateChecker","loadFormData","selectedDayIndex","cleanup","removeEventListener","themeObserver","parse",'<div class="markdown-fallback"><pre>',"length","activeDayDetailIndex","$nextTick",".rent-header",".hotel-header","expanded","weather","travel_mode","food","cost","expandedSectionType","querySelectorAll","offsetParent","e_address","rent_customized","保存规划内容失败:","getItem","now","plan_customized","formData","plan_mode","warn","querySelector","closest","parentNode","originalNextSibling","appendChild"," 缩放调整时出现错误:","addEventListener","undefined","resizeHandler","rent_requirements","drivingPlanning","name","## ","hotel","info","getViewUrl","DS API 请求失败!","住宿推荐","**酒店信息:**","read","replace","trim","rentCompleted","hotel_requirements","planningHotel","s_address","join","getHours","askQwen","rent","dates","driving","split","%SP","%SC","%EP","lng","push","lat","注意，我已经去过","view","setLoadingState","last_end","%PT","getAIImg","url","costCompleted","message","showPaymentWindow","handleContentRestoration","showBtn","showPlanningFailure","scrollTo","showFloatingButtons","scroll","handleScroll","getBoundingClientRect","documentElement","smooth","resetAllLoadingStates","refreshVisibleMaps","pos","天美食规划失败:","isPlanningFullyCompleted","clearPlanningContents","paymentAmount","e_location","getFormattedDate","租车方案规划失败：","planningFinishProc","路线规划失败：","planningView","resetScrollState","planningFood","美食规划失败：","美食推荐规划失败，请检查网络连接后重试"];return Xr=function(){return e},Xr()}function m(e,A){const t=Xr();return m=function(r,n){return r=r-0,t[r]},m(e,A)}const re="http://localhost:3999",Ft=void 0,ne=void 0,WB=void 0,ss=void 0,as=void 0,YB=void 0,ZB=void 0,is=void 0,zB=void 0;D.setLevel("info");const qB={components:{JournalRecommendation:XB,PaymentMethods:fa},data(){const e=m;return{contents:[],showBtn:!0,loading:!1,errorMessage:"",last_start:"",last_end:"",rent_requirements:"",rent_customized:!1,plan_requirements:"0",plan_customized:!1,hotel_requirements:"",hotel_customized:!1,maximizedMapIndex:-1,originalParent:null,originalNextSibling:null,planOptions:[{value:"0",text:"速度优先(默认值)"},{value:"1",text:"费用优先"},{value:"2",text:"距离优先"},{value:"3",text:e(0)},{value:"4",text:"躲避拥堵"},{value:"5",text:"不走高速"},{value:"6",text:"躲避拥堵且不走高速"},{value:"7",text:"躲避拥堵且距离优先"},{value:"8",text:"躲避拥堵且不走高速且距离优先"},{value:"9",text:"躲避拥堵且不走高速且费用优先"}],...RA[e(1)](),selectedDayIndex:-1,activeDayDetailIndex:-1,expandedSectionType:null,shouldShowJournalRecommendation:!1,loadingStates:{weather:!1,rent:!1,driving:!1,view:!1,food:!1,hotel:!1,cost:!1},showCompletionModal:!1,showFailureModal:!1,failureReason:"",showFloatingButtons:!1,showPaymentModal:!1,paymentAmount:0,paymentCompleted:!1}},computed:{selectedPlanText(){const e=this.planOptions.find(A=>A.value===this.plan_requirements);return e?e.text:""},isAnyLoading(){return Object.values(this.loadingStates).some(e=>e)},currentLoadingMessage(){const e=m,A=this[e(2)](),t={weather:"正在查询天气信息，请稍等...",rent:"正在思考租车方案，请稍等...",driving:"正在规划第"+A+e(3),view:"正在规划第"+A+"天的景点信息，请稍等...",food:"正在规划第"+A+e(4),hotel:"正在规划第"+A+"天的住宿信息，请稍等...",cost:"正在计算费用预算，请稍等..."};for(const[r,n]of Object.entries(this.loadingStates))if(n)return t[r];return"正在处理中，请稍等..."},currentProgressStep(){const e=m;if(this.contents.length===0)return 1;if(this.isAnyLoading||!this.showBtn)return 2;const A=this[e(5)].some(r=>{const n=e;return r.weatherCompleted===2||r.rentCompleted===2||r[n(6)]===2||r.viewCompleted===2||r.foodCompleted===2||r.hotelCompleted===2||r.costCompleted===2});return this.contents.every(r=>r[e(7)]===2||r.rentCompleted===2||r.drivingCompleted===2||r.viewCompleted===2||r.foodCompleted===2||r.hotelCompleted===2||r.costCompleted===2)&&this.contents.length>0?3:A?2:1}},async mounted(){const e=m;if(typeof window>"u")return;try{await BA.initialize(),BA.setupAutoComplete("start-tipinput","end-tipinput",(t,r)=>{const n=m;this.s_address=t,this.s_location=r||this.getDefaultFormData()[n(8)]},(t,r)=>{this.e_address=t,this.e_location=r||this.getDefaultFormData().e_location}),ns[e(9)]()}catch(t){this.errorMessage=t.message,D[e(10)]("地图初始化失败:",t)}mA.initScrollListener(),mA.setContentCompletedChecker(()=>this.showBtn),mA[e(11)](()=>({selectedDayIndex:this.selectedDayIndex,isAnyLoading:this.isAnyLoading})),this[e(12)](),this.initFloatingButtons(),this.initThemeObserver(),this.shouldShowJournalRecommendation=!1,this.loadPlanningContents()&&(this.shouldContinuePlanning()?this.$nextTick(()=>{setTimeout(()=>{this.askUserToContinuePlanning()},500)}):(this.clearPlanningContents(),this.contents=[],this[e(13)]=-1,this.activeDayDetailIndex=-1,this.expandedSectionType=null,this.showBtn=!0))},beforeUnmount(){const e=m;this.maximizedMapIndex!==-1&&this.restoreMapToOriginalPosition(this.maximizedMapIndex),mA.cleanup(),ns.cleanup(),BA[e(14)](),fe.cleanup(),this.removeKeyboardListener(),this.handleScroll&&typeof window<"u"&&window[e(15)]("scroll",this.handleScroll),this[e(16)]&&this.themeObserver.disconnect()},watch:{contents:{handler(e){e.length>0&&mA.getScrollState().autoScrollEnabled&&this.$nextTick(()=>{this.isAnyLoading&&mA.smartScrollToContent()}),e.length>0&&this.savePlanningContents()},deep:!0},s_address:{handler(){this.saveFormData()}},e_address:{handler(){this.saveFormData()}},s_location:{handler(){this.saveFormData()},deep:!0},e_location:{handler(){this.saveFormData()},deep:!0},startDate:{handler(){this.saveFormData()}},dates:{handler(){this.saveFormData()}},plan_mode:{handler(){this.saveFormData()}},travel_mode:{handler(){this.saveFormData()}},selectedDayIndex:{handler(){this.contents.length>0&&this.savePlanningContents()}},activeDayDetailIndex:{handler(){this.contents.length>0&&this.savePlanningContents()}},expandedSectionType:{handler(){this.contents.length>0&&this.savePlanningContents()}},showBtn:{handler(){this.contents.length>0&&this.savePlanningContents()}}},methods:{parseMarkdown(e){const A=m;try{return os[A(17)](e||"")}catch(t){return console.error("Markdown parsing failed:",t,"Text:",e.substring(0,100)),A(18)+e+"</pre></div>"}},getCurrentProcessingDay(){const e=m;if(!this.contents||this.contents.length===0)return 1;for(let A=this[e(5)].length-1;A>=0;A--){const t=this[e(5)][A];if(t.weatherCompleted>0||t.rentCompleted>0||t.drivingCompleted>0||t.viewCompleted>0||t.foodCompleted>0||t.hotelCompleted>0||t.costCompleted>0)return A+1}return 1},selectDay(e){const A=m;e<0||e>=this.contents[A(19)]||!this.contents[e]||this.isDayCompleted(this.contents[e])&&(this[A(20)]=this.activeDayDetailIndex===e?-1:e,this[A(20)]!==-1&&(this.expandedSectionType=null,this.selectedDayIndex=-1),this.$nextTick(()=>{this.refreshVisibleMaps()}))},handleDetailPanelClick(e){const A=m,t=this[A(20)];t===-1||t>=this.contents.length||t<0||this[A(5)][t]&&(this.expandedSectionType=e,this.selectedDayIndex=t,this[A(21)](()=>{this.scrollToSection(e,t),this.refreshVisibleMaps()}))},scrollToSection(e,A){const t=m;let r="";switch(e){case"weather":r=".weather-header";break;case"rent":r=t(22);break;case"driving":r=".driving-header";break;case"view":r=".view-header";break;case"food":r=".food-header";break;case"hotel":r=t(23);break}if(r){const n=document.querySelector(r);n&&setTimeout(()=>{mA.scrollToElement(n,"smooth")},100)}},shouldShowSection(e,A){if(this.expandedSectionType===null||A>=this.contents.length||A<0)return!1;const t=this.expandedSectionType===e&&(this.selectedDayIndex===-1||this.selectedDayIndex===A);return this.$nextTick(()=>{document.querySelectorAll(".answer-area-container").forEach(n=>{const s=m,a=n.querySelector(".section-content");a&&(window.getComputedStyle(a).display!=="none"?n.classList.add(s(24)):n.classList.remove("expanded"))})}),t},isSectionAvailable(e,A){const t=m;if(A>=this.contents.length||A<0)return!1;const r=this.contents[A];if(!r)return!1;switch(e){case"weather":return A===0&&r.weatherCompleted===2;case"rent":return A===0&&this.travel_mode==="租车"&&r.rentCompleted===2;case"driving":return r[t(6)]===2;case"view":return r.viewCompleted===2;case"food":return r.foodCompleted===2;case"hotel":return r.hotelCompleted===2;default:return!1}},shouldShowSectionHeader(e,A){const t=m;if(this.activeDayDetailIndex===-1||A!==this.activeDayDetailIndex||A>=this.contents.length||A<0)return!1;const r=this.contents[A];if(!r)return!1;switch(e){case t(25):return A===0&&r.weatherCompleted!==0;case"rent":return A===0&&this[t(26)]==="租车"&&r.rentCompleted!==0;case"driving":return r[t(6)]!==0;case"view":return r.viewCompleted!==0;case t(27):return r.foodCompleted!==0;case"hotel":return r.hotelCompleted!==0;case t(28):return r.costCompleted!==0&&(A===this.contents.length-1||this.contents.every(n=>n&&(n.drivingCompleted===2||n.viewCompleted===2)));default:return!1}},handleSectionHeaderClick(e,A){const t=m;this.expandedSectionType===e&&this.selectedDayIndex===A?(this[t(29)]=null,this.selectedDayIndex=-1):(this.expandedSectionType=e,this.selectedDayIndex=A,this.activeDayDetailIndex=A,this.$nextTick(()=>{this.refreshVisibleMaps()}))},collapseAll(){this.expandedSectionType=null,this.selectedDayIndex=-1,this.activeDayDetailIndex=-1,this.$nextTick(()=>{document[m(30)](".answer-area-container").forEach(t=>{t.classList.remove("expanded")}),this.refreshVisibleMaps()})},async temporarilyExpandSection(e,A,t=2e3){const r=m,n=this.expandedSectionType,s=this[r(13)];return this.expandedSectionType=e,this.selectedDayIndex=A,await this.$nextTick(),new Promise(a=>{setTimeout(()=>{this.expandedSectionType=n,this.selectedDayIndex=s,a()},t)})},refreshVisibleMaps(){typeof window>"u"||this.contents.forEach((e,A)=>{const t=m;if("amap"in e&&(this.selectedDayIndex===-1||this.selectedDayIndex===A)){const n=document.querySelector("#map-container-"+A);n&&n[t(31)]!==null&&setTimeout(()=>{this.forceMapResize(A)},100)}})},isDayCompleted(e){return e?e.weatherCompleted===2||e.rentCompleted===2||e.drivingCompleted===2||e.viewCompleted===2||e.foodCompleted===2||e.hotelCompleted===2||e.costCompleted===2:!1},getDayTitle(e,A){const t=m;return A===0?this.s_address+" → "+this[t(32)]:A===this.contents.length-1&&this.plan_mode==="往返"?this.e_address+" → "+this.s_address:this.e_address+"游览"},setLoadingState(e,A){this.loadingStates[e]=A},resetAllLoadingStates(){this.loadingStates={weather:!1,rent:!1,driving:!1,view:!1,food:!1,hotel:!1,cost:!1}},scrollPageToBottom(){mA.smartScrollToContent()},resetScrollState(){mA.resetScrollState()},saveFormData(){const e={s_address:this.s_address,e_address:this.e_address,startDate:this.startDate,dates:this.dates,plan_mode:this.plan_mode,travel_mode:this.travel_mode,s_location:this.s_location,e_location:this.e_location};RA.saveFormData(e)},loadFormData(){const e=RA.loadFormData();if(e){const A=RA.mergeFormData({s_address:this.s_address,e_address:this.e_address,startDate:this.startDate,dates:this.dates,plan_mode:this.plan_mode,travel_mode:this.travel_mode,s_location:this.s_location,e_location:this.e_location},e);Object.assign(this,A)}},clearFormData(){RA.clearFormData()},resetFormData(){const e=RA.resetFormData();Object.assign(this,e),this.clearPlanningContents(),this.contents=[],this.selectedDayIndex=-1,this.activeDayDetailIndex=-1,this.expandedSectionType=null,this.showBtn=!0},savePlanningContents(){const e=m;if(!(typeof window>"u"))try{const A={contents:this.contents,last_start:this.last_start,last_end:this.last_end,selectedDayIndex:this.selectedDayIndex,activeDayDetailIndex:this[e(20)],expandedSectionType:this.expandedSectionType,rent_requirements:this.rent_requirements,rent_customized:this[e(33)],plan_requirements:this.plan_requirements,plan_customized:this.plan_customized,hotel_requirements:this.hotel_requirements,hotel_customized:this.hotel_customized,showBtn:this.showBtn,formData:{s_address:this.s_address,e_address:this.e_address,startDate:this.startDate,dates:this.dates,plan_mode:this.plan_mode,travel_mode:this.travel_mode,s_location:this.s_location,e_location:this.e_location},timestamp:Date.now()};localStorage.setItem("topmeans_planning_contents",JSON.stringify(A))}catch(A){console.warn(e(34),A)}},loadPlanningContents(){const e=m;if(typeof window>"u")return!1;try{const A=localStorage[e(35)]("topmeans_planning_contents");if(!A)return!1;const t=JSON.parse(A),r=24*60*60*1e3;return t.timestamp&&Date[e(36)]()-t.timestamp>r?(this.clearPlanningContents(),!1):(this.contents=t.contents||[],this.last_start=t.last_start||"",this.last_end=t.last_end||"",this.selectedDayIndex=t.selectedDayIndex!==void 0?t.selectedDayIndex:-1,this.activeDayDetailIndex=t.activeDayDetailIndex!==void 0?t.activeDayDetailIndex:-1,this.expandedSectionType=t.expandedSectionType||null,this.rent_requirements=t.rent_requirements||"",this.rent_customized=t.rent_customized||!1,this.plan_requirements=t.plan_requirements||"0",this[e(37)]=t.plan_customized||!1,this.hotel_requirements=t.hotel_requirements||"",this.hotel_customized=t.hotel_customized||!1,this.showBtn=t.showBtn!==void 0?t.showBtn:!0,t.formData&&(this.s_address=t.formData.s_address||this.s_address,this.e_address=t.formData.e_address||this.e_address,this.startDate=t.formData.startDate||this.startDate,this.dates=t[e(38)].dates||this.dates,this.plan_mode=t.formData.plan_mode||this[e(39)],this.travel_mode=t.formData.travel_mode||this.travel_mode,this.s_location=t.formData.s_location||this.s_location,this.e_location=t.formData.e_location||this.e_location),!0)}catch(A){return console[e(40)]("加载规划内容失败:",A),this.clearPlanningContents(),!1}},clearPlanningContents(){if(!(typeof window>"u"))try{localStorage.removeItem("topmeans_planning_contents")}catch(e){console.warn("清除规划内容失败:",e)}},hasSavedPlanningContents(){const e=m;if(typeof window>"u")return!1;try{const A=localStorage.getItem("topmeans_planning_contents");if(!A)return!1;const t=JSON[e(17)](A),r=24*60*60*1e3;return t.timestamp&&Date.now()-t.timestamp>r?!1:t.contents&&t[e(5)].length>0}catch{return!1}},updateRentRequirements(e){this.rent_requirements=e.target.value},updateHotelRequirements(e){this.hotel_requirements=e.target.value},toggleMapSize(e){this.maximizedMapIndex===e?(this.restoreMapToOriginalPosition(e),this.maximizedMapIndex=-1,this.removeKeyboardListener()):(this.moveMapToBodyForMaximize(e),this.maximizedMapIndex=e,this.addKeyboardListener()),this.$nextTick(()=>{setTimeout(()=>{this.forceMapResize(e)},350)})},moveMapToBodyForMaximize(e){const A=m;if(typeof window>"u")return;const t=document[A(41)]("#map-container-"+e)[A(42)](".map-wrapper");t&&(this.originalParent=t[A(43)],this[A(44)]=t.nextSibling,document.body.appendChild(t))},restoreMapToOriginalPosition(e){const A=m;if(typeof window>"u")return;const t=document.querySelector("#map-container-"+e).closest(".map-wrapper");t&&this.originalParent&&(this.originalNextSibling?this.originalParent.insertBefore(t,this[A(44)]):this.originalParent[A(45)](t),this.originalParent=null,this[A(44)]=null)},forceMapResize(e){try{const A=BA.getMapInstance(e);if(!A)return;typeof A.resize=="function"&&A.resize();const t=A.getCenter(),r=A.getZoom();typeof A.getSize=="function"&&A.getSize(),typeof A.setFitView=="function"&&A.setFitView(),setTimeout(()=>{try{t&&r&&typeof A.setZoomAndCenter=="function"&&A.setZoomAndCenter(r,t),setTimeout(()=>{const n=m;try{if(typeof A.getZoom=="function"&&typeof A.setZoom=="function"){const s=A.getZoom();A.setZoom(s+.01),setTimeout(()=>{A.setZoom(s)},50)}}catch(s){console.warn("地图 "+e+n(46),s)}},100)}catch(n){console.warn("地图 "+e+" 中心点设置时出现错误:",n)}},100)}catch(A){console.warn("地图 "+e+" 重新渲染时出现错误:",A)}},addKeyboardListener(){const e=m;typeof window<"u"&&(this.escKeyHandler=A=>{A.key==="Escape"&&this.maximizedMapIndex!==-1&&this.toggleMapSize(this.maximizedMapIndex)},this.resizeHandler=()=>{this.maximizedMapIndex!==-1&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(()=>{this.forceMapResize(this.maximizedMapIndex)},300))},window.addEventListener("keydown",this.escKeyHandler),window[e(47)]("resize",this.resizeHandler))},removeKeyboardListener(){const e=m;typeof window!==e(48)&&(this.escKeyHandler&&(window.removeEventListener("keydown",this.escKeyHandler),this.escKeyHandler=null),this[e(49)]&&(window.removeEventListener("resize",this.resizeHandler),this.resizeHandler=null),this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null))},handleRentCustomize(e){const A=m;this.rent_requirements&&this[A(50)].trim()&&this.handleActionClick("rent",e)},async drivingPlanning(e,A,t,r,n){const s=m;try{await this.$nextTick(),await this.$nextTick(),await BA[s(51)](e,A,t,r,n),await new Promise(a=>setTimeout(a,2e3))}catch(a){D.error("路线生成错误",a),this.errorMessage=a.message||"路线生成失败"}finally{this.loading=!1}},async savePlanToDB(e,A,t){const r=m;await this.saveMapAsImage(e,A,t);let n=`# Smart Travel Plan

## `+this.s_address+" 到 "+this.e_address+`

`;if(this.contents[e].weather&&(n+=this.contents[e].weather+`

`),this.contents[e].rent&&(n+=this.contents[e].rent+`

`),this.contents[e].driving&&(n+=this.contents[e].driving+`

`,n+="![路线规划](./map-"+t+"-"+e+`.png)

`),this.contents[e].view)for(let a=0;a<this.contents[e].view.length;a++)n+="## "+this.contents[e].view[a][r(52)]+`

`,this.contents[e].view[a].url&&(n+="!["+this[r(5)][e].view[a].name+"]("+this.contents[e].view[a].url+`)

`),n+=this.contents[e].view[a].info+`

`;if(this.contents[e].food)for(let a=0;a<this.contents[e][r(27)].length;a++)n+=r(53)+this.contents[e].food[a][r(52)]+`

`,this.contents[e].food[a].url&&(n+="!["+this[r(5)][e].food[a].name+"]("+this.contents[e].food[a].url+`)

`),n+=this.contents[e].food[a].info+`

`;if(this.contents[e].hotel)for(let a=0;a<this.contents[e].hotel[r(19)];a++)this.contents[e].hotel[a].url?n+="## [携程直达："+this.contents[e].hotel[a].name+"]("+this.contents[e].hotel[a].url+' "'+this.contents[e].hotel[a].name+`")

`:n+="## "+this.contents[e][r(54)][a].name+`

`,n+=this.contents[e].hotel[a][r(55)]+`

`;let s=await fetch(re+"/api/save_plan",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:n,user:A,filename:"plan-"+t+"-"+e+".md"})});if(!s.ok)throw D.error("保存计划失败，请检查网络连接",s),new Error("保存计划失败，请检查网络连接");if(s=await fetch(re+"/api/user/add_plan",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({account:A,create_time:t,days:this.dates})}),!s.ok)throw D.error("计划存库失败，请检查网络连接",s),new Error("计划存库失败，请检查网络连接")},async saveMapAsImage(e,A,t){try{await BA.saveMapAsImage(e,A,t,this)}catch(r){throw D.error("保存地图为图片失败:",r),r}},async getHotelUrl(e,A,t,r){try{return await fe.getHotelUrl(e,A,t,r,"")}catch(n){throw D.error("酒店信息获取失败:",n,e),n}},async getFoodImgUrl(e,A){try{return await fe.getFoodImgUrl(e,A)}catch(t){throw D.error("美食图片获取失败:",t),t}},async getViewUrl(e){const A=m;try{return await fe[A(56)](e)}catch(t){throw D.error("景点信息获取失败:",t),t}},async askDeepSeek(e,A,t){const r=m,n=Qe(),{user:s}=await n.getUserInfo(),a=this.getFormattedDate();try{const o=await fetch(re+"/api/ds",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({msg:A})});if(!o.ok)throw D.error("DS API 请求失败:"+o.statusText),new Error(r(57));const i=o.body.getReader(),l=new TextDecoder("utf-8");let c="";for(;;){const{done:x,value:d}=await i.read();if(x)break;const f=l.decode(d),u=f.split(`
`).filter(h=>h.trim());for(const h of u)try{if(!h.startsWith("data: "))continue;const v=h.slice(6);if(v==="[DONE]")break;const w=JSON.parse(v),Q=w.choices[0].delta.content;Q&&(c+=Q),t==="rent"?this.contents[e].rent=`**租车建议**
`+c:t==="plan"&&(this.contents[e].plan="**第"+(e+1)+`天的规划**

`+c)}catch(v){D.error("解析数据失败:",v)}}if(c&&t==="hotel"){const x=c.split(r(58));for(let d=0;d<x[r(19)];d++)if(d!==0&&(d>this.contents[e].hotel.length&&this.contents[e][r(54)].push({}),this.contents[e].hotel[d-1].info=`**酒店信息:** 
`,x[d].includes(r(59)))){this.contents[e].hotel[d-1].info+=x[d].split("**酒店信息:**")[1].trim(),this.contents[e].hotel[d-1].name=x[d].split("@@")[1].split("$$")[0].trim();try{this.contents[e].hotel[d-1].url=await this.getHotelUrl(this.contents[e][r(54)][d-1].name,s.account,a,e+1)}catch{}}}}catch(o){throw D.error("DS API 请求失败:",o),o}},async askQwen(e,A){const t=m;let r="";try{const n=await fetch(re+"/api/qwen",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({msg:A})});if(!n.ok)throw new Error("HTTP error! status: "+n.status);const s=n.body.getReader(),a=new TextDecoder;for(;;){const{done:o,value:i}=await s[t(60)]();if(o)break;const l=a.decode(i);r+=l,this.contents[e].think=r.replace(/%SP/g,"").replace(/%SC/g,"").replace(/%EP/g,"").replace(/%EC/g,"").replace(/%CIR/g,"").replace(/%PT/g,"").replace(/@/g,"").replace(/#/g,"").replace(/\$/g,"")[t(61)](/\^/g,"")[t(61)](/\*/g,"").replace(/%/g,"").replace(/&/g,"")[t(62)]()}}catch(n){throw D.error("Qwen API 请求失败:",n),n}return r},async askDS(e,A){const t=m;let r="";try{const n=await fetch(re+"/api/ds",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({msg:A})});if(!n.ok)throw D.error("DS API 请求失败!"),new Error("DS API 请求失败!");const s=n.body.getReader(),a=new TextDecoder("utf-8");for(;;){const{done:o,value:i}=await s.read();if(o)break;const l=a.decode(i),c=l.split(`
`).filter(x=>x.trim());for(const x of c)try{if(!x.startsWith("data: "))continue;const d=x.slice(6);if(d==="[DONE]")break;const f=JSON.parse(d),u=f.choices[0].delta.content;u&&(r+=u),this.contents[e].think=r.replace(/%SP/g,"").replace(/%SC/g,"")[t(61)](/%EP/g,"").replace(/%EC/g,"").replace(/%CIR/g,"").replace(/@/g,"").replace(/#/g,"").replace(/\$/g,"").replace(/\^/g,"").replace(/\*/g,"").replace(/%/g,"")[t(61)](/&/g,"").trim()}catch{}}}catch(n){throw D.error("DS API 请求失败:"+n),n}return r},async handleActionClick(e,A){const t=m;let r=!1;const n=Qe(),{user:s}=await n.getUserInfo(),a=this.getFormattedDate();let o=Ft;if(o=o[t(61)](/startDate/g,this.startDate).replace(/s_address/g,this.s_address).replace(/e_address/g,this.e_address).replace(/plan_mode/g,this.plan_mode).replace(/travel_mode/g,this.travel_mode).replace(/dates/g,this.dates),e==="rent"){if(this.rent_requirements){this.contents[A].rentCompleted=1;const i=o+ss.replace(/customized_rent_prompt/g,this.rent_requirements||"");this.contents[A].rent="",await this.planningRent(A,s,a,i),this.contents[A][t(63)]=2,this.rent_customized=!0,r=!0}}else if(e==="driving"){if(this.plan_requirements){this.contents[A].drivingCompleted=1;let i="";A>0&&(i="注意，第"+(A-1)+"天路线已完成规划，起点是"+this.last_start+", 终点是"+this.last_end+"，因此今天的起点就是"+this.last_end+"，最终的目的地是"+this.e_address);const l=o+as.replace(/index/g,A+1).replace(/driving_mid_s_e_prompt/g,i).replace(/customized_driving_prompt/g,this.selectedPlanText||"");this.contents[A].driving="";const c=parseInt(this.plan_requirements,10);await this.planningDriving(A,s,a,l,c),this.contents[A].drivingCompleted=2,this.plan_customized=!0,r=!0}}else if(e==="hotel"&&this[t(64)]){this.contents[A].hotelCompleted=1;const i=o+is.replace(/e_address/g,this.e_address)[t(61)](/customized_hotel_prompt/g,this.hotel_requirements||"");for(let l=0;l<this.contents[A].hotel.length;l++)this.contents[A][t(54)][l].info="",this.contents[A].hotel[l].name="",this[t(5)][A].hotel[l].url="";await this[t(65)](A,s,a,i),this.contents[A].hotelCompleted=2,this.hotel_customized=!0,r=!0}r&&this.showBtn&&(await this.savePlanToDB(A,s.account,a),this.savePlanningContents())},validateFormData(){const e=m,A={s_address:this[e(66)],e_address:this.e_address,startDate:this.startDate,dates:this.dates,plan_mode:this.plan_mode,travel_mode:this.travel_mode,s_location:this.s_location,e_location:this.e_location},t=RA.validateFormData(A);return!t.isValid&&alert(t.message),t.isValid},getDefaultFormData(){return RA.getDefaultFormData()},getLocationSummary(e){const A=m;if(!e)return"未设置";const t=[];return e.province&&t.push(e.province),e.city&&t.push(e.city),e.district&&t.push(e.district),t.length>0?t[A(67)](" "):e.address||"位置信息不完整"},hasCompleteLocation(e){return e&&e.lng&&e.lat||e&&e.city&&e.province},getFormattedDate(){const e=m,A=new Date,t=A.getFullYear(),r=String(A.getMonth()+1).padStart(2,"0"),n=String(A.getDate()).padStart(2,"0"),s=""+t+r+n,a=""+String(A[e(68)]()).padStart(2,"0")+String(A.getMinutes()).padStart(2,"0")+String(A.getSeconds()).padStart(2,"0");return""+s+a},async planningWeather(e,A,t,r){const n=m;this[n(5)][e][n(7)]=1,this.setLoadingState("weather",!0),this.expandedSectionType="weather",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this[n(21)]();const s=r+WB+ne;this.contents[e].weather=(await this.askQwen(e,s)).replace(/```markdown/g,"")[n(61)](/```/g,""),this.contents[e].weatherCompleted=2,this.setLoadingState("weather",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningRent(e,A,t,r){const n=m;this.contents[e].rentCompleted=1,this.setLoadingState("rent",!0),this.expandedSectionType="rent",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick();const s=ss.replace(/customized_rent_prompt/g,this.rent_requirements||""),a=r+s+ne;this.contents[e].rent=(await this[n(69)](e,a)).replace(/```markdown/g,"").replace(/```/g,""),this.contents[e].rentCompleted=2,this.setLoadingState(n(70),!1),this.expandedSectionType=null,this[n(13)]=-1},async planningDriving(e,A,t,r,n){const s=m;this.contents[e].drivingCompleted=1,this.setLoadingState("driving",!0),this.expandedSectionType="driving",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick();let a="";e>0&&e<this.dates-1||e>0&&e===this.dates-1&&this.plan_mode==="单程"?a="注意，第"+e+"天路线已完成规划，起点是"+this.last_start+", 终点是"+this.last_end+"，因此今天的起点就是"+this.last_end+"，最终的目的地是"+this.e_address:e===this[s(71)]-1&&this.dates>1&&this[s(39)]==="往返"&&(a="注意，第"+e+"天路线已完成规划，起点是"+this.last_start+", 终点是"+this.last_end+"，因此今天的起点就是"+this.last_end+"，最终的目的地是"+this.s_address);let o="";for(let g=0;g<this.dates;++g)if(this.contents[g].circle)for(let b=0;b<this.contents[g].circle[s(19)];b++)o+=this.contents[g].circle[b]+",";o&&(o="注意，我已经去过"+o+"这几个地方了，规划环线时不要重复，");const i=as.replace(/index/g,e+1).replace(/driving_mid_s_e_prompt/g,a).replace(/last_circle_prompt/g,o).replace(/customized_driving_prompt/g,this.selectedPlanText||""),l=r+i+ne,c=await this.askDS(e,l);this.contents[e][s(72)]=c.replace(/%SP/g,"").replace(/%SC/g,"").replace(/%EP/g,"").replace(/%EC/g,"")[s(61)](/%CIR/g,"").replace(/\^\^/g,"").replace(/\@\@/g,"").replace(/```markdown/g,"").replace(/```/g,"");const x=c.split("^^")[1][s(73)]("^^")[0].trim(),d=c.split(s(74))[1].split("%SP")[0].trim(),f=c.split("%SC")[1].split(s(75))[0].trim(),u=c.split("@@")[1].split("@@")[0].trim(),h=c.split(s(76))[1][s(73)]("%EP")[0].trim(),v=c.split("%EC")[1].split("%EC")[0][s(62)]();this.last_start=x,this.last_end=u;let w={lng:null,lat:null},Q={lng:null,lat:null};const y=await BA.getAccurateCoordinates(x,d,f),p=await BA.getAccurateCoordinates(u,h,v);!y[s(77)]||!y.lat?w={lng:null,lat:null}:w={lng:y.lng,lat:y.lat},!p[s(77)]||!p.lat?Q={lng:null,lat:null}:Q={lng:p.lng,lat:p.lat};let C=[];if(x===u){const g=c.split("%CIR")[1],b=g.split(" → ");this.contents[e].circle=[];for(let L=1;L<b.length-1;++L){const E=b[L].trim(),k=await BA.getAccurateCoordinates(E,h,v);this.contents[e].circle.push(E),k&&k.lng&&k.lat?C[s(78)]([k.lng,k.lat]):console.warn("环线地点地理编码失败: "+E+"，跳过该地点")}}if(this.contents[e].pos={s_address:x,s_province:d,s_city:f,e_address:u,e_province:h,e_city:v,s_location:{s_lng:w.lng,s_lat:w.lat},e_location:{e_lng:Q.lng,e_lat:Q.lat},circle_locations:C},this.contents[e].amap="",w.lng&&w.lat&&Q.lng&&Q.lat){await this.$nextTick(),await this.$nextTick();try{await this.drivingPlanning(e,{s_lng:w.lng,s_lat:w[s(79)]},{e_lng:Q.lng,e_lat:Q.lat},n,C),await new Promise(g=>setTimeout(g,1e3))}catch(g){D.error("第"+(e+1)+"天导航规划失败:",g)}}else console.warn("第"+(e+1)+"天地理编码失败，跳过地图规划，但继续其他规划流程");this.contents[e].drivingCompleted=2,this.setLoadingState("driving",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningView(e,A,t,r){const n=m;this.contents[e].viewCompleted=1,this.setLoadingState("view",!0),this.expandedSectionType="view",this[n(13)]=e,this.activeDayDetailIndex=e,await this.$nextTick(),this.contents[e].view=[];let s=YB.replace(/e_address/g,this.last_end);if(e>0){let l="";for(let c=0;c<e;++c)for(let x=0;x<this.contents[c].view.length;++x)l+=this.contents[c].view[x].name+",";s=s.replace(/last_view_prompt/g,n(80)+l+"这几个地方了，不要重复，")}else s=s.replace(/last_view_prompt/g,"");const a=s+ne,o=await this.askQwen(e,a),i=o.split("@@@@");for(let l=0;l<i.length;l++)if(l!==0){l>this.contents[e].view.length&&this.contents[e][n(81)].push({}),i[l].includes("^^")?this.contents[e].view[l-1].name=i[l].split("^^")[1].split("^^")[0].trim():i[l].includes("$$")&&(this.contents[e].view[l-1].name=i[l].split("$$")[1].split("$$")[0].trim()),this.contents[e].view[l-1].info=i[l].replace(/\^\^.*\^\^/g,"").replace(/\$\$.*\$\$/g,"").replace(/%PT/g,"").replace(/PT/g,"")[n(62)](),this.contents[e][n(81)][l-1].prompt=i[l].split("%PT")[1].split("%PT")[0].trim();try{this.contents[e].view[l-1].url=await fe.getAIImg("view",this.last_end,this.contents[e].view[l-1].name,this.contents[e].view[l-1].prompt)}catch{this.contents[e].view[l-1].url=""}}this.contents[e].viewCompleted=2,this[n(82)]("view",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningFood(e,A,t,r){const n=m;this.contents[e].foodCompleted=1,this.setLoadingState("food",!0),this.expandedSectionType="food",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick(),this.contents[e].food=[];let s=ZB.replace(/e_address/g,this[n(83)]);if(e>0){let l="";for(let c=0;c<e;++c)for(let x=0;x<this.contents[c].food.length;++x)l+=this.contents[c].food[x].name+",";s=s.replace(/last_food_prompt/g,"注意，我已经吃过"+l+"这几个美食了，不要重复，")}else s=s.replace(/last_food_prompt/g,"");const a=s+ne,o=await this.askQwen(e,a),i=o[n(73)]("@@@@");for(let l=0;l<i.length;l++)if(l!==0){l>this.contents[e].food.length&&this.contents[e].food.push({}),i[l].includes("^^")?this.contents[e].food[l-1].name=i[l].split("^^")[1].split("^^")[0].trim():i[l].includes("$$")&&(this[n(5)][e].food[l-1].name=i[l].split("$$")[1].split("$$")[0].trim()),this.contents[e].food[l-1][n(55)]=i[l].replace(/\^\^/g,"").replace(/\$\$/g,"").replace(/%PT/g,"").trim(),this.contents[e][n(27)][l-1].prompt=i[l].split("%PT")[1].split(n(84))[0].trim();try{this.contents[e].food[l-1].url=await fe[n(85)]("food",this.last_end,this.contents[e][n(27)][l-1].name,this[n(5)][e].food[l-1].prompt)}catch{this.contents[e].food[l-1].url=""}}this.contents[e].foodCompleted=2,this.setLoadingState("food",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningHotel(e,A,t,r){const n=m;this.contents[e].hotelCompleted=1,this.setLoadingState("hotel",!0),this.expandedSectionType="hotel",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick(),this[n(5)][e].hotel=[];const s=is.replace(/e_address/g,this.last_end).replace(/customized_hotel_prompt/g,this.hotel_requirements||""),a=s+ne,o=await this[n(69)](e,a),i=o.split("@@@@");for(let l=0;l<i.length;l++)if(l!==0){l>this[n(5)][e].hotel.length&&this.contents[e].hotel.push({}),this.contents[e].hotel[l-1].name=i[l].split("^^")[1].split("^^")[0].trim(),this.contents[e].hotel[l-1].info=i[l].replace(/\^\^.*\^\^/g,"").replace(/\$\$.*\$\$/g,"").trim();try{this.contents[e].hotel[l-1].url=await this.getHotelUrl(this.contents[e].hotel[l-1].name,A.account,t,l)}catch{this.contents[e].hotel[l-1][n(86)]="https://www.ctrip.com"}}this.contents[e].hotelCompleted=2,this.setLoadingState("hotel",!1),this[n(29)]=null,this.selectedDayIndex=-1},async planningCost(e,A,t,r){const n=m;this.contents[e][n(87)]=1,this.setLoadingState("cost",!0),this.expandedSectionType="cost",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick();const s=r+zB+ne;this.contents[e][n(28)]=await this.askDS(e,s),this.contents[e][n(87)]=2,this.setLoadingState("cost",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningNew(){const e=m,A=Qe();if(!A.checkLoginStatus()){alert("请先登录");return}const{user:t}=await A.getUserInfo(),r=this.dates*2;try{const a=await fetch(re+"/api/user/balance",{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer "+A.token}});if(a.ok){const o=await a.json(),i=o.balance||0;if(i>=r)if(confirm("检测到您的账户余额为 ¥"+i+"，本次规划需要支付 ¥"+r+`。

是否使用余额完成支付？

点击"确定"使用余额支付
点击"取消"选择其他支付方式`))try{const c=await fetch(re+"/api/user/deduct-balance",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer "+A.token},body:JSON.stringify({amount:r,description:"旅游规划服务 - "+this.dates+"天"})});if(c.ok){const x=await c.json();if(x.success)console.log("余额支付成功，开始规划");else throw new Error(x[e(88)]||"余额扣除失败")}else throw new Error("余额扣除请求失败")}catch(c){console.error("余额支付失败:",c),alert("余额支付失败，请选择其他支付方式"),this[e(89)](r);return}else{this.showPaymentWindow(r);return}else{this.showPaymentWindow(r);return}}else{console[e(40)]("获取用户余额失败，显示支付窗口"),this.showPaymentWindow(r);return}}catch(a){console.error("获取用户余额出错:",a),this.showPaymentWindow(r);return}if(this.hasSavedPlanningContents()){if(this.loadPlanningContents()&&this.shouldContinuePlanning()&&confirm(`检测到有未完成的规划内容，是否继续之前的规划？

点击"确定"继续之前的规划
点击"取消"开始新的规划`)){this.showBtn=!1,await this[e(21)](),setTimeout(()=>{this[e(90)](!1)},500);return}this.resetPageContentAndPlanning()}if(!this.validateFormData())return;if(!this.hasCompleteLocation(this.s_location)){alert("为避免地点位置错误，请根据弹出的地点信息选择起点");return}if(!this.hasCompleteLocation(this.e_location)){alert("为避免地点位置错误，请根据弹出的地点信息选择终点");return}this[e(91)]=!1,this.last_start=this.s_address,this.last_end=this.e_address,this.resetScrollState(),this.clearPlanningContents(),this.contents=[];for(let a=0;a<this.dates;a++)this.contents.push({weatherCompleted:0,rentCompleted:0,drivingCompleted:0,viewCompleted:0,hotelCompleted:0,foodCompleted:0,costCompleted:0});const n=this.getFormattedDate();let s=Ft;s=s.replace(/startDate/g,this.startDate).replace(/s_address/g,this.s_address).replace(/e_address/g,this.e_address).replace(/plan_mode/g,this.plan_mode).replace(/travel_mode/g,this.travel_mode).replace(/dates/g,this.dates);try{await this.planningWeather(0,t,n,s)}catch(a){D.error("天气规划失败："+a),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("天气信息获取失败，请检查网络连接后重试");return}if(this.travel_mode==="租车")try{await this.planningRent(0,t,n,s)}catch(a){D.error("租车方案规划失败："+a),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("租车方案规划失败，请检查网络连接后重试");return}for(let a=0;a<this.dates;a++){try{await this.planningDriving(a,t,n,s)}catch(o){D.error("路线规划失败："+o),this.planningFinishProc(),this.resetScrollState(),this[e(5)]=[],this[e(92)]("路线规划失败，请检查网络连接后重试");return}if(a!==this.dates-1||a===0||this.plan_mode==="单程"){try{await this.planningView(a,t,n,s)}catch(o){D[e(10)]("景点规划失败："+o),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("景点推荐规划失败，请检查网络连接后重试");return}try{await this.planningFood(a,t,n,s)}catch(o){D.error("美食规划失败："+o),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("美食推荐规划失败，请检查网络连接后重试");return}try{await this.planningHotel(a,t,n,s)}catch(o){D[e(10)]("住宿规划失败："+o),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("住宿推荐规划失败，请检查网络连接后重试");return}}try{await this.savePlanToDB(a,t.account,n)}catch(o){D.error("旅游规划存库失败："+o)}}await this.planningFinishProc(),this.showCompletionModal=!0},async planningFinishProc(){this.showBtn=!0,this.selectedDayIndex=0,this.shouldShowJournalRecommendation=!0,this.activeDayDetailIndex=0,this.expandedSectionType=null,await this.$nextTick(),this.refreshVisibleMaps(),this.isPlanningFullyCompleted()&&this.clearPlanningContents()},handleCompletionModalConfirm(){this.showCompletionModal=!1,this.clearPlanningContents(),this.$nextTick(()=>{const e=m,A=document.querySelector(".day-navigation");if(A){const t=A.offsetTop-100;window[e(93)]({top:Math.max(0,t),behavior:"smooth"})}})},handleFailureModalConfirm(){this.showFailureModal=!1},showPlanningFailure(e){this.failureReason=e,this.showFailureModal=!0},initFloatingButtons(){const e=m;typeof window>"u"||(this.handleScroll=this.throttle(()=>{const A=m,t=window.pageYOffset||document.documentElement.scrollTop,r=document.documentElement.scrollHeight,n=document.documentElement.clientHeight;this[A(94)]=t>200&&r>n+400},100),window.addEventListener(e(95),this[e(96)],{passive:!0}))},scrollToTop(){const e=m,A=document.querySelector(".btn-planning"),t=document.querySelector(".btn-reset");let r=null;if(A)r=A.getBoundingClientRect();else if(t)r=t[e(97)]();else{window.scrollTo({top:0,behavior:"smooth"});return}const n=window.pageYOffset||document[e(98)].scrollTop,s=r.top+n,a=window.innerHeight,o=s-a*.3;window.scrollTo({top:Math.max(0,o),behavior:e(99)})},scrollToBottom(){window.scrollTo({top:document.documentElement.scrollHeight,behavior:"smooth"})},throttle(e,A){let t;return function(...n){const s=()=>{clearTimeout(t),e.apply(this,n)};clearTimeout(t),t=setTimeout(s,A)}},initThemeObserver(){const e=m;typeof window!==e(48)&&(this.themeObserver=new MutationObserver(A=>{A.forEach(t=>{t.type==="attributes"&&t.attributeName==="class"&&this.updateCustomSuggestTheme()})}),this.themeObserver.observe(document[e(98)],{attributes:!0,attributeFilter:["class"]}))},updateCustomSuggestTheme(){document.querySelectorAll(".custom-amap-suggest").forEach(A=>{BA.updateThemeStyles&&BA.updateThemeStyles(A)})},async handleContentRestoration(e=!0){const A=m;try{await this.restoreMapInstances(),this.restoreScrollManagerState();let t=!1;e&&(t=this.shouldContinuePlanning(),t&&await this.continuePlanning()),(!e||!t)&&(this.restoreCompletedState(),await this.planningFinishProc(),this.showCompletionModal=!0)}catch{this.clearPlanningContents(),this.contents=[],this.selectedDayIndex=-1,this.showBtn=!0,this[A(100)](),alert("恢复规划内容失败，请重新开始规划")}},async restoreMapInstances(){if(!(!this.contents||this.contents.length===0)){await this.$nextTick(),await this.$nextTick();try{await BA.initialize()}catch(e){console.warn("地图服务重新初始化失败:",e)}for(let e=0;e<this.contents.length;e++)if(this.contents[e].drivingCompleted===2)try{await new Promise(r=>setTimeout(r,200*e));const t=document.querySelector("#map-container-"+e);t?(t.innerHTML="",await this.$nextTick(),await this.regenerateMapRoute(e)):console.warn("第"+(e+1)+"天地图容器不存在")}catch(t){console.warn("地图 "+(e+1)+" 恢复失败:",t)}setTimeout(()=>{this[m(101)](),setTimeout(()=>{this.forceRefreshAllMaps()},3e3)},2e3)}},async regenerateMapRoute(e){const A=m;try{const t=this.contents[e];if(!t||!t.driving){console.warn("第"+(e+1)+"天没有路线规划数据");return}if(this.contents[e][A(102)].s_location&&this.contents[e][A(102)].e_location&&this[A(5)][e].pos.s_location.s_lng&&this.contents[e].pos.s_location.s_lat&&this.contents[e].pos.e_location.e_lng&&this.contents[e].pos.e_location.e_lat){const r=parseInt(this.plan_requirements||"0",10);await this.$nextTick(),await this.$nextTick(),await new Promise(n=>setTimeout(n,500)),await this.drivingPlanning(e,this.contents[e].pos.s_location,this.contents[e].pos.e_location,r,this.contents[e].pos.circle_locations),setTimeout(()=>{this.forceMapResize(e)},1e3)}else console.warn("第"+(e+1)+"天坐标信息无效，无法生成地图")}catch(t){console.error("第"+(e+1)+"天地图路线重新生成失败:",t),setTimeout(()=>{try{document.querySelector("#map-container-"+e)&&this.forceMapResize(e)}catch(r){console.error("第"+(e+1)+"天地图resize失败:",r)}},1e3)}},restoreScrollManagerState(){mA.resetScrollState()},isPlanningFullyCompleted(){if(!this.contents||this.contents.length===0)return!1;for(let e=0;e<this.contents.length;e++){const A=this.contents[e],t=["weatherCompleted","rentCompleted","drivingCompleted","viewCompleted","foodCompleted","hotelCompleted","costCompleted"];for(const r of t)if(A[r]===1)return!1;if(this.hasIncompleteStages(A,e))return!1}return!0},shouldContinuePlanning(){return!this.isPlanningFullyCompleted()},hasIncompleteStages(e,A){const t=m;return A===0&&e.weatherCompleted===0||A===0&&this.travel_mode==="租车"&&e.rentCompleted===0||e.drivingCompleted===0||(A!==this[t(71)]-1||A===0||this.plan_mode==="单程")&&(e.viewCompleted===0||e.foodCompleted===0||e.hotelCompleted===0)},async continuePlanning(){const e=m;try{const A=Qe(),{user:t}=await A.getUserInfo(),r=this.getFormattedDate();let n=Ft;n=n.replace(/startDate/g,this.startDate)[e(61)](/s_address/g,this[e(66)]).replace(/e_address/g,this.e_address).replace(/plan_mode/g,this.plan_mode).replace(/travel_mode/g,this[e(26)]).replace(/dates/g,this.dates);for(let s=0;s<this.contents.length;s++){const a=this.contents[s];if(s===0&&a[e(7)]!==2)try{await this.planningWeather(s,t,r,n)}catch(i){throw console.warn("第"+(s+1)+"天天气规划失败:",i),i}if(s===0&&this.travel_mode==="租车"&&a.rentCompleted!==2)try{await this.planningRent(s,t,r,n)}catch(i){throw console.warn("第"+(s+1)+"天租车规划失败:",i),i}if(a.drivingCompleted!==2){const i=parseInt(this.plan_requirements,10);try{await this.planningDriving(s,t,r,n,i)}catch(l){throw console.warn("第"+(s+1)+"天路线规划失败:",l),l}}if(s!==this[e(71)]-1||s===0||this.plan_mode==="单程"){if(a.viewCompleted!==2)try{await this.planningView(s,t,r,n)}catch(i){throw console.warn("第"+(s+1)+"天景点规划失败:",i),i}if(a.foodCompleted!==2)try{await this.planningFood(s,t,r,n)}catch(i){throw console.warn("第"+(s+1)+e(103),i),i}if(a.hotelCompleted!==2)try{await this.planningHotel(s,t,r,n)}catch(i){throw console.warn("第"+(s+1)+"天住宿规划失败:",i),i}}try{await this.savePlanToDB(s,t.account,r)}catch(i){console.warn("第"+(s+1)+"天存库失败:",i)}}await this.planningFinishProc(),this.showCompletionModal=!0}catch(A){console.error("继续规划失败:",A),this.resetAllLoadingStates(),this.showBtn=!0,this.showPlanningFailure("继续规划失败，请检查网络连接后重试")}},restoreCompletedState(){const e=m;this.showBtn=!0,this.resetAllLoadingStates(),this[e(104)]()&&this[e(105)](),this.contents.length>0&&(this.selectedDayIndex=0,this.$nextTick(()=>{setTimeout(()=>{const A=m;this.refreshVisibleMaps(),this[A(5)].forEach((t,r)=>{t.drivingCompleted===2&&setTimeout(()=>{this.forceMapResize(r)},500*(r+1))})},1e3)}))},forceRefreshAllMaps(){this[m(5)].forEach((A,t)=>{A.drivingCompleted===2&&setTimeout(()=>{try{document.querySelector("#map-container-"+t)&&(BA.getMapInstance(t)?this.forceMapResize(t):this.regenerateMapRoute(t))}catch(r){console.error("强制刷新第"+(t+1)+"天地图失败:",r)}},1e3*(t+1))})},showPaymentWindow(e){const A=m;this[A(106)]=e,this.showPaymentModal=!0},handlePaymentSuccess(){this.showPaymentModal=!1,this.paymentCompleted=!0,this.continuePlanningAfterPayment()},handlePaymentCancel(){this.showPaymentModal=!1,this.paymentCompleted=!1,this.showBtn=!0},async continuePlanningAfterPayment(){this.continuePlanningAfterPaymentLogic()},async continuePlanningAfterPaymentLogic(){const e=m,A=Qe(),{user:t}=await A.getUserInfo();if(this.hasSavedPlanningContents()){if(this.loadPlanningContents()&&this.shouldContinuePlanning()&&confirm(`检测到有未完成的规划内容，是否继续之前的规划？

点击"确定"继续之前的规划
点击"取消"开始新的规划`)){this.showBtn=!1,await this[e(21)](),setTimeout(()=>{this.handleContentRestoration(!1)},500);return}this.resetPageContentAndPlanning()}if(!this.validateFormData())return;if(!this.hasCompleteLocation(this[e(8)])){alert("为避免地点位置错误，请根据弹出的地点信息选择起点");return}if(!this.hasCompleteLocation(this[e(107)])){alert("为避免地点位置错误，请根据弹出的地点信息选择终点");return}this.showBtn=!1,this.last_start=this.s_address,this[e(83)]=this.e_address,this.resetScrollState(),this.clearPlanningContents(),this.contents=[];for(let s=0;s<this.dates;s++)this.contents.push({weatherCompleted:0,rentCompleted:0,drivingCompleted:0,viewCompleted:0,hotelCompleted:0,foodCompleted:0,costCompleted:0});const r=this[e(108)]();let n=Ft;n=n[e(61)](/startDate/g,this.startDate).replace(/s_address/g,this.s_address).replace(/e_address/g,this.e_address).replace(/plan_mode/g,this.plan_mode).replace(/travel_mode/g,this[e(26)]).replace(/dates/g,this.dates);try{await this.planningWeather(0,t,r,n)}catch(s){D.error("天气规划失败："+s),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("天气信息获取失败，请检查网络连接后重试");return}if(this.travel_mode==="租车")try{await this.planningRent(0,t,r,n)}catch(s){D.error(e(109)+s),this[e(110)](),this.resetScrollState(),this.contents=[],this.showPlanningFailure("租车方案规划失败，请检查网络连接后重试");return}for(let s=0;s<this.dates;s++){try{await this.planningDriving(s,t,r,n)}catch(a){D.error(e(111)+a),this[e(110)](),this.resetScrollState(),this.contents=[],this.showPlanningFailure("路线规划失败，请检查网络连接后重试");return}if(s!==this.dates-1||s===0||this.plan_mode==="单程"){try{await this[e(112)](s,t,r,n)}catch(a){D.error("景点规划失败："+a),this.planningFinishProc(),this[e(113)](),this.contents=[],this.showPlanningFailure("景点推荐规划失败，请检查网络连接后重试");return}try{await this[e(114)](s,t,r,n)}catch(a){D.error(e(115)+a),this[e(110)](),this.resetScrollState(),this.contents=[],this.showPlanningFailure(e(116));return}try{await this.planningHotel(s,t,r,n)}catch(a){D.error("住宿规划失败："+a),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("住宿推荐规划失败，请检查网络连接后重试");return}}try{await this.savePlanToDB(s,t.account,r)}catch(a){D.error("旅游规划存库失败："+a)}}await this.planningFinishProc(),this.showCompletionModal=!0},async askUserToContinuePlanning(){try{confirm(`检测到有未完成的规划内容，是否继续之前的规划？

点击"确定"继续之前的规划
点击"取消"重置页面内容`)?(this.showBtn=!1,await this.handleContentRestoration()):this.resetPageContentAndPlanning()}catch(e){console.error("询问用户继续规划时出错:",e),this.resetPageContentAndPlanning()}},resetPageContentAndPlanning(){this.clearPlanningContents(),this.contents=[],this.selectedDayIndex=-1,this.activeDayDetailIndex=-1,this.expandedSectionType=null,this.showBtn=!0,this.resetAllLoadingStates(),this.last_start="",this.last_end="",this.rent_requirements="",this.rent_customized=!1,this.plan_requirements="0",this.plan_customized=!1,this.hotel_requirements="",this.hotel_customized=!1,this.errorMessage="",this.failureReason="",this.showFailureModal=!1,this.showCompletionModal=!1,console.log("页面内容和规划数据已重置")}}};function Wr(){const e=["global-loading-banner","disabled","input-wrapper","value","day-number","panel-subtitle","onClick","src","location","div","label-icon"," 出发地 ","startDate","polyline",'<label for="travel-mode" class="form-label" data-v-19315b56><svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M7 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" data-v-19315b56></path><path d="M17 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" data-v-19315b56></path><path d="M5 17h-2v-6l2-5h9l4 5h1a2 2 0 0 1 2 2v4h-2" data-v-19315b56></path><path d="M9 17v-6h4v6" data-v-19315b56></path><path d="M2 6h15" data-v-19315b56></path></svg> 交通方式 </label>',"button",'<div class="btn-content" data-v-19315b56><svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M4.5 16.5c-1.5 1.5-1.5 4.5 0 6s4.5 1.5 6 0l1-1" data-v-19315b56></path><path d="M15 7l-6.5 6.5a1.5 1.5 0 0 0 0 2.12l.88.88a1.5 1.5 0 0 0 2.12 0L18 10" data-v-19315b56></path><path d="M9 5l8 8" data-v-19315b56></path><path d="M21 3l-6 6" data-v-19315b56></path></svg><span class="btn-text" data-v-19315b56>开始规划</span></div><div class="btn-shine" data-v-19315b56></div>',"showBtn",'<div class="loading-animation" data-v-19315b56><div class="loading-spinner" data-v-19315b56><svg viewBox="0 0 50 50" data-v-19315b56><circle cx="25" cy="25" r="20" fill="none" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416" data-v-19315b56><animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite" data-v-19315b56></animate><animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite" data-v-19315b56></animate></circle></svg></div><div class="loading-text" data-v-19315b56><span class="loading-message" data-v-19315b56>正在为您规划完美旅程...</span><div class="loading-dots" data-v-19315b56><span data-v-19315b56></span><span data-v-19315b56></span><span data-v-19315b56></span></div></div></div>',"span","activeDayDetailIndex","contents","weather","handleDetailPanelClick","🌤️","rent","travel_mode","feature-button","food","length","weatherCompleted","answer-area","shouldShowSectionHeader","think","parseMarkdown","answer-action-input","handleRentCustomize","handleActionClick","rent_customized","🗺️","drivingCompleted","shouldShowSection","map-wrapper","toggleMapSize","map-container","Tourist Attractions","view","icon-ori","info","Local Cuisine","think-area","content-item","url","hotel","hotel_customized","completion-modal","stop"," 需要支付",'<p class="payment-note" data-v-19315b56>支付完成后即可开始智能规划您的完美旅程</p><div class="payment-features" data-v-19315b56><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>🎯</span><span data-v-19315b56>AI智能规划</span></div><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>🗺️</span><span data-v-19315b56>精准路线导航</span></div><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>🍽️</span><span data-v-19315b56>地道美食推荐</span></div><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>🏨</span><span data-v-19315b56>优质住宿建议</span></div></div>',"handlePaymentSuccess","modal-header failure-header","modal-title","规划失败","dates","plan_mode"," 生成失败","failureReason","floating-scroll-buttons","scroll-btn scroll-to-bottom"];return Wr=function(){return e},Wr()}const sA=Yr;function Yr(e,A){const t=Wr();return Yr=function(r,n){return r=r-0,t[r]},Yr(e,A)}const jB={class:sA(0)},$B={class:"global-loading-content"},Ax={class:"global-loading-text"},ex={class:"travel-planning-container"},tx={class:"travel-form"},rx={class:"form-progress"},nx={class:"location-section"},sx={class:"location-inputs"},ax={class:"form-item location-item"},ix={class:"input-wrapper"},ox=[sA(1)],lx={class:"form-item location-item"},cx={class:"input-wrapper"},Bx=["disabled"],xx={class:"travel-details-section"},dx={class:"details-grid"},ux={class:"form-item detail-item"},hx={class:sA(2)},gx=["disabled"],wx={class:"form-item detail-item"},fx={class:sA(2)},Qx=["disabled"],Cx=[sA(3),"selected"],px={class:"form-item detail-item"},Ux={class:"input-wrapper"},Fx=["disabled"],mx={class:"form-item detail-item"},vx={class:"input-wrapper"},yx=["disabled"],Ex={class:"action-section"},Hx={class:"button-group"},Ix=["disabled"],bx={class:"loading-state"},Sx={class:"day-navigation"},Lx=["onClick","disabled"],Dx={class:sA(4)},Kx={class:"day-title"},Tx={class:"day-detail-panel"},Mx={class:"panel-header"},_x={class:"panel-title"},Ox={class:sA(5)},kx={class:"panel-body"},Rx={class:"panel-features"},Gx=[sA(1)],Px=["disabled"],Vx=["disabled"],Nx=["disabled"],Jx=[sA(1)],Xx=[sA(1)],Wx={class:"panel-actions"},Yx={id:"scroll-area",class:"scroll-container",ref:"scrollContainer"},Zx={"data-dynamic-item":"",class:"answer-area-container"},zx=["onClick"],qx={class:"header-toggle"},jx={class:"section-content"},$x=["innerHTML"],Ad={"data-dynamic-item":"",class:"answer-area-container"},ed=["onClick"],td={class:"header-toggle"},rd={class:"section-content"},nd=["innerHTML"],sd={class:"answer-action-input-container"},ad=[sA(3),"onKeypress"],id=["onClick"],od={"data-dynamic-item":"",class:"answer-area-container"},ld=["onClick"],cd={class:"header-toggle"},Bd={class:"section-content"},xd=["innerHTML"],dd={class:"answer-action-input-container"},ud=["value"],hd=[sA(6)],gd={class:"planning-box"},wd=["onClick"],fd={class:"map-controls"},Qd=["onClick","title"],Cd={key:0},pd={key:1},Ud=["id"],Fd={"data-dynamic-item":"",class:"answer-area-container"},md=["onClick"],vd={class:"header-toggle"},yd={class:"section-content"},Ed={class:"content-wrapper"},Hd=["innerHTML"],Id=["src"],bd=["innerHTML"],Sd={"data-dynamic-item":"",class:"answer-area-container"},Ld=["onClick"],Dd={class:"header-toggle"},Kd={class:"section-content"},Td={class:"content-wrapper"},Md=["innerHTML"],_d=[sA(7)],Od=["innerHTML"],kd={"data-dynamic-item":"",class:"answer-area-container"},Rd=["onClick"],Gd={class:"header-toggle"},Pd={class:"section-content"},Vd={class:"content-wrapper"},Nd=["innerHTML"],Jd=["href"],Xd=["innerHTML"],Wd={class:"answer-action-input-container"},Yd=["value","onKeypress"],Zd=["onClick"],zd={"data-dynamic-item":"",class:"answer-area-container"},qd=["innerHTML"],jd={class:"vitepress-divider"},$d={key:0,class:"journal-recommendation-section"},Au={class:"modal-body"},eu={class:"modal-message"},tu={class:"modal-subtitle"},ru={class:"location"},nu={class:sA(8)},su={class:"modal-footer"},au={class:"modal-body"},iu={class:"modal-message"},ou={class:"payment-amount-display"},lu={class:"amount-value"},cu={class:"modal-footer"},Bu={class:"modal-body"},xu={class:"modal-message"},du={class:"modal-subtitle"},uu={class:"modal-footer"};function hu(e,A,t,r,n,s){const a=sA,o=Ue("JournalRecommendation"),i=Ue("PaymentMethods");return M(),_(vA,null,[H(B("div",jB,[B("div",$B,[A[28]||(A[28]=B("div",{class:"global-spinner"},null,-1)),B("span",Ax,I(e.currentLoadingMessage),1)])],512),[[S,e.isAnyLoading]]),B(a(9),ex,[A[88]||(A[88]=fA('<div class="page-header" data-v-19315b56><div class="header-content" data-v-19315b56><h1 class="page-title" data-v-19315b56><svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" data-v-19315b56></path><circle cx="12" cy="10" r="3" data-v-19315b56></circle></svg> 行旅诗笺 </h1><p class="page-subtitle" data-v-19315b56>为您智能定制完美的旅行体验</p></div><div class="header-decoration" data-v-19315b56><div class="decoration-circle" data-v-19315b56></div><div class="decoration-circle" data-v-19315b56></div><div class="decoration-circle" data-v-19315b56></div></div></div>',1)),B("div",tx,[B("div",rx,[B("div",{class:oA(["progress-step",{active:e.currentProgressStep>=1}])},A[29]||(A[29]=[B("div",{class:"step-number"},"1",-1),B("span",{class:"step-label"},"基本信息",-1)]),2),B(a(9),{class:oA(["progress-line",{active:e.currentProgressStep>=2}])},null,2),B("div",{class:oA(["progress-step",{active:e.currentProgressStep>=2}])},A[30]||(A[30]=[B("div",{class:"step-number"},"2",-1),B("span",{class:"step-label"},"生成规划",-1)]),2),B("div",{class:oA(["progress-line",{active:e.currentProgressStep>=3}])},null,2),B("div",{class:oA(["progress-step",{active:e.currentProgressStep>=3}])},A[31]||(A[31]=[B("div",{class:"step-number"},"3",-1),B("span",{class:"step-label"},"完成",-1)]),2)]),B("div",nx,[A[37]||(A[37]=B("h3",{class:"section-title"},[B("svg",{class:"section-icon",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[B("path",{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}),B("circle",{cx:"12",cy:"10",r:"3"})]),J(" 选择出发地与目的地 ")],-1)),B("div",sx,[B("div",ax,[A[33]||(A[33]=B("label",{for:"start-tipinput",class:"form-label"},[B("svg",{class:a(10),viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[B("circle",{cx:"12",cy:"12",r:"10"}),B("circle",{cx:"12",cy:"12",r:"3"})]),J(a(11))],-1)),B(a(9),ix,[H(B("input",{id:"start-tipinput",class:"form-input","onUpdate:modelValue":A[0]||(A[0]=l=>e.s_address=l),type:"text",placeholder:"请输入出发城市/地点",disabled:!e.showBtn,autocomplete:"off"},null,8,ox),[[Yt,e.s_address]]),A[32]||(A[32]=B("div",{class:"input-decoration"},null,-1))])]),A[36]||(A[36]=B("div",{class:"route-connector"},[B("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[B("path",{d:"M5 12h14"}),B("path",{d:"m12 5 7 7-7 7"})])],-1)),B("div",lx,[A[35]||(A[35]=B("label",{for:"end-tipinput",class:"form-label"},[B("svg",{class:"label-icon",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[B("path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"}),B("path",{d:"M4 22v-7"})]),J(" 目的地 ")],-1)),B("div",cx,[H(B("input",{id:"end-tipinput",class:"form-input","onUpdate:modelValue":A[1]||(A[1]=l=>e.e_address=l),type:"text",placeholder:"请输入目的地城市/景点",disabled:!e.showBtn,autocomplete:"off"},null,8,Bx),[[Yt,e.e_address]]),A[34]||(A[34]=B(a(9),{class:"input-decoration"},null,-1))])])])]),B("div",xx,[A[48]||(A[48]=fA('<h3 class="section-title" data-v-19315b56><svg class="section-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><rect x="3" y="4" width="18" height="18" rx="2" ry="2" data-v-19315b56></rect><line x1="16" y1="2" x2="16" y2="6" data-v-19315b56></line><line x1="8" y1="2" x2="8" y2="6" data-v-19315b56></line><line x1="3" y1="10" x2="21" y2="10" data-v-19315b56></line></svg> 设置旅行参数 </h3>',1)),B(a(9),dx,[B("div",ux,[A[39]||(A[39]=fA('<label for="start-dateinput" class="form-label" data-v-19315b56><svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><rect x="3" y="4" width="18" height="18" rx="2" ry="2" data-v-19315b56></rect><line x1="16" y1="2" x2="16" y2="6" data-v-19315b56></line><line x1="8" y1="2" x2="8" y2="6" data-v-19315b56></line><line x1="3" y1="10" x2="21" y2="10" data-v-19315b56></line></svg> 出发日期 </label>',1)),B("div",hx,[H(B("input",{type:"date",class:"form-input date-input","onUpdate:modelValue":A[2]||(A[2]=l=>e[a(12)]=l),disabled:!e.showBtn},null,8,gx),[[Yt,e.startDate]]),A[38]||(A[38]=B("div",{class:"input-decoration"},null,-1))])]),B(a(9),wx,[A[41]||(A[41]=B("label",{for:"datesinput",class:"form-label"},[B("svg",{class:"label-icon",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[B("circle",{cx:"12",cy:"12",r:"10"}),B(a(13),{points:"12,6 12,12 16,14"})]),J(" 游玩天数 ")],-1)),B("div",fx,[H(B("select",{id:"datesinput",class:"form-input select-input","onUpdate:modelValue":A[3]||(A[3]=l=>e.dates=l),disabled:!e.showBtn},[(M(),_(vA,null,DA(7,l=>B("option",{value:l,key:l,selected:l===3},I(l)+"天",9,Cx)),64))],8,Qx),[[$e,e.dates,void 0,{number:!0}]]),A[40]||(A[40]=B("div",{class:"input-decoration"},null,-1))])]),B("div",px,[A[44]||(A[44]=fA('<label for="plan-mode" class="form-label" data-v-19315b56><svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" data-v-19315b56></path><polyline points="3.27,6.96 12,12.01 20.73,6.96" data-v-19315b56></polyline><line x1="12" y1="22.08" x2="12" y2="12" data-v-19315b56></line></svg> 旅行模式 </label>',1)),B("div",Ux,[H(B("select",{id:"plan-mode",class:"form-input select-input","onUpdate:modelValue":A[4]||(A[4]=l=>e.plan_mode=l),disabled:!e.showBtn},A[42]||(A[42]=[B("option",{value:"往返"},"往返旅行",-1),B("option",{value:"单程"},"单程旅行",-1)]),8,Fx),[[$e,e.plan_mode]]),A[43]||(A[43]=B("div",{class:"input-decoration"},null,-1))])]),B("div",mx,[A[47]||(A[47]=fA(a(14),1)),B("div",vx,[H(B("select",{id:"travel-mode",class:"form-input select-input","onUpdate:modelValue":A[5]||(A[5]=l=>e.travel_mode=l),disabled:!e.showBtn},A[45]||(A[45]=[B("option",{value:"自驾"},"自驾出行",-1),B("option",{value:"租车"},"租车出行",-1)]),8,yx),[[$e,e.travel_mode]]),A[46]||(A[46]=B("div",{class:"input-decoration"},null,-1))])])])]),B("div",Ex,[B("div",Hx,[H(B(a(15),{class:"btn-primary btn-planning",onClick:A[6]||(A[6]=(...l)=>e.planningNew&&e.planningNew(...l))},A[49]||(A[49]=[fA(a(16),2)]),512),[[S,e[a(17)]]]),H(B("button",{class:"btn-secondary btn-reset",onClick:A[7]||(A[7]=(...l)=>e.resetFormData&&e.resetFormData(...l)),title:"清除所有输入内容",disabled:!e.showBtn},A[50]||(A[50]=[fA('<div class="btn-content" data-v-19315b56><svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" data-v-19315b56></path><path d="M21 3v5h-5" data-v-19315b56></path><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" data-v-19315b56></path><path d="M3 21v-5h5" data-v-19315b56></path></svg><span class="btn-text" data-v-19315b56>重置表单</span></div>',1)]),8,Ix),[[S,e.showBtn]])]),H(B("div",bx,A[51]||(A[51]=[fA(a(18),1)]),512),[[S,!e.showBtn]])])]),H(B("div",Sx,[(M(!0),_(vA,null,DA(e.contents,(l,c)=>{const x=a;return M(),_("button",{key:"nav-"+c,class:oA(["day-nav-btn",{active:e.activeDayDetailIndex===c,completed:e.isDayCompleted(l),disabled:!e.isDayCompleted(l)}]),onClick:d=>e.selectDay(c),disabled:!e.isDayCompleted(l)},[B("span",Dx,"第"+I(c+1)+"天",1),B(x(19),Kx,I(e.getDayTitle(l,c)),1)],10,Lx)}),128))],512),[[S,e.contents.length>0]]),A[89]||(A[89]=B(a(9),{class:"ai-generated-tip"},"所有规划内容由 AI 生成，仅供参考",-1)),H(B("div",Tx,[B("div",Mx,[A[52]||(A[52]=B("div",{class:"panel-icon"},"✨",-1)),B("h3",_x,"第"+I(e.activeDayDetailIndex+1)+"天行程详情",1),B("p",Ox,I(e[a(20)]!==-1&&e[a(21)][e.activeDayDetailIndex]?e.getDayTitle(e.contents[e.activeDayDetailIndex],e.activeDayDetailIndex):""),1)]),B("div",kx,[B("div",Rx,[H(B("button",{class:oA(["feature-button",{available:e.isSectionAvailable("weather",e.activeDayDetailIndex),active:e.expandedSectionType==="weather"}]),disabled:!e.isSectionAvailable(a(22),e.activeDayDetailIndex),onClick:A[8]||(A[8]=l=>e[a(23)]("weather"))},A[53]||(A[53]=[B("span",{class:"feature-icon"},a(24),-1),B("span",{class:"feature-text"},"天气",-1)]),10,Gx),[[S,e.activeDayDetailIndex===0]]),H(B("button",{class:oA(["feature-button",{available:e.isSectionAvailable("rent",e.activeDayDetailIndex),active:e.expandedSectionType==="rent"}]),disabled:!e.isSectionAvailable(a(25),e.activeDayDetailIndex),onClick:A[9]||(A[9]=l=>e.handleDetailPanelClick("rent"))},A[54]||(A[54]=[B("span",{class:"feature-icon"},"🚗",-1),B("span",{class:"feature-text"},"租车",-1)]),10,Px),[[S,e.activeDayDetailIndex===0&&e[a(26)]==="租车"]]),B("button",{class:oA(["feature-button",{available:e.isSectionAvailable("driving",e.activeDayDetailIndex),active:e.expandedSectionType==="driving"}]),disabled:!e.isSectionAvailable("driving",e.activeDayDetailIndex),onClick:A[10]||(A[10]=l=>e.handleDetailPanelClick("driving"))},A[55]||(A[55]=[B("span",{class:"feature-icon"},"🗺️",-1),B("span",{class:"feature-text"},"路线",-1)]),10,Vx),B("button",{class:oA(["feature-button",{available:e.isSectionAvailable("view",e.activeDayDetailIndex),active:e.expandedSectionType==="view"}]),disabled:!e.isSectionAvailable("view",e.activeDayDetailIndex),onClick:A[11]||(A[11]=l=>e.handleDetailPanelClick("view"))},A[56]||(A[56]=[B("span",{class:"feature-icon"},"🏞️",-1),B("span",{class:"feature-text"},"景点",-1)]),10,Nx),B("button",{class:oA([a(27),{available:e.isSectionAvailable(a(28),e.activeDayDetailIndex),active:e.expandedSectionType===a(28)}]),disabled:!e.isSectionAvailable("food",e[a(20)]),onClick:A[12]||(A[12]=l=>e.handleDetailPanelClick("food"))},A[57]||(A[57]=[B("span",{class:"feature-icon"},"🍜",-1),B("span",{class:"feature-text"},"美食",-1)]),10,Jx),B("button",{class:oA(["feature-button",{available:e.isSectionAvailable("hotel",e.activeDayDetailIndex),active:e.expandedSectionType==="hotel"}]),disabled:!e.isSectionAvailable("hotel",e[a(20)]),onClick:A[13]||(A[13]=l=>e.handleDetailPanelClick("hotel"))},A[58]||(A[58]=[B("span",{class:"feature-icon"},"🏨",-1),B("span",{class:"feature-text"},"住宿",-1)]),10,Xx)]),B("div",Wx,[H(B("button",{class:"collapse-all-btn",onClick:A[14]||(A[14]=(...l)=>e.collapseAll&&e.collapseAll(...l))},A[59]||(A[59]=[B("span",{class:"btn-icon"},"📝",-1),B("span",{class:"btn-text"},"收起全部",-1)]),512),[[S,e.expandedSectionType!==null]])])])],512),[[S,e.activeDayDetailIndex!==-1&&e.contents[a(29)]>0&&e.contents[e.activeDayDetailIndex]]]),B("div",Yx,[(M(!0),_(vA,null,DA(e.contents,(l,c)=>{const x=a;return M(),_("div",{key:"day-"+c},[H(B("div",Zx,[B("div",{class:"section-header weather-header",onClick:d=>e.handleSectionHeaderClick(x(22),c)},[A[60]||(A[60]=B("div",{class:"header-icon"},"🌤️",-1)),A[61]||(A[61]=B("div",{class:"header-title"},"天气信息",-1)),A[62]||(A[62]=B("div",{class:"header-subtitle"},"Weather Information",-1)),H(B("div",qx,"▼",512),[[S,e.shouldShowSection("weather",c)]])],8,zx),H(B("div",jx,[H(B("div",{class:"think-area"},I(l.think),513),[[S,l[x(30)]===1]]),B("div",{class:x(31),innerHTML:e.parseMarkdown(l.weather)},null,8,$x)],512),[[S,e.shouldShowSection("weather",c)]])],512),[[S,e[x(32)]("weather",c)]]),H(B("div",Ad,[B("div",{class:"section-header rent-header",onClick:d=>e.handleSectionHeaderClick("rent",c)},[A[63]||(A[63]=B("div",{class:"header-icon"},"🚗",-1)),A[64]||(A[64]=B("div",{class:"header-title"},"租车方案",-1)),A[65]||(A[65]=B("div",{class:"header-subtitle"},"Car Rental",-1)),H(B("div",td,"▼",512),[[S,e.shouldShowSection("rent",c)]])],8,ed),H(B("div",rd,[H(B("div",{class:"think-area"},I(l[x(33)]),513),[[S,l.rentCompleted===1]]),B("div",{class:"answer-area",innerHTML:e[x(34)](l.rent)},null,8,nd),H(B("div",sd,[B("input",{value:e.rent_requirements,type:"text",class:x(35),placeholder:"请输入您的租车需求，我们可以根据您的需求重新定制一次...",onInput:A[15]||(A[15]=(...d)=>e.updateRentRequirements&&e.updateRentRequirements(...d)),onKeypress:rn(d=>e[x(36)](c),["enter"])},null,40,ad)],512),[[S,l.rentCompleted===2&&!e.rent_customized]]),H(B("button",{class:"answer-action-btn",onClick:d=>e[x(37)]("rent",c)},A[66]||(A[66]=[B("span",{class:"btn-icon"},"🚗",-1),B("span",{class:"btn-text"},"定制",-1)]),8,id),[[S,l.rentCompleted===2&&!e[x(38)]]])],512),[[S,e.shouldShowSection("rent",c)]])],512),[[S,e.shouldShowSectionHeader("rent",c)]]),H(B("div",od,[B("div",{class:"section-header driving-header",onClick:d=>e.handleSectionHeaderClick("driving",c)},[A[67]||(A[67]=B("div",{class:"header-icon"},x(39),-1)),A[68]||(A[68]=B("div",{class:"header-title"},"路线规划",-1)),A[69]||(A[69]=B("div",{class:"header-subtitle"},"Route Planning",-1)),H(B("div",cd,"▼",512),[[S,e.shouldShowSection("driving",c)]])],8,ld),H(B("div",Bd,[H(B("div",{class:"think-area"},I(l.think),513),[[S,l[x(40)]===1]]),B("div",{class:"answer-area",innerHTML:e.parseMarkdown(l.driving)},null,8,xd),H(B(x(9),dd,[H(B("select",{class:"answer-action-input","onUpdate:modelValue":A[16]||(A[16]=d=>e.plan_requirements=d)},[(M(!0),_(vA,null,DA(e.planOptions,d=>(M(),_("option",{key:d.value,value:d.value},I(d.text),9,ud))),128))],512),[[$e,e.plan_requirements]])],512),[[S,l.drivingCompleted===2&&!e.plan_customized]]),H(B("button",{class:"answer-action-btn",onClick:d=>e.handleActionClick("driving",c)},A[70]||(A[70]=[B("span",{class:"btn-icon"},"📍",-1),B("span",{class:"btn-text"},"重新规划(一次免费机会)",-1)]),8,hd),[[S,l.drivingCompleted===2&&!e.plan_customized]])],512),[[S,e[x(41)]("driving",c)]])],512),[[S,e.shouldShowSectionHeader("driving",c)]]),H(B("div",gd,[A[71]||(A[71]=B("div",{class:"section-header map-header"},[B("div",{class:"header-icon"},"🗺️"),B("div",{class:"header-title"},"路线地图"),B("div",{class:"header-subtitle"},"Route Map")],-1)),B("div",{class:oA([x(42),{"map-maximized":e.maximizedMapIndex===c}])},[e.maximizedMapIndex===c?(M(),_(x(9),{key:0,class:"map-overlay",onClick:d=>e[x(43)](c)},null,8,wd)):yA("",!0),B("div",fd,[B(x(15),{class:"map-control-btn",onClick:d=>e.toggleMapSize(c),title:e.maximizedMapIndex===c?"缩小地图 (ESC)":"最大化地图"},[e.maximizedMapIndex===c?(M(),_("span",Cd,"✕")):(M(),_("span",pd,"🗖"))],8,Qd)]),B(x(9),{id:"map-container-"+c,class:x(44)},null,8,Ud)],2)],512),[[S,e.shouldShowSectionHeader("driving",c)&&"amap"in l&&e.shouldShowSection("driving",c)]]),H(B("div",Fd,[B("div",{class:"section-header view-header",onClick:d=>e.handleSectionHeaderClick("view",c)},[A[72]||(A[72]=B("div",{class:"header-icon"},"🏞️",-1)),A[73]||(A[73]=B("div",{class:"header-title"},"景点推荐",-1)),A[74]||(A[74]=B("div",{class:"header-subtitle"},x(45),-1)),H(B("div",vd,"▼",512),[[S,e.shouldShowSection("view",c)]])],8,md),H(B("div",yd,[B("div",Ed,[H(B(x(9),{class:"think-area"},I(l.think),513),[[S,l.viewCompleted===1]]),B("div",{class:"answer-area",innerHTML:this.parseMarkdown(`**景点推荐:**

`)},null,8,Hd),(M(!0),_(vA,null,DA(l[x(46)],(d,f)=>{const u=x;return M(),_("div",{class:"content-item",key:"view-"+f},[A[75]||(A[75]=B("span",{class:u(47)},"🏞️",-1)),J(" "+I(d.name)+" ",1),B("img",{src:d.url,alt:"景点图片",class:"view-image"},null,8,Id),A[76]||(A[76]=B("div",{class:"ai-generated-tip"},"图片由 AI 生成，仅供参考",-1)),B("div",{class:"answer-area",innerHTML:this.parseMarkdown(d[u(48)])},null,8,bd)])}),128))])],512),[[S,e.shouldShowSection(x(46),c)]])],512),[[S,e.shouldShowSectionHeader("view",c)]]),H(B(x(9),Sd,[B("div",{class:"section-header food-header",onClick:d=>e.handleSectionHeaderClick("food",c)},[A[77]||(A[77]=B("div",{class:"header-icon"},"🍜",-1)),A[78]||(A[78]=B(x(9),{class:"header-title"},"美食推荐",-1)),A[79]||(A[79]=B("div",{class:"header-subtitle"},x(49),-1)),H(B("div",Dd,"▼",512),[[S,e.shouldShowSection("food",c)]])],8,Ld),H(B("div",Kd,[B("div",Td,[H(B("div",{class:"think-area"},I(l.think),513),[[S,l.foodCompleted===1]]),B("div",{class:"answer-area",innerHTML:this.parseMarkdown(`**美食推荐:**

`)},null,8,Md),(M(!0),_(vA,null,DA(l.food,(d,f)=>{const u=x;return M(),_(u(9),{class:"content-item",key:"food-"+f},[A[80]||(A[80]=B("span",{class:"icon-ori"},"🍜",-1)),J(" "+I(d.name)+" ",1),B("img",{src:d.url,alt:"美食图片",class:"food-image"},null,8,_d),A[81]||(A[81]=B("div",{class:"ai-generated-tip"},"图片由 AI 生成，仅供参考",-1)),B("div",{class:"answer-area",innerHTML:this.parseMarkdown(d.info)},null,8,Od)])}),128))])],512),[[S,e.shouldShowSection("food",c)]])],512),[[S,e.shouldShowSectionHeader("food",c)]]),H(B("div",kd,[B("div",{class:"section-header hotel-header",onClick:d=>e.handleSectionHeaderClick("hotel",c)},[A[82]||(A[82]=B("div",{class:"header-icon"},"🏨",-1)),A[83]||(A[83]=B("div",{class:"header-title"},"住宿推荐",-1)),A[84]||(A[84]=B(x(9),{class:"header-subtitle"},"Accommodation",-1)),H(B("div",Gd,"▼",512),[[S,e.shouldShowSection("hotel",c)]])],8,Rd),H(B("div",Pd,[B("div",Vd,[H(B("div",{class:x(50)},I(l[x(33)]),513),[[S,l.hotelCompleted===1]]),B("div",{class:"answer-area",innerHTML:this.parseMarkdown(`**住宿推荐:**

`)},null,8,Nd),(M(!0),_(vA,null,DA(l.hotel,(d,f)=>{const u=x;return M(),_("div",{class:u(51),key:"hotel-"+f},[B("a",{href:d[u(52)],target:"_blank",class:"hotel-link"},[A[85]||(A[85]=B(u(19),{class:"icon-ori"},"🏨",-1)),J(" "+I("携程直达："+d.name),1)],8,Jd),B("div",{class:"answer-area",innerHTML:this.parseMarkdown(d.info)},null,8,Xd)])}),128))]),H(B("div",Wd,[B("input",{value:e.hotel_requirements,type:"text",class:"answer-action-input",placeholder:"请输入您的住宿需求，我们可以根据您的需求重新定制一次...",onInput:A[17]||(A[17]=(...d)=>e.updateHotelRequirements&&e.updateHotelRequirements(...d)),onKeypress:rn(d=>e.handleActionClick(x(53),c),["enter"])},null,40,Yd)],512),[[S,l.hotelCompleted===2&&!e.hotel_customized]]),H(B("button",{class:"answer-action-btn",onClick:d=>e.handleActionClick(x(53),c)},A[86]||(A[86]=[B("span",{class:"btn-icon"},"🏨",-1),B("span",{class:"btn-text"},"定制",-1)]),8,Zd),[[S,l.hotelCompleted===2&&!e[x(54)]]])],512),[[S,e.shouldShowSection("hotel",c)]])],512),[[S,e.shouldShowSectionHeader("hotel",c)]]),H(B("div",zd,[A[87]||(A[87]=B("div",{class:"section-header cost-header"},[B(x(9),{class:"header-icon"},"💰"),B("div",{class:"header-title"},"费用预算"),B("div",{class:"header-subtitle"},"Budget Planning")],-1)),H(B("div",{class:"think-area"},I(l.think),513),[[S,l.costCompleted===1]]),B("div",{class:"answer-area",innerHTML:e.parseMarkdown(l.cost)},null,8,qd)],512),[[S,e.shouldShowSectionHeader("cost",c)]]),H(B("hr",jd,null,512),[[S,"cost"in l]])])}),128))],512),e.shouldShowJournalRecommendation?(M(),_("div",$d,[eA(o,{destination:e.e_address},null,8,["destination"])])):yA("",!0)]),e.showCompletionModal?(M(),_("div",{key:0,class:"completion-modal-overlay",onClick:A[20]||(A[20]=(...l)=>e.handleCompletionModalConfirm&&e.handleCompletionModalConfirm(...l))},[B("div",{class:a(55),onClick:A[19]||(A[19]=Zt(()=>{},["stop"]))},[A[97]||(A[97]=B("div",{class:"modal-header"},[B(a(9),{class:"modal-icon"},"✨"),B("h3",{class:"modal-title"},"规划完成！")],-1)),B("div",Au,[B("p",eu,[A[90]||(A[90]=J("您的 ")),B("strong",null,I(e.dates)+"天"+I(e.plan_mode)+"旅行规划",1),A[91]||(A[91]=J(" 已经生成完毕！"))]),B("p",tu,[A[92]||(A[92]=J("从 ")),B("span",ru,I(e.s_address),1),A[93]||(A[93]=J(" 到 ")),B("span",nu,I(e.e_address),1),A[94]||(A[94]=J(" 的完美行程等您探索"))]),A[95]||(A[95]=fA('<div class="modal-features" data-v-19315b56><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>📍</span><span data-v-19315b56>详细路线规划</span></div><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>🏞️</span><span data-v-19315b56>精选景点推荐</span></div><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>🏨</span><span data-v-19315b56>优质住宿建议</span></div><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>🍜</span><span data-v-19315b56>地道美食指南</span></div></div>',1))]),B("div",su,[B("button",{class:"modal-confirm-btn",onClick:A[18]||(A[18]=(...l)=>e.handleCompletionModalConfirm&&e.handleCompletionModalConfirm(...l))},A[96]||(A[96]=[B("span",{class:"btn-icon"},"👍",-1)]))])])])):yA("",!0),e.showPaymentModal?(M(),_("div",{key:1,class:"payment-modal-overlay",onClick:A[22]||(A[22]=(...l)=>e.handlePaymentCancel&&e.handlePaymentCancel(...l))},[B("div",{class:"payment-modal",onClick:A[21]||(A[21]=Zt(()=>{},[a(56)]))},[A[102]||(A[102]=B("div",{class:"modal-header"},[B("div",{class:"modal-icon"},"💳"),B("h3",{class:"modal-title"},"支付确认")],-1)),B("div",au,[B("p",iu,[A[98]||(A[98]=J("本次 ")),B("strong",null,I(e.dates)+"天"+I(e.plan_mode)+"旅行规划",1),A[99]||(A[99]=J(a(57)))]),B("div",ou,[A[100]||(A[100]=B("span",{class:"amount-label"},"支付金额：",-1)),B("span",lu,"¥"+I(e.paymentAmount),1)]),A[101]||(A[101]=fA(a(58),2))]),B("div",cu,[eA(i,{amount:e.paymentAmount,onPaymentSuccess:e[a(59)],onPaymentCancel:e.handlePaymentCancel},null,8,["amount","onPaymentSuccess","onPaymentCancel"])])])])):yA("",!0),e.showFailureModal?(M(),_("div",{key:2,class:"failure-modal-overlay",onClick:A[25]||(A[25]=(...l)=>e.handleFailureModalConfirm&&e.handleFailureModalConfirm(...l))},[B("div",{class:"failure-modal",onClick:A[24]||(A[24]=Zt(()=>{},["stop"]))},[A[107]||(A[107]=B("div",{class:a(60)},[B("div",{class:"modal-icon"},"❌"),B("h3",{class:a(61)},a(62))],-1)),B("div",Bu,[B("p",xu,[A[103]||(A[103]=J("很抱歉，您的 ")),B("strong",null,I(e[a(63)])+"天"+I(e[a(64)])+"旅行规划",1),A[104]||(A[104]=J(a(65)))]),B("p",du,I(e[a(66)]),1),A[105]||(A[105]=fA('<div class="failure-suggestions" data-v-19315b56><div class="suggestion-item" data-v-19315b56><span class="suggestion-icon" data-v-19315b56>🔄</span><span data-v-19315b56>检查网络连接后重新尝试</span></div><div class="suggestion-item" data-v-19315b56><span class="suggestion-icon" data-v-19315b56>📝</span><span data-v-19315b56>确认起终点地址填写正确</span></div><div class="suggestion-item" data-v-19315b56><span class="suggestion-icon" data-v-19315b56>⏰</span><span data-v-19315b56>稍后再试，避开网络高峰期</span></div><div class="suggestion-item" data-v-19315b56><span class="suggestion-icon" data-v-19315b56>💬</span><span data-v-19315b56>如持续失败请联系客服</span></div></div>',1))]),B("div",uu,[B("button",{class:"modal-failure-btn",onClick:A[23]||(A[23]=(...l)=>e.handleFailureModalConfirm&&e.handleFailureModalConfirm(...l))},A[106]||(A[106]=[B("span",{class:"btn-icon"},"😔",-1),B("span",{class:"btn-text"},"我知道了",-1)]))])])])):yA("",!0),B("div",{class:oA([a(67),{show:e.showFloatingButtons}])},[B("button",{class:"scroll-btn scroll-to-top",onClick:A[26]||(A[26]=(...l)=>e.scrollToTop&&e.scrollToTop(...l)),title:"回到规划表单"},A[108]||(A[108]=[B("span",{class:"scroll-btn-icon"},"⬆️",-1)])),B("button",{class:a(68),onClick:A[27]||(A[27]=(...l)=>e.scrollToBottom&&e.scrollToBottom(...l)),title:"滚动到底部"},A[109]||(A[109]=[B("span",{class:"scroll-btn-icon"},"⬇️",-1)]))],2)],64)}const Cu=ls(qB,[["render",hu],["__scopeId","data-v-19315b56"]]);export{Cu as default};
