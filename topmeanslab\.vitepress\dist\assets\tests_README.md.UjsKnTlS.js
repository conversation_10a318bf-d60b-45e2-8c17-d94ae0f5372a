import{_ as a,c as i,o as l,ab as n}from"./chunks/framework.oPHriSgN.js";const c=JSON.parse('{"title":"TopmeansLab 测试套件","description":"","frontmatter":{},"headers":[],"relativePath":"tests/README.md","filePath":"tests/README.md"}'),e={name:"tests/README.md"};function t(h,s,p,r,k,d){return l(),i("div",null,s[0]||(s[0]=[n(`<h1 id="topmeanslab-测试套件" tabindex="-1">TopmeansLab 测试套件 <a class="header-anchor" href="#topmeanslab-测试套件" aria-label="Permalink to &quot;TopmeansLab 测试套件&quot;">​</a></h1><p>本目录包含了 TopmeansLab 项目的完整测试套件，使用 Vitest 作为测试框架。</p><h2 id="测试结构" tabindex="-1">测试结构 <a class="header-anchor" href="#测试结构" aria-label="Permalink to &quot;测试结构&quot;">​</a></h2><div class="language- vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span>tests/</span></span>
<span class="line"><span>├── setup.js                          # 测试环境设置</span></span>
<span class="line"><span>├── services/                         # 服务层单元测试</span></span>
<span class="line"><span>│   ├── TopmeansApiService.test.js    # API 服务测试</span></span>
<span class="line"><span>│   ├── TopmeansFormManager.test.js   # 表单管理测试</span></span>
<span class="line"><span>│   ├── TopmeansMapService.test.js    # 地图服务测试</span></span>
<span class="line"><span>│   ├── TopmeansMarkdownService.test.js # Markdown 服务测试</span></span>
<span class="line"><span>│   ├── TopmeansScrollManager.test.js # 滚动管理测试</span></span>
<span class="line"><span>│   └── TopmeansStyleManager.test.js  # 样式管理测试</span></span>
<span class="line"><span>├── integration/                      # 集成测试</span></span>
<span class="line"><span>│   └── services-integration.test.js  # 服务集成测试</span></span>
<span class="line"><span>├── components/                       # 组件测试（待添加）</span></span>
<span class="line"><span>├── backend/                          # 后端测试（待添加）</span></span>
<span class="line"><span>├── utils/                            # 工具函数测试（待添加）</span></span>
<span class="line"><span>└── performance/                      # 性能测试（待添加）</span></span></code></pre></div><h2 id="运行测试" tabindex="-1">运行测试 <a class="header-anchor" href="#运行测试" aria-label="Permalink to &quot;运行测试&quot;">​</a></h2><h3 id="安装依赖" tabindex="-1">安装依赖 <a class="header-anchor" href="#安装依赖" aria-label="Permalink to &quot;安装依赖&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">npm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> install</span></span></code></pre></div><h3 id="运行所有测试" tabindex="-1">运行所有测试 <a class="header-anchor" href="#运行所有测试" aria-label="Permalink to &quot;运行所有测试&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">npm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> test</span></span></code></pre></div><h3 id="运行测试并生成覆盖率报告" tabindex="-1">运行测试并生成覆盖率报告 <a class="header-anchor" href="#运行测试并生成覆盖率报告" aria-label="Permalink to &quot;运行测试并生成覆盖率报告&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">npm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> run</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> test:coverage</span></span></code></pre></div><h3 id="运行测试-ui" tabindex="-1">运行测试 UI <a class="header-anchor" href="#运行测试-ui" aria-label="Permalink to &quot;运行测试 UI&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">npm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> run</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> test:ui</span></span></code></pre></div><h3 id="运行特定测试文件" tabindex="-1">运行特定测试文件 <a class="header-anchor" href="#运行特定测试文件" aria-label="Permalink to &quot;运行特定测试文件&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">npm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> test</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> TopmeansApiService.test.js</span></span></code></pre></div><h2 id="测试覆盖范围" tabindex="-1">测试覆盖范围 <a class="header-anchor" href="#测试覆盖范围" aria-label="Permalink to &quot;测试覆盖范围&quot;">​</a></h2><h3 id="服务层测试" tabindex="-1">服务层测试 <a class="header-anchor" href="#服务层测试" aria-label="Permalink to &quot;服务层测试&quot;">​</a></h3><h4 id="topmeansapiservice" tabindex="-1">TopmeansApiService <a class="header-anchor" href="#topmeansapiservice" aria-label="Permalink to &quot;TopmeansApiService&quot;">​</a></h4><ul><li>✅ API 调用方法测试</li><li>✅ 错误处理测试</li><li>✅ 请求取消功能测试</li><li>✅ 连接检查测试</li></ul><h4 id="topmeansformmanager" tabindex="-1">TopmeansFormManager <a class="header-anchor" href="#topmeansformmanager" aria-label="Permalink to &quot;TopmeansFormManager&quot;">​</a></h4><ul><li>✅ 表单数据持久化测试</li><li>✅ 数据验证测试</li><li>✅ 数据合并测试</li><li>✅ 导入导出功能测试</li></ul><h4 id="topmeansmapservice" tabindex="-1">TopmeansMapService <a class="header-anchor" href="#topmeansmapservice" aria-label="Permalink to &quot;TopmeansMapService&quot;">​</a></h4><ul><li>✅ 地图初始化测试</li><li>✅ 自动完成功能测试</li><li>✅ 地理编码测试</li><li>✅ 地图实例管理测试</li></ul><h4 id="topmeansmarkdownservice" tabindex="-1">TopmeansMarkdownService <a class="header-anchor" href="#topmeansmarkdownservice" aria-label="Permalink to &quot;TopmeansMarkdownService&quot;">​</a></h4><ul><li>✅ Markdown 解析测试</li><li>✅ 插件配置测试</li><li>✅ 自定义规则测试</li><li>✅ 错误处理测试</li></ul><h4 id="topmeansscrollmanager" tabindex="-1">TopmeansScrollManager <a class="header-anchor" href="#topmeansscrollmanager" aria-label="Permalink to &quot;TopmeansScrollManager&quot;">​</a></h4><ul><li>✅ 滚动监听测试</li><li>✅ 用户滚动检测测试</li><li>✅ 自动滚动管理测试</li><li>✅ 状态管理测试</li></ul><h4 id="topmeansstylemanager" tabindex="-1">TopmeansStyleManager <a class="header-anchor" href="#topmeansstylemanager" aria-label="Permalink to &quot;TopmeansStyleManager&quot;">​</a></h4><ul><li>✅ 样式注入测试</li><li>✅ 主题切换测试</li><li>✅ 样式监控测试</li><li>✅ 资源清理测试</li></ul><h3 id="集成测试" tabindex="-1">集成测试 <a class="header-anchor" href="#集成测试" aria-label="Permalink to &quot;集成测试&quot;">​</a></h3><h4 id="服务集成测试" tabindex="-1">服务集成测试 <a class="header-anchor" href="#服务集成测试" aria-label="Permalink to &quot;服务集成测试&quot;">​</a></h4><ul><li>✅ 表单和 API 集成</li><li>✅ 地图和样式集成</li><li>✅ 滚动和内容集成</li><li>✅ 错误处理集成</li><li>✅ 数据持久化集成</li><li>✅ 性能测试</li><li>✅ 清理功能集成</li><li>✅ 真实场景测试</li></ul><h2 id="测试配置" tabindex="-1">测试配置 <a class="header-anchor" href="#测试配置" aria-label="Permalink to &quot;测试配置&quot;">​</a></h2><h3 id="vitest-配置" tabindex="-1">Vitest 配置 <a class="header-anchor" href="#vitest-配置" aria-label="Permalink to &quot;Vitest 配置&quot;">​</a></h3><p>测试使用 <code>vitest.config.js</code> 进行配置：</p><ul><li>使用 jsdom 环境模拟浏览器环境</li><li>配置路径别名</li><li>设置覆盖率报告</li><li>配置测试设置文件</li></ul><h3 id="测试设置" tabindex="-1">测试设置 <a class="header-anchor" href="#测试设置" aria-label="Permalink to &quot;测试设置&quot;">​</a></h3><p><code>tests/setup.js</code> 包含：</p><ul><li>环境变量模拟</li><li>DOM API 模拟</li><li>第三方库模拟</li><li>全局测试清理</li></ul><h2 id="测试最佳实践" tabindex="-1">测试最佳实践 <a class="header-anchor" href="#测试最佳实践" aria-label="Permalink to &quot;测试最佳实践&quot;">​</a></h2><h3 id="_1-测试结构" tabindex="-1">1. 测试结构 <a class="header-anchor" href="#_1-测试结构" aria-label="Permalink to &quot;1. 测试结构&quot;">​</a></h3><p>每个测试文件遵循以下结构：</p><div class="language-javascript vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { describe, it, expect, vi, beforeEach, afterEach } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;vitest&#39;</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { ServiceClass } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;@services/ServiceClass&#39;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">describe</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;ServiceClass&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, () </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  let</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> serviceInstance</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  beforeEach</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(() </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 设置测试环境</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    serviceInstance </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> new</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ServiceClass</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">()</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  })</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  afterEach</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(() </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 清理测试环境</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    vi.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">clearAllMocks</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">()</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  })</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  describe</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;methodName&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, () </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    it</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;should do something&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, () </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">      // 测试实现</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    })</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  })</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">})</span></span></code></pre></div><h3 id="_2-mock-策略" tabindex="-1">2. Mock 策略 <a class="header-anchor" href="#_2-mock-策略" aria-label="Permalink to &quot;2. Mock 策略&quot;">​</a></h3><ul><li>使用 <code>vi.fn()</code> 创建函数模拟</li><li>使用 <code>vi.spyOn()</code> 监视方法调用</li><li>使用 <code>vi.mock()</code> 模拟模块</li><li>使用 <code>vi.stubEnv()</code> 模拟环境变量</li></ul><h3 id="_3-异步测试" tabindex="-1">3. 异步测试 <a class="header-anchor" href="#_3-异步测试" aria-label="Permalink to &quot;3. 异步测试&quot;">​</a></h3><div class="language-javascript vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">it</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;should handle async operation&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">async</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> () </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> result</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> await</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> serviceInstance.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">asyncMethod</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">()</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  expect</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(result).</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">toBe</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(expectedValue)</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">})</span></span></code></pre></div><h3 id="_4-错误测试" tabindex="-1">4. 错误测试 <a class="header-anchor" href="#_4-错误测试" aria-label="Permalink to &quot;4. 错误测试&quot;">​</a></h3><div class="language-javascript vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">it</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;should throw error when invalid input&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">async</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> () </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  await</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> expect</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(serviceInstance.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">method</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(invalidInput))</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    .rejects.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">toThrow</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;Expected error message&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">})</span></span></code></pre></div><h2 id="覆盖率目标" tabindex="-1">覆盖率目标 <a class="header-anchor" href="#覆盖率目标" aria-label="Permalink to &quot;覆盖率目标&quot;">​</a></h2><ul><li>语句覆盖率：&gt; 90%</li><li>分支覆盖率：&gt; 85%</li><li>函数覆盖率：&gt; 95%</li><li>行覆盖率：&gt; 90%</li></ul><h2 id="持续集成" tabindex="-1">持续集成 <a class="header-anchor" href="#持续集成" aria-label="Permalink to &quot;持续集成&quot;">​</a></h2><p>测试配置支持 CI/CD 环境：</p><ul><li>自动运行测试套件</li><li>生成覆盖率报告</li><li>上传测试结果</li><li>失败时阻止部署</li></ul><h2 id="故障排除" tabindex="-1">故障排除 <a class="header-anchor" href="#故障排除" aria-label="Permalink to &quot;故障排除&quot;">​</a></h2><h3 id="常见问题" tabindex="-1">常见问题 <a class="header-anchor" href="#常见问题" aria-label="Permalink to &quot;常见问题&quot;">​</a></h3><ol><li><p><strong>测试环境问题</strong></p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 清理缓存</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">npm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> run</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> clear-cache</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 重新安装依赖</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">rm</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> -rf</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> node_modules</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> &amp;&amp; </span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">npm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> install</span></span></code></pre></div></li><li><p><strong>Mock 问题</strong></p><ul><li>确保在 <code>beforeEach</code> 中重置 mocks</li><li>检查 mock 函数的调用次数和参数</li></ul></li><li><p><strong>异步测试超时</strong></p><ul><li>增加测试超时时间</li><li>检查异步操作是否正确等待</li></ul></li></ol><h3 id="调试测试" tabindex="-1">调试测试 <a class="header-anchor" href="#调试测试" aria-label="Permalink to &quot;调试测试&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 运行单个测试文件并显示详细输出</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">npm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> test</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> --</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> --reporter=verbose</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> TopmeansApiService.test.js</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 运行测试并保持监听模式</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">npm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> test</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> --</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> --watch</span></span></code></pre></div><h2 id="贡献指南" tabindex="-1">贡献指南 <a class="header-anchor" href="#贡献指南" aria-label="Permalink to &quot;贡献指南&quot;">​</a></h2><h3 id="添加新测试" tabindex="-1">添加新测试 <a class="header-anchor" href="#添加新测试" aria-label="Permalink to &quot;添加新测试&quot;">​</a></h3><ol><li>在相应的测试目录中创建测试文件</li><li>遵循现有的测试结构和命名约定</li><li>确保测试覆盖所有主要功能</li><li>添加适当的错误处理测试</li><li>更新此文档</li></ol><h3 id="测试代码审查" tabindex="-1">测试代码审查 <a class="header-anchor" href="#测试代码审查" aria-label="Permalink to &quot;测试代码审查&quot;">​</a></h3><ul><li>确保测试清晰易懂</li><li>验证测试覆盖了所有边界情况</li><li>检查 mock 使用是否合理</li><li>确认测试不会产生副作用</li></ul><h2 id="性能测试" tabindex="-1">性能测试 <a class="header-anchor" href="#性能测试" aria-label="Permalink to &quot;性能测试&quot;">​</a></h2><p>性能测试确保应用在各种负载下表现良好：</p><ul><li>大量数据处理测试</li><li>并发操作测试</li><li>内存使用测试</li><li>响应时间测试</li></ul><h2 id="安全测试" tabindex="-1">安全测试 <a class="header-anchor" href="#安全测试" aria-label="Permalink to &quot;安全测试&quot;">​</a></h2><p>安全测试验证应用的安全性：</p><ul><li>输入验证测试</li><li>XSS 防护测试</li><li>CSRF 防护测试</li><li>数据加密测试</li></ul><h2 id="未来计划" tabindex="-1">未来计划 <a class="header-anchor" href="#未来计划" aria-label="Permalink to &quot;未来计划&quot;">​</a></h2><ul><li>[ ] 添加端到端测试</li><li>[ ] 添加可视化回归测试</li><li>[ ] 添加负载测试</li><li>[ ] 添加安全测试</li><li>[ ] 添加无障碍测试</li></ul>`,72)]))}const g=a(e,[["render",t]]);export{c as __pageData,g as default};
