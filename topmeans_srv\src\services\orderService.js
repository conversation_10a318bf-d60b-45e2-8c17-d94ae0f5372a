const mysql = require('mysql2/promise');
const logger = require('../log/logger');

// 数据库连接池配置
const pool = mysql.createPool({
    host: '*************',
    port: 3306,
    user: 'root',
    password: 'Asuse12.',
    database: 'user_db',
    waitForConnections: true,
    connectionLimit: 1000,
    queueLimit: 0,
    connectTimeout: 10000,
    enableKeepAlive: true,
    keepAliveInitialDelay: 10000
});

class OrderService {
  /**
   * 初始化订单表
   */
  async initOrderTable() {
    try {
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS orders (
          id INT AUTO_INCREMENT PRIMARY KEY,
          order_id VARCHAR(64) UNIQUE NOT NULL COMMENT '订单号',
          user_id INT COMMENT '用户ID',
          user_account VARCHAR(100) COMMENT '用户账号',
          amount DECIMAL(10,2) NOT NULL COMMENT '订单金额',
          subject VARCHAR(256) NOT NULL COMMENT '订单标题',
          goods TEXT COMMENT '商品信息JSON',
          payment_method VARCHAR(20) DEFAULT 'alipay' COMMENT '支付方式',
          status ENUM('created', 'pending', 'paid', 'failed', 'cancelled', 'closed') DEFAULT 'created' COMMENT '订单状态',
          alipay_trade_no VARCHAR(64) COMMENT '支付宝交易号',
          qr_code TEXT COMMENT '支付二维码',
          notify_data TEXT COMMENT '支付通知数据',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          paid_at TIMESTAMP NULL COMMENT '支付时间',
          expired_at TIMESTAMP NULL COMMENT '过期时间',
          INDEX idx_order_id (order_id),
          INDEX idx_user_id (user_id),
          INDEX idx_status (status),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
      `;
      
      await pool.execute(createTableSQL);
      logger.info('订单表初始化成功');
      return { success: true, message: '订单表初始化成功' };
    } catch (error) {
      logger.error('初始化订单表失败:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * 创建订单
   * @param {Object} orderData 订单数据
   * @returns {Object} 创建结果
   */
  async createOrder(orderData) {
    try {
      const {
        orderId,
        userId,
        userAccount,
        amount,
        subject,
        goods,
        paymentMethod = 'alipay',
        qrCode
      } = orderData;

      // 计算过期时间（15分钟后）
      const expiredAt = new Date(Date.now() + 15 * 60 * 1000);

      const insertSQL = `
        INSERT INTO orders (
          order_id, user_id, user_account, amount, subject, goods, 
          payment_method, status, qr_code, expired_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'created', ?, ?)
      `;

      const [result] = await pool.execute(insertSQL, [
        orderId, userId, userAccount, amount, subject, 
        JSON.stringify(goods), paymentMethod, qrCode, expiredAt
      ]);

      logger.info('订单创建成功:', { orderId, insertId: result.insertId });
      
      return {
        success: true,
        orderId,
        insertId: result.insertId,
        message: '订单创建成功'
      };
    } catch (error) {
      logger.error('创建订单失败:', error);
      return {
        success: false,
        message: error.message || '创建订单失败'
      };
    }
  }

  /**
   * 更新订单状态
   * @param {string} orderId 订单号
   * @param {string} status 新状态
   * @param {Object} extraData 额外数据
   * @returns {Object} 更新结果
   */
  async updateOrderStatus(orderId, status, extraData = {}) {
    try {
      let updateSQL = 'UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP';
      let params = [status];

      // 根据状态添加额外字段
      if (status === 'paid' && extraData.alipayTradeNo) {
        updateSQL += ', alipay_trade_no = ?, paid_at = CURRENT_TIMESTAMP';
        params.push(extraData.alipayTradeNo);
      }

      if (extraData.notifyData) {
        updateSQL += ', notify_data = ?';
        params.push(JSON.stringify(extraData.notifyData));
      }

      updateSQL += ' WHERE order_id = ?';
      params.push(orderId);

      const [result] = await pool.execute(updateSQL, params);

      if (result.affectedRows > 0) {
        logger.info('订单状态更新成功:', { orderId, status });
        return {
          success: true,
          message: '订单状态更新成功'
        };
      } else {
        return {
          success: false,
          message: '订单不存在或状态未变更'
        };
      }
    } catch (error) {
      logger.error('更新订单状态失败:', error);
      return {
        success: false,
        message: error.message || '更新订单状态失败'
      };
    }
  }

  /**
   * 查询订单信息
   * @param {string} orderId 订单号
   * @returns {Object} 查询结果
   */
  async getOrder(orderId) {
    try {
      const selectSQL = 'SELECT * FROM orders WHERE order_id = ?';
      const [rows] = await pool.execute(selectSQL, [orderId]);

      if (rows.length > 0) {
        const order = rows[0];
        // 解析JSON字段
        if (order.goods) {
          try {
            order.goods = JSON.parse(order.goods);
          } catch (e) {
            logger.warn('解析订单商品信息失败:', e);
          }
        }
        if (order.notify_data) {
          try {
            order.notify_data = JSON.parse(order.notify_data);
          } catch (e) {
            logger.warn('解析通知数据失败:', e);
          }
        }

        return {
          success: true,
          order
        };
      } else {
        return {
          success: false,
          message: '订单不存在'
        };
      }
    } catch (error) {
      logger.error('查询订单失败:', error);
      return {
        success: false,
        message: error.message || '查询订单失败'
      };
    }
  }

  /**
   * 获取用户订单列表
   * @param {number} userId 用户ID
   * @param {number} page 页码
   * @param {number} limit 每页数量
   * @returns {Object} 查询结果
   */
  async getUserOrders(userId, page = 1, limit = 10) {
    try {
      const offset = (page - 1) * limit;
      
      const countSQL = 'SELECT COUNT(*) as total FROM orders WHERE user_id = ?';
      const [countResult] = await pool.execute(countSQL, [userId]);
      const total = countResult[0].total;

      const selectSQL = `
        SELECT order_id, amount, subject, status, created_at, paid_at 
        FROM orders 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
      `;
      const [rows] = await pool.execute(selectSQL, [userId, limit, offset]);

      return {
        success: true,
        orders: rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('获取用户订单列表失败:', error);
      return {
        success: false,
        message: error.message || '获取订单列表失败'
      };
    }
  }

  /**
   * 关闭过期订单
   * @returns {Object} 处理结果
   */
  async closeExpiredOrders() {
    try {
      const updateSQL = `
        UPDATE orders 
        SET status = 'closed', updated_at = CURRENT_TIMESTAMP 
        WHERE status IN ('created', 'pending') 
        AND expired_at < CURRENT_TIMESTAMP
      `;
      
      const [result] = await pool.execute(updateSQL);
      
      logger.info(`关闭过期订单数量: ${result.affectedRows}`);
      
      return {
        success: true,
        closedCount: result.affectedRows,
        message: `成功关闭${result.affectedRows}个过期订单`
      };
    } catch (error) {
      logger.error('关闭过期订单失败:', error);
      return {
        success: false,
        message: error.message || '关闭过期订单失败'
      };
    }
  }
}

module.exports = new OrderService();
