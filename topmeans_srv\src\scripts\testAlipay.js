require('dotenv').config();
const { AlipaySdk } = require('alipay-sdk');
const logger = require('../log/logger');

function formatPrivateKey(key) {
  if (!key) return '';
  if (key.includes('-----BEGIN')) return key;
  return `-----BEGIN RSA PRIVATE KEY-----\n${key}\n-----END RSA PRIVATE KEY-----`;
}

function formatPublicKey(key) {
  if (!key) return '';
  if (key.includes('-----BEGIN')) return key;
  return `-----BEGIN PUBLIC KEY-----\n${key}\n-----END PUBLIC KEY-----`;
}

async function testAlipayConfig() {
  try {
    console.log('测试支付宝配置...');
    console.log('AppID:', process.env.alipayAppID);
    console.log('Gateway:', process.env.ALIPAY_GATEWAY || "https://openapi.alipay.com/gateway.do");
    
    const privateKey = formatPrivateKey(process.env.alipayPrivateKey);
    const publicKey = formatPublicKey(process.env.alipayPublicKey);
    
    console.log('私钥长度:', privateKey.length);
    console.log('公钥长度:', publicKey.length);
    
    const alipaySdk = new AlipaySdk({
      appId: process.env.alipayAppID,
      privateKey: privateKey,
      alipayPublicKey: publicKey,
      gateway: process.env.ALIPAY_GATEWAY || "https://openapi.alipay.com/gateway.do",
      signType: 'RSA2',
      charset: 'utf-8',
      version: '1.0'
    });
    
    console.log('SDK初始化成功');
    
    // 测试创建支付订单
    const orderId = `TEST_${Date.now()}`;
    console.log('测试订单ID:', orderId);
    
    const result = await alipaySdk.exec('alipay.trade.precreate', {
      notify_url: `${process.env.VITE_BACKEND_SRV_URL}/api/payment/notify`,
      bizContent: {
        out_trade_no: orderId,
        total_amount: '0.01',
        subject: '测试订单',
        store_id: 'TopMeans_Store',
        timeout_express: '15m'
      }
    });
    
    console.log('支付宝API响应:', JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error('测试失败:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
  }
}

testAlipayConfig();
