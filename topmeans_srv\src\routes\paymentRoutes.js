const express = require('express');
const alipayService = require('../services/alipayService');
const orderService = require('../services/orderService');
const logger = require('../log/logger');

const router = express.Router();

/**
 * 创建支付订单
 * POST /api/payment/create
 */
router.post('/create', async (req, res) => {
  try {
    const { amount, subject, goods, userId, userAccount, paymentMethod = 'alipay' } = req.body;
    
    // 验证必要参数
    if (!amount || !subject) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：amount, subject'
      });
    }

    // 验证金额格式
    const orderAmount = parseFloat(amount);
    if (isNaN(orderAmount) || orderAmount <= 0) {
      return res.status(400).json({
        success: false,
        message: '订单金额格式错误'
      });
    }

    // 生成订单ID
    const orderId = `TM_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
    
    // 构建订单对象
    const order = {
      orderId,
      amount: orderAmount,
      subject,
      goods: goods || {},
      userId,
      userAccount
    };

    // 根据支付方式处理
    if (paymentMethod === 'alipay') {
      // 调用支付宝创建支付
      const paymentResult = await alipayService.createPayment(order);
      
      if (paymentResult.success) {
        // 保存订单到数据库
        const orderData = {
          ...order,
          paymentMethod,
          qrCode: paymentResult.paymentUrl // 保存支付URL
        };

        const saveResult = await orderService.createOrder(orderData);

        if (saveResult.success) {
          // 更新订单状态为pending
          await orderService.updateOrderStatus(orderId, 'pending');

          res.json({
            success: true,
            orderId,
            paymentUrl: paymentResult.paymentUrl,
            amount: orderAmount,
            subject,
            message: '订单创建成功'
          });
        } else {
          res.status(500).json({
            success: false,
            message: '保存订单失败：' + saveResult.message
          });
        }
      } else {
        res.status(500).json({
          success: false,
          message: '创建支付失败：' + paymentResult.message
        });
      }
    } else {
      res.status(400).json({
        success: false,
        message: '暂不支持该支付方式'
      });
    }
  } catch (error) {
    logger.error('创建支付订单失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 查询订单状态
 * GET /api/payment/status/:orderId
 */
router.get('/status/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    
    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: '订单号不能为空'
      });
    }

    // 从数据库查询订单
    const orderResult = await orderService.getOrder(orderId);
    
    if (!orderResult.success) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    const order = orderResult.order;
    
    // 如果订单已支付，直接返回
    if (order.status === 'paid') {
      return res.json({
        success: true,
        status: 'paid',
        orderId,
        amount: order.amount,
        paidAt: order.paid_at,
        alipayTradeNo: order.alipay_trade_no
      });
    }

    // 如果订单状态为pending，查询支付宝最新状态
    if (order.status === 'pending' && order.payment_method === 'alipay') {
      const queryResult = await alipayService.queryPayment(orderId);
      
      if (queryResult.success) {
        // 根据支付宝返回的状态更新本地订单
        if (queryResult.tradeStatus === 'TRADE_SUCCESS') {
          await orderService.updateOrderStatus(orderId, 'paid', {
            alipayTradeNo: queryResult.tradeNo,
            notifyData: queryResult
          });
          
          return res.json({
            success: true,
            status: 'paid',
            orderId,
            amount: order.amount,
            alipayTradeNo: queryResult.tradeNo
          });
        } else if (queryResult.tradeStatus === 'TRADE_CLOSED') {
          await orderService.updateOrderStatus(orderId, 'closed');
          
          return res.json({
            success: true,
            status: 'closed',
            orderId,
            message: '订单已关闭'
          });
        }
      }
    }

    // 返回当前订单状态
    res.json({
      success: true,
      status: order.status,
      orderId,
      amount: order.amount,
      createdAt: order.created_at,
      expiredAt: order.expired_at
    });
    
  } catch (error) {
    logger.error('查询订单状态失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 支付宝同步返回
 * GET /api/payment/return
 */
router.get('/return', async (req, res) => {
  try {
    logger.info('收到支付宝同步返回:', req.query);

    const {
      out_trade_no: orderId,
      trade_no: alipayTradeNo,
      total_amount: totalAmount
    } = req.query;

    if (orderId) {
      // 查询订单状态
      const orderResult = await orderService.getOrder(orderId);

      if (orderResult.success) {
        // 重定向到前端成功页面
        res.redirect(`${process.env.FRONTEND_URL}/payment/success?orderId=${orderId}`);
      } else {
        res.redirect(`${process.env.FRONTEND_URL}/payment/error?message=订单不存在`);
      }
    } else {
      res.redirect(`${process.env.FRONTEND_URL}/payment/error?message=缺少订单号`);
    }
  } catch (error) {
    logger.error('处理支付宝同步返回失败:', error);
    res.redirect(`${process.env.FRONTEND_URL}/payment/error?message=处理失败`);
  }
});

/**
 * 支付宝异步通知
 * POST /api/payment/notify
 */
router.post('/notify', async (req, res) => {
  try {
    logger.info('收到支付宝异步通知:', req.body);
    
    // 验证签名
    const isValid = alipayService.verifyNotify(req.body);
    
    if (!isValid) {
      logger.error('支付宝通知签名验证失败');
      return res.send('fail');
    }

    const {
      out_trade_no: orderId,
      trade_no: alipayTradeNo,
      trade_status: tradeStatus,
      total_amount: totalAmount
    } = req.body;

    // 查询订单是否存在
    const orderResult = await orderService.getOrder(orderId);
    
    if (!orderResult.success) {
      logger.error('支付通知中的订单不存在:', orderId);
      return res.send('fail');
    }

    const order = orderResult.order;

    // 验证金额
    if (parseFloat(totalAmount) !== parseFloat(order.amount)) {
      logger.error('支付金额不匹配:', {
        notifyAmount: totalAmount,
        orderAmount: order.amount
      });
      return res.send('fail');
    }

    // 根据交易状态更新订单
    if (tradeStatus === 'TRADE_SUCCESS') {
      const updateResult = await orderService.updateOrderStatus(orderId, 'paid', {
        alipayTradeNo,
        notifyData: req.body
      });
      
      if (updateResult.success) {
        logger.info('订单支付成功:', { orderId, alipayTradeNo });
        res.send('success');
      } else {
        logger.error('更新订单状态失败:', updateResult.message);
        res.send('fail');
      }
    } else if (tradeStatus === 'TRADE_CLOSED') {
      await orderService.updateOrderStatus(orderId, 'closed', {
        notifyData: req.body
      });
      logger.info('订单已关闭:', orderId);
      res.send('success');
    } else {
      logger.info('其他交易状态:', { orderId, tradeStatus });
      res.send('success');
    }
    
  } catch (error) {
    logger.error('处理支付宝通知失败:', error);
    res.send('fail');
  }
});

/**
 * 取消订单
 * POST /api/payment/cancel/:orderId
 */
router.post('/cancel/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    
    // 查询订单
    const orderResult = await orderService.getOrder(orderId);
    
    if (!orderResult.success) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    const order = orderResult.order;
    
    // 只有未支付的订单才能取消
    if (order.status === 'paid') {
      return res.status(400).json({
        success: false,
        message: '已支付的订单无法取消'
      });
    }

    // 如果是支付宝订单，尝试关闭支付宝订单
    if (order.payment_method === 'alipay' && order.status === 'pending') {
      await alipayService.closeOrder(orderId);
    }

    // 更新本地订单状态
    const updateResult = await orderService.updateOrderStatus(orderId, 'cancelled');
    
    if (updateResult.success) {
      res.json({
        success: true,
        message: '订单已取消'
      });
    } else {
      res.status(500).json({
        success: false,
        message: '取消订单失败'
      });
    }
    
  } catch (error) {
    logger.error('取消订单失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 获取用户订单列表
 * GET /api/payment/orders
 */
router.get('/orders', async (req, res) => {
  try {
    const { userId, page = 1, limit = 10 } = req.query;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '用户ID不能为空'
      });
    }

    const result = await orderService.getUserOrders(
      parseInt(userId), 
      parseInt(page), 
      parseInt(limit)
    );
    
    res.json(result);
    
  } catch (error) {
    logger.error('获取用户订单列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

module.exports = router;
