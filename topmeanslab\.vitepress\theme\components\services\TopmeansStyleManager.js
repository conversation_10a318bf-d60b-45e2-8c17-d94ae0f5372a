/**
 * 样式管理器 - 负责高德地图样式和主题相关的操作
 */
export class TopmeansStyleManager {
    constructor() {
        this.amapObserver = null;
        this.amapStyleInterval = null;
    }

    /**
     * 确保高德地图提示框样式正确应用
     */
    ensureAmapSuggestStyles() {
        if (typeof window === 'undefined') return;

        // 注入强制样式到页面头部
        this.injectAmapStyles();

        // 创建一个更频繁的检查机制
        this.startAmapStyleMonitor();
    }

    /**
     * 注入强制样式
     */
    injectAmapStyles() {
        // 移除已存在的样式
        const existingStyle = document.getElementById('amap-dark-theme-fix');
        if (existingStyle) {
            existingStyle.remove();
        }

        const style = document.createElement('style');
        style.id = 'amap-dark-theme-fix';
        style.innerHTML = `
            /* 高德地图提示框强制样式 - 最高优先级 */
            .amap-sug-result,
            .amap-ui-autocomplete,
            div[class*="amap"][class*="sug"],
            div[class*="amap"][class*="auto"] {
                background: var(--vp-c-bg) !important;
                border: 1px solid var(--vp-c-divider) !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
                z-index: 99999 !important;
            }

            .amap-sug-result .auto-item,
            .amap-sug-result li,
            .amap-ui-autocomplete .amap-ui-autocomplete-item,
            .amap-ui-autocomplete li,
            div[class*="amap"] .auto-item,
            div[class*="amap"] li {
                background: var(--vp-c-bg) !important;
                color: var(--vp-c-text-1) !important;
                border-bottom: 1px solid var(--vp-c-divider-light) !important;
                padding: 12px 16px !important;
                font-size: 0.875rem !important;
                line-height: 1.4 !important;
            }

            .amap-sug-result .auto-item *,
            .amap-sug-result li *,
            .amap-ui-autocomplete .amap-ui-autocomplete-item *,
            .amap-ui-autocomplete li *,
            div[class*="amap"] .auto-item *,
            div[class*="amap"] li * {
                color: var(--vp-c-text-1) !important;
            }

            /* 深色主题特殊处理 */
            .dark .amap-sug-result,
            .dark .amap-ui-autocomplete,
            .dark div[class*="amap"][class*="sug"],
            .dark div[class*="amap"][class*="auto"] {
                background: var(--vp-c-bg-soft) !important;
                border-color: var(--vp-c-divider) !important;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3) !important;
            }

            .dark .amap-sug-result .auto-item,
            .dark .amap-sug-result li,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item,
            .dark .amap-ui-autocomplete li,
            .dark div[class*="amap"] .auto-item,
            .dark div[class*="amap"] li {
                background: var(--vp-c-bg-soft) !important;
                color: #ffffff !important;
                border-bottom-color: var(--vp-c-divider) !important;
            }

            .dark .amap-sug-result .auto-item *,
            .dark .amap-sug-result li *,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item *,
            .dark .amap-ui-autocomplete li *,
            .dark div[class*="amap"] .auto-item *,
            .dark div[class*="amap"] li * {
                color: #ffffff !important;
            }

            /* 悬停效果 */
            .amap-sug-result .auto-item:hover,
            .amap-sug-result li:hover,
            .amap-ui-autocomplete .amap-ui-autocomplete-item:hover,
            .amap-ui-autocomplete li:hover {
                background: var(--vp-c-brand-soft) !important;
                color: var(--vp-c-brand-1) !important;
            }

            .dark .amap-sug-result .auto-item:hover,
            .dark .amap-sug-result li:hover,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item:hover,
            .dark .amap-ui-autocomplete li:hover {
                background: var(--vp-c-brand-dimm) !important;
                color: #ffffff !important;
            }

            .amap-sug-result .auto-item:hover *,
            .amap-sug-result li:hover *,
            .amap-ui-autocomplete .amap-ui-autocomplete-item:hover *,
            .amap-ui-autocomplete li:hover *,
            .dark .amap-sug-result .auto-item:hover *,
            .dark .amap-sug-result li:hover *,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item:hover *,
            .dark .amap-ui-autocomplete li:hover * {
                color: inherit !important;
            }
        `;

        document.head.appendChild(style);
    }

    /**
     * 启动样式监控器
     */
    startAmapStyleMonitor() {
        // 清除之前的监控器
        if (this.amapStyleInterval) {
            clearInterval(this.amapStyleInterval);
        }

        // 每500ms检查一次高德地图提示框
        this.amapStyleInterval = setInterval(() => {
            this.forceApplyAmapStyles();
        }, 500);

        // 创建 DOM 观察器
        const observer = new MutationObserver((mutations) => {
            let shouldCheck = false;
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const className = node.className || '';
                        if (className.includes('amap') || className.includes('sug') || className.includes('auto')) {
                            shouldCheck = true;
                        }
                    }
                });
            });

            if (shouldCheck) {
                setTimeout(() => this.forceApplyAmapStyles(), 100);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        this.amapObserver = observer;
    }

    /**
     * 强制应用样式
     */
    forceApplyAmapStyles() {
        if (typeof window === 'undefined') return;

        try {
            const isDark = document.documentElement.classList.contains('dark');
            const textColor = isDark ? '#ffffff' : 'var(--vp-c-text-1)';
            const bgColor = isDark ? 'var(--vp-c-bg-soft)' : 'var(--vp-c-bg)';

            // 查找所有可能的高德地图提示框
            const selectors = [
                '.amap-sug-result',
                '.amap-ui-autocomplete',
                'div[class*="amap"][class*="sug"]',
                'div[class*="amap"][class*="auto"]',
                '[class*="amap-sug"]',
                '[class*="amap-auto"]'
            ];

            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element && element.style) {
                        element.style.setProperty('background', bgColor, 'important');
                        element.style.setProperty('border', '1px solid var(--vp-c-divider)', 'important');
                        element.style.setProperty('border-radius', '8px', 'important');
                        element.style.setProperty('z-index', '99999', 'important');
                    }

                    // 处理子元素
                    const items = element.querySelectorAll('.auto-item, li, .amap-ui-autocomplete-item, [class*="item"]');
                    items.forEach(item => {
                        if (item && item.style) {
                            item.style.setProperty('background', bgColor, 'important');
                            item.style.setProperty('color', textColor, 'important');
                            item.style.setProperty('padding', '12px 16px', 'important');

                            // 处理所有子元素的文字颜色
                            const allChildren = item.querySelectorAll('*');
                            allChildren.forEach(child => {
                                if (child && child.style) {
                                    child.style.setProperty('color', textColor, 'important');
                                }
                            });
                        }
                    });
                });
            });

        } catch (error) {
        }
    }

    /**
     * 检测当前主题模式
     */
    isDarkMode() {
        if (typeof window === 'undefined') return false;
        return document.documentElement.classList.contains('dark');
    }

    /**
     * 获取主题相关的CSS变量值
     */
    getThemeColors() {
        if (typeof window === 'undefined') return {};

        const computedStyle = getComputedStyle(document.documentElement);
        
        return {
            bg: computedStyle.getPropertyValue('--vp-c-bg').trim(),
            bgSoft: computedStyle.getPropertyValue('--vp-c-bg-soft').trim(),
            text1: computedStyle.getPropertyValue('--vp-c-text-1').trim(),
            divider: computedStyle.getPropertyValue('--vp-c-divider').trim(),
            dividerLight: computedStyle.getPropertyValue('--vp-c-divider-light').trim(),
            brand: computedStyle.getPropertyValue('--vp-c-brand').trim(),
            brandSoft: computedStyle.getPropertyValue('--vp-c-brand-soft').trim(),
            brandDimm: computedStyle.getPropertyValue('--vp-c-brand-dimm').trim()
        };
    }

    /**
     * 监听主题变化
     */
    watchThemeChange(callback) {
        if (typeof window === 'undefined') return;

        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const isDark = document.documentElement.classList.contains('dark');
                    callback(isDark);
                }
            });
        });

        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['class']
        });

        return observer;
    }

    /**
     * 应用自定义样式
     */
    applyCustomStyles(styleId, cssText) {
        // 移除已存在的样式
        const existingStyle = document.getElementById(styleId);
        if (existingStyle) {
            existingStyle.remove();
        }

        const style = document.createElement('style');
        style.id = styleId;
        style.innerHTML = cssText;
        document.head.appendChild(style);
    }

    /**
     * 移除自定义样式
     */
    removeCustomStyles(styleId) {
        const existingStyle = document.getElementById(styleId);
        if (existingStyle) {
            existingStyle.remove();
        }
    }

    /**
     * 清理所有样式监控器和观察器
     */
    cleanup() {
        // 清理高德地图观察器
        if (this.amapObserver) {
            this.amapObserver.disconnect();
            this.amapObserver = null;
        }

        // 清理样式监控器
        if (this.amapStyleInterval) {
            clearInterval(this.amapStyleInterval);
            this.amapStyleInterval = null;
        }

        // 移除注入的样式
        this.removeCustomStyles('amap-dark-theme-fix');
    }

    /**
     * 重新初始化样式管理器
     */
    reinitialize() {
        this.cleanup();
        this.ensureAmapSuggestStyles();
    }

    /**
     * 获取当前样式状态
     */
    getStyleState() {
        return {
            hasAmapObserver: !!this.amapObserver,
            hasStyleInterval: !!this.amapStyleInterval,
            isDarkMode: this.isDarkMode(),
            themeColors: this.getThemeColors()
        };
    }
}

// 创建单例实例
export const styleManager = new TopmeansStyleManager();
