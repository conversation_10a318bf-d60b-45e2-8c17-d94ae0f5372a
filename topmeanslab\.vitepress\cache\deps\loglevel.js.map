{"version": 3, "sources": ["../../../node_modules/loglevel/lib/loglevel.js"], "sourcesContent": ["/*\n* loglevel - https://github.com/pimterry/loglevel\n*\n* Copyright (c) 2013 <PERSON>\n* Licensed under the MIT license.\n*/\n(function (root, definition) {\n    \"use strict\";\n    if (typeof define === 'function' && define.amd) {\n        define(definition);\n    } else if (typeof module === 'object' && module.exports) {\n        module.exports = definition();\n    } else {\n        root.log = definition();\n    }\n}(this, function () {\n    \"use strict\";\n\n    // Slightly dubious tricks to cut down minimized file size\n    var noop = function() {};\n    var undefinedType = \"undefined\";\n    var isIE = (typeof window !== undefinedType) && (typeof window.navigator !== undefinedType) && (\n        /Trident\\/|MSIE /.test(window.navigator.userAgent)\n    );\n\n    var logMethods = [\n        \"trace\",\n        \"debug\",\n        \"info\",\n        \"warn\",\n        \"error\"\n    ];\n\n    var _loggersByName = {};\n    var defaultLogger = null;\n\n    // Cross-browser bind equivalent that works at least back to IE6\n    function bindMethod(obj, methodName) {\n        var method = obj[methodName];\n        if (typeof method.bind === 'function') {\n            return method.bind(obj);\n        } else {\n            try {\n                return Function.prototype.bind.call(method, obj);\n            } catch (e) {\n                // Missing bind shim or IE8 + Modernizr, fallback to wrapping\n                return function() {\n                    return Function.prototype.apply.apply(method, [obj, arguments]);\n                };\n            }\n        }\n    }\n\n    // Trace() doesn't print the message in IE, so for that case we need to wrap it\n    function traceForIE() {\n        if (console.log) {\n            if (console.log.apply) {\n                console.log.apply(console, arguments);\n            } else {\n                // In old IE, native console methods themselves don't have apply().\n                Function.prototype.apply.apply(console.log, [console, arguments]);\n            }\n        }\n        if (console.trace) console.trace();\n    }\n\n    // Build the best logging method possible for this env\n    // Wherever possible we want to bind, not wrap, to preserve stack traces\n    function realMethod(methodName) {\n        if (methodName === 'debug') {\n            methodName = 'log';\n        }\n\n        if (typeof console === undefinedType) {\n            return false; // No method possible, for now - fixed later by enableLoggingWhenConsoleArrives\n        } else if (methodName === 'trace' && isIE) {\n            return traceForIE;\n        } else if (console[methodName] !== undefined) {\n            return bindMethod(console, methodName);\n        } else if (console.log !== undefined) {\n            return bindMethod(console, 'log');\n        } else {\n            return noop;\n        }\n    }\n\n    // These private functions always need `this` to be set properly\n\n    function replaceLoggingMethods() {\n        /*jshint validthis:true */\n        var level = this.getLevel();\n\n        // Replace the actual methods.\n        for (var i = 0; i < logMethods.length; i++) {\n            var methodName = logMethods[i];\n            this[methodName] = (i < level) ?\n                noop :\n                this.methodFactory(methodName, level, this.name);\n        }\n\n        // Define log.log as an alias for log.debug\n        this.log = this.debug;\n\n        // Return any important warnings.\n        if (typeof console === undefinedType && level < this.levels.SILENT) {\n            return \"No console available for logging\";\n        }\n    }\n\n    // In old IE versions, the console isn't present until you first open it.\n    // We build realMethod() replacements here that regenerate logging methods\n    function enableLoggingWhenConsoleArrives(methodName) {\n        return function () {\n            if (typeof console !== undefinedType) {\n                replaceLoggingMethods.call(this);\n                this[methodName].apply(this, arguments);\n            }\n        };\n    }\n\n    // By default, we use closely bound real methods wherever possible, and\n    // otherwise we wait for a console to appear, and then try again.\n    function defaultMethodFactory(methodName, _level, _loggerName) {\n        /*jshint validthis:true */\n        return realMethod(methodName) ||\n               enableLoggingWhenConsoleArrives.apply(this, arguments);\n    }\n\n    function Logger(name, factory) {\n      // Private instance variables.\n      var self = this;\n      /**\n       * The level inherited from a parent logger (or a global default). We\n       * cache this here rather than delegating to the parent so that it stays\n       * in sync with the actual logging methods that we have installed (the\n       * parent could change levels but we might not have rebuilt the loggers\n       * in this child yet).\n       * @type {number}\n       */\n      var inheritedLevel;\n      /**\n       * The default level for this logger, if any. If set, this overrides\n       * `inheritedLevel`.\n       * @type {number|null}\n       */\n      var defaultLevel;\n      /**\n       * A user-specific level for this logger. If set, this overrides\n       * `defaultLevel`.\n       * @type {number|null}\n       */\n      var userLevel;\n\n      var storageKey = \"loglevel\";\n      if (typeof name === \"string\") {\n        storageKey += \":\" + name;\n      } else if (typeof name === \"symbol\") {\n        storageKey = undefined;\n      }\n\n      function persistLevelIfPossible(levelNum) {\n          var levelName = (logMethods[levelNum] || 'silent').toUpperCase();\n\n          if (typeof window === undefinedType || !storageKey) return;\n\n          // Use localStorage if available\n          try {\n              window.localStorage[storageKey] = levelName;\n              return;\n          } catch (ignore) {}\n\n          // Use session cookie as fallback\n          try {\n              window.document.cookie =\n                encodeURIComponent(storageKey) + \"=\" + levelName + \";\";\n          } catch (ignore) {}\n      }\n\n      function getPersistedLevel() {\n          var storedLevel;\n\n          if (typeof window === undefinedType || !storageKey) return;\n\n          try {\n              storedLevel = window.localStorage[storageKey];\n          } catch (ignore) {}\n\n          // Fallback to cookies if local storage gives us nothing\n          if (typeof storedLevel === undefinedType) {\n              try {\n                  var cookie = window.document.cookie;\n                  var cookieName = encodeURIComponent(storageKey);\n                  var location = cookie.indexOf(cookieName + \"=\");\n                  if (location !== -1) {\n                      storedLevel = /^([^;]+)/.exec(\n                          cookie.slice(location + cookieName.length + 1)\n                      )[1];\n                  }\n              } catch (ignore) {}\n          }\n\n          // If the stored level is not valid, treat it as if nothing was stored.\n          if (self.levels[storedLevel] === undefined) {\n              storedLevel = undefined;\n          }\n\n          return storedLevel;\n      }\n\n      function clearPersistedLevel() {\n          if (typeof window === undefinedType || !storageKey) return;\n\n          // Use localStorage if available\n          try {\n              window.localStorage.removeItem(storageKey);\n          } catch (ignore) {}\n\n          // Use session cookie as fallback\n          try {\n              window.document.cookie =\n                encodeURIComponent(storageKey) + \"=; expires=Thu, 01 Jan 1970 00:00:00 UTC\";\n          } catch (ignore) {}\n      }\n\n      function normalizeLevel(input) {\n          var level = input;\n          if (typeof level === \"string\" && self.levels[level.toUpperCase()] !== undefined) {\n              level = self.levels[level.toUpperCase()];\n          }\n          if (typeof level === \"number\" && level >= 0 && level <= self.levels.SILENT) {\n              return level;\n          } else {\n              throw new TypeError(\"log.setLevel() called with invalid level: \" + input);\n          }\n      }\n\n      /*\n       *\n       * Public logger API - see https://github.com/pimterry/loglevel for details\n       *\n       */\n\n      self.name = name;\n\n      self.levels = { \"TRACE\": 0, \"DEBUG\": 1, \"INFO\": 2, \"WARN\": 3,\n          \"ERROR\": 4, \"SILENT\": 5};\n\n      self.methodFactory = factory || defaultMethodFactory;\n\n      self.getLevel = function () {\n          if (userLevel != null) {\n            return userLevel;\n          } else if (defaultLevel != null) {\n            return defaultLevel;\n          } else {\n            return inheritedLevel;\n          }\n      };\n\n      self.setLevel = function (level, persist) {\n          userLevel = normalizeLevel(level);\n          if (persist !== false) {  // defaults to true\n              persistLevelIfPossible(userLevel);\n          }\n\n          // NOTE: in v2, this should call rebuild(), which updates children.\n          return replaceLoggingMethods.call(self);\n      };\n\n      self.setDefaultLevel = function (level) {\n          defaultLevel = normalizeLevel(level);\n          if (!getPersistedLevel()) {\n              self.setLevel(level, false);\n          }\n      };\n\n      self.resetLevel = function () {\n          userLevel = null;\n          clearPersistedLevel();\n          replaceLoggingMethods.call(self);\n      };\n\n      self.enableAll = function(persist) {\n          self.setLevel(self.levels.TRACE, persist);\n      };\n\n      self.disableAll = function(persist) {\n          self.setLevel(self.levels.SILENT, persist);\n      };\n\n      self.rebuild = function () {\n          if (defaultLogger !== self) {\n              inheritedLevel = normalizeLevel(defaultLogger.getLevel());\n          }\n          replaceLoggingMethods.call(self);\n\n          if (defaultLogger === self) {\n              for (var childName in _loggersByName) {\n                _loggersByName[childName].rebuild();\n              }\n          }\n      };\n\n      // Initialize all the internal levels.\n      inheritedLevel = normalizeLevel(\n          defaultLogger ? defaultLogger.getLevel() : \"WARN\"\n      );\n      var initialLevel = getPersistedLevel();\n      if (initialLevel != null) {\n          userLevel = normalizeLevel(initialLevel);\n      }\n      replaceLoggingMethods.call(self);\n    }\n\n    /*\n     *\n     * Top-level API\n     *\n     */\n\n    defaultLogger = new Logger();\n\n    defaultLogger.getLogger = function getLogger(name) {\n        if ((typeof name !== \"symbol\" && typeof name !== \"string\") || name === \"\") {\n            throw new TypeError(\"You must supply a name when creating a logger.\");\n        }\n\n        var logger = _loggersByName[name];\n        if (!logger) {\n            logger = _loggersByName[name] = new Logger(\n                name,\n                defaultLogger.methodFactory\n            );\n        }\n        return logger;\n    };\n\n    // Grab the current global log variable in case of overwrite\n    var _log = (typeof window !== undefinedType) ? window.log : undefined;\n    defaultLogger.noConflict = function() {\n        if (typeof window !== undefinedType &&\n               window.log === defaultLogger) {\n            window.log = _log;\n        }\n\n        return defaultLogger;\n    };\n\n    defaultLogger.getLoggers = function getLoggers() {\n        return _loggersByName;\n    };\n\n    // ES6 default export, for compatibility\n    defaultLogger['default'] = defaultLogger;\n\n    return defaultLogger;\n}));\n"], "mappings": ";;;;;AAAA;AAAA;AAMA,KAAC,SAAU,MAAM,YAAY;AACzB;AACA,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC5C,eAAO,UAAU;AAAA,MACrB,WAAW,OAAO,WAAW,YAAY,OAAO,SAAS;AACrD,eAAO,UAAU,WAAW;AAAA,MAChC,OAAO;AACH,aAAK,MAAM,WAAW;AAAA,MAC1B;AAAA,IACJ,GAAE,SAAM,WAAY;AAChB;AAGA,UAAI,OAAO,WAAW;AAAA,MAAC;AACvB,UAAI,gBAAgB;AACpB,UAAI,OAAQ,OAAO,WAAW,iBAAmB,OAAO,OAAO,cAAc,iBACzE,kBAAkB,KAAK,OAAO,UAAU,SAAS;AAGrD,UAAI,aAAa;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAEA,UAAI,iBAAiB,CAAC;AACtB,UAAI,gBAAgB;AAGpB,eAAS,WAAW,KAAK,YAAY;AACjC,YAAI,SAAS,IAAI,UAAU;AAC3B,YAAI,OAAO,OAAO,SAAS,YAAY;AACnC,iBAAO,OAAO,KAAK,GAAG;AAAA,QAC1B,OAAO;AACH,cAAI;AACA,mBAAO,SAAS,UAAU,KAAK,KAAK,QAAQ,GAAG;AAAA,UACnD,SAAS,GAAG;AAER,mBAAO,WAAW;AACd,qBAAO,SAAS,UAAU,MAAM,MAAM,QAAQ,CAAC,KAAK,SAAS,CAAC;AAAA,YAClE;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAGA,eAAS,aAAa;AAClB,YAAI,QAAQ,KAAK;AACb,cAAI,QAAQ,IAAI,OAAO;AACnB,oBAAQ,IAAI,MAAM,SAAS,SAAS;AAAA,UACxC,OAAO;AAEH,qBAAS,UAAU,MAAM,MAAM,QAAQ,KAAK,CAAC,SAAS,SAAS,CAAC;AAAA,UACpE;AAAA,QACJ;AACA,YAAI,QAAQ,MAAO,SAAQ,MAAM;AAAA,MACrC;AAIA,eAAS,WAAW,YAAY;AAC5B,YAAI,eAAe,SAAS;AACxB,uBAAa;AAAA,QACjB;AAEA,YAAI,OAAO,YAAY,eAAe;AAClC,iBAAO;AAAA,QACX,WAAW,eAAe,WAAW,MAAM;AACvC,iBAAO;AAAA,QACX,WAAW,QAAQ,UAAU,MAAM,QAAW;AAC1C,iBAAO,WAAW,SAAS,UAAU;AAAA,QACzC,WAAW,QAAQ,QAAQ,QAAW;AAClC,iBAAO,WAAW,SAAS,KAAK;AAAA,QACpC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ;AAIA,eAAS,wBAAwB;AAE7B,YAAI,QAAQ,KAAK,SAAS;AAG1B,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,cAAI,aAAa,WAAW,CAAC;AAC7B,eAAK,UAAU,IAAK,IAAI,QACpB,OACA,KAAK,cAAc,YAAY,OAAO,KAAK,IAAI;AAAA,QACvD;AAGA,aAAK,MAAM,KAAK;AAGhB,YAAI,OAAO,YAAY,iBAAiB,QAAQ,KAAK,OAAO,QAAQ;AAChE,iBAAO;AAAA,QACX;AAAA,MACJ;AAIA,eAAS,gCAAgC,YAAY;AACjD,eAAO,WAAY;AACf,cAAI,OAAO,YAAY,eAAe;AAClC,kCAAsB,KAAK,IAAI;AAC/B,iBAAK,UAAU,EAAE,MAAM,MAAM,SAAS;AAAA,UAC1C;AAAA,QACJ;AAAA,MACJ;AAIA,eAAS,qBAAqB,YAAY,QAAQ,aAAa;AAE3D,eAAO,WAAW,UAAU,KACrB,gCAAgC,MAAM,MAAM,SAAS;AAAA,MAChE;AAEA,eAAS,OAAO,MAAM,SAAS;AAE7B,YAAI,OAAO;AASX,YAAI;AAMJ,YAAI;AAMJ,YAAI;AAEJ,YAAI,aAAa;AACjB,YAAI,OAAO,SAAS,UAAU;AAC5B,wBAAc,MAAM;AAAA,QACtB,WAAW,OAAO,SAAS,UAAU;AACnC,uBAAa;AAAA,QACf;AAEA,iBAAS,uBAAuB,UAAU;AACtC,cAAI,aAAa,WAAW,QAAQ,KAAK,UAAU,YAAY;AAE/D,cAAI,OAAO,WAAW,iBAAiB,CAAC,WAAY;AAGpD,cAAI;AACA,mBAAO,aAAa,UAAU,IAAI;AAClC;AAAA,UACJ,SAAS,QAAQ;AAAA,UAAC;AAGlB,cAAI;AACA,mBAAO,SAAS,SACd,mBAAmB,UAAU,IAAI,MAAM,YAAY;AAAA,UACzD,SAAS,QAAQ;AAAA,UAAC;AAAA,QACtB;AAEA,iBAAS,oBAAoB;AACzB,cAAI;AAEJ,cAAI,OAAO,WAAW,iBAAiB,CAAC,WAAY;AAEpD,cAAI;AACA,0BAAc,OAAO,aAAa,UAAU;AAAA,UAChD,SAAS,QAAQ;AAAA,UAAC;AAGlB,cAAI,OAAO,gBAAgB,eAAe;AACtC,gBAAI;AACA,kBAAI,SAAS,OAAO,SAAS;AAC7B,kBAAI,aAAa,mBAAmB,UAAU;AAC9C,kBAAI,WAAW,OAAO,QAAQ,aAAa,GAAG;AAC9C,kBAAI,aAAa,IAAI;AACjB,8BAAc,WAAW;AAAA,kBACrB,OAAO,MAAM,WAAW,WAAW,SAAS,CAAC;AAAA,gBACjD,EAAE,CAAC;AAAA,cACP;AAAA,YACJ,SAAS,QAAQ;AAAA,YAAC;AAAA,UACtB;AAGA,cAAI,KAAK,OAAO,WAAW,MAAM,QAAW;AACxC,0BAAc;AAAA,UAClB;AAEA,iBAAO;AAAA,QACX;AAEA,iBAAS,sBAAsB;AAC3B,cAAI,OAAO,WAAW,iBAAiB,CAAC,WAAY;AAGpD,cAAI;AACA,mBAAO,aAAa,WAAW,UAAU;AAAA,UAC7C,SAAS,QAAQ;AAAA,UAAC;AAGlB,cAAI;AACA,mBAAO,SAAS,SACd,mBAAmB,UAAU,IAAI;AAAA,UACvC,SAAS,QAAQ;AAAA,UAAC;AAAA,QACtB;AAEA,iBAAS,eAAe,OAAO;AAC3B,cAAI,QAAQ;AACZ,cAAI,OAAO,UAAU,YAAY,KAAK,OAAO,MAAM,YAAY,CAAC,MAAM,QAAW;AAC7E,oBAAQ,KAAK,OAAO,MAAM,YAAY,CAAC;AAAA,UAC3C;AACA,cAAI,OAAO,UAAU,YAAY,SAAS,KAAK,SAAS,KAAK,OAAO,QAAQ;AACxE,mBAAO;AAAA,UACX,OAAO;AACH,kBAAM,IAAI,UAAU,+CAA+C,KAAK;AAAA,UAC5E;AAAA,QACJ;AAQA,aAAK,OAAO;AAEZ,aAAK,SAAS;AAAA,UAAE,SAAS;AAAA,UAAG,SAAS;AAAA,UAAG,QAAQ;AAAA,UAAG,QAAQ;AAAA,UACvD,SAAS;AAAA,UAAG,UAAU;AAAA,QAAC;AAE3B,aAAK,gBAAgB,WAAW;AAEhC,aAAK,WAAW,WAAY;AACxB,cAAI,aAAa,MAAM;AACrB,mBAAO;AAAA,UACT,WAAW,gBAAgB,MAAM;AAC/B,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACJ;AAEA,aAAK,WAAW,SAAU,OAAO,SAAS;AACtC,sBAAY,eAAe,KAAK;AAChC,cAAI,YAAY,OAAO;AACnB,mCAAuB,SAAS;AAAA,UACpC;AAGA,iBAAO,sBAAsB,KAAK,IAAI;AAAA,QAC1C;AAEA,aAAK,kBAAkB,SAAU,OAAO;AACpC,yBAAe,eAAe,KAAK;AACnC,cAAI,CAAC,kBAAkB,GAAG;AACtB,iBAAK,SAAS,OAAO,KAAK;AAAA,UAC9B;AAAA,QACJ;AAEA,aAAK,aAAa,WAAY;AAC1B,sBAAY;AACZ,8BAAoB;AACpB,gCAAsB,KAAK,IAAI;AAAA,QACnC;AAEA,aAAK,YAAY,SAAS,SAAS;AAC/B,eAAK,SAAS,KAAK,OAAO,OAAO,OAAO;AAAA,QAC5C;AAEA,aAAK,aAAa,SAAS,SAAS;AAChC,eAAK,SAAS,KAAK,OAAO,QAAQ,OAAO;AAAA,QAC7C;AAEA,aAAK,UAAU,WAAY;AACvB,cAAI,kBAAkB,MAAM;AACxB,6BAAiB,eAAe,cAAc,SAAS,CAAC;AAAA,UAC5D;AACA,gCAAsB,KAAK,IAAI;AAE/B,cAAI,kBAAkB,MAAM;AACxB,qBAAS,aAAa,gBAAgB;AACpC,6BAAe,SAAS,EAAE,QAAQ;AAAA,YACpC;AAAA,UACJ;AAAA,QACJ;AAGA,yBAAiB;AAAA,UACb,gBAAgB,cAAc,SAAS,IAAI;AAAA,QAC/C;AACA,YAAI,eAAe,kBAAkB;AACrC,YAAI,gBAAgB,MAAM;AACtB,sBAAY,eAAe,YAAY;AAAA,QAC3C;AACA,8BAAsB,KAAK,IAAI;AAAA,MACjC;AAQA,sBAAgB,IAAI,OAAO;AAE3B,oBAAc,YAAY,SAAS,UAAU,MAAM;AAC/C,YAAK,OAAO,SAAS,YAAY,OAAO,SAAS,YAAa,SAAS,IAAI;AACvE,gBAAM,IAAI,UAAU,gDAAgD;AAAA,QACxE;AAEA,YAAI,SAAS,eAAe,IAAI;AAChC,YAAI,CAAC,QAAQ;AACT,mBAAS,eAAe,IAAI,IAAI,IAAI;AAAA,YAChC;AAAA,YACA,cAAc;AAAA,UAClB;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAGA,UAAI,OAAQ,OAAO,WAAW,gBAAiB,OAAO,MAAM;AAC5D,oBAAc,aAAa,WAAW;AAClC,YAAI,OAAO,WAAW,iBACf,OAAO,QAAQ,eAAe;AACjC,iBAAO,MAAM;AAAA,QACjB;AAEA,eAAO;AAAA,MACX;AAEA,oBAAc,aAAa,SAAS,aAAa;AAC7C,eAAO;AAAA,MACX;AAGA,oBAAc,SAAS,IAAI;AAE3B,aAAO;AAAA,IACX,CAAC;AAAA;AAAA;", "names": []}