import{_ as t,c as s,o as e,ab as p}from"./chunks/framework.oPHriSgN.js";const r="/assets/image.DQnNEvMR.png",o="/assets/image-1.csC8abzi.png",i="/assets/image-10.Dscl3Jn2.png",n="/assets/image-11.9RizTE3W.png",l="/assets/image-6.CRyYipzQ.png",m="/assets/image-7.X9AibjFM.png",h="/assets/image-8.B8D11nUf.png",c="/assets/image-9.CxC0oqs6.png",g="/assets/image-2.CzJX7MUo.png",_="/assets/image-12.JpZYRkFj.png",d="/assets/image-13.hkeW57Hh.png",x="/assets/image-14.Cjg0nF6B.png",b="/assets/image-15.D7QG16D3.png",f="/assets/image-16.DbNZawzG.png",u="/assets/image-17.Dh4W23V6.png",q="/assets/image-18.BgE8Ltpn.png",k="/assets/image-19.DmTIaI5Z.png",P="/assets/image-3.CI9YlX6f.png",D="/assets/image-4.B7EBioCx.png",C="/assets/image-20.C7v9WgCz.png",T="/assets/image-21.Cb30YAc8.png",y="/assets/image-5.CC8m1yDn.png",N=JSON.parse('{"title":"案例展示","description":"","frontmatter":{"layout":"doc","title":"案例展示"},"headers":[],"relativePath":"showcase/index.md","filePath":"showcase/index.md"}'),B={name:"showcase/index.md"};function M(z,a,E,S,A,I){return e(),s("div",null,a[0]||(a[0]=[p("",73)]))}const R=t(B,[["render",M]]);export{N as __pageData,R as default};
