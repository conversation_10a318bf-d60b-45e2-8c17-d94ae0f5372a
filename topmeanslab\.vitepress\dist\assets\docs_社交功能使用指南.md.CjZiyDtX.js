import{_ as i,c as a,o,ab as r}from"./chunks/framework.oPHriSgN.js";const g=JSON.parse('{"title":"旅游规划网站社交功能使用指南","description":"","frontmatter":{},"headers":[],"relativePath":"docs/社交功能使用指南.md","filePath":"docs/社交功能使用指南.md"}'),t={name:"docs/社交功能使用指南.md"};function e(n,l,s,h,d,u){return o(),a("div",null,l[0]||(l[0]=[r(`<h1 id="旅游规划网站社交功能使用指南" tabindex="-1">旅游规划网站社交功能使用指南 <a class="header-anchor" href="#旅游规划网站社交功能使用指南" aria-label="Permalink to &quot;旅游规划网站社交功能使用指南&quot;">​</a></h1><h2 id="功能概述" tabindex="-1">功能概述 <a class="header-anchor" href="#功能概述" aria-label="Permalink to &quot;功能概述&quot;">​</a></h2><p>本次更新为旅游规划网站增加了完整的社交功能，包括：</p><ol><li><strong>游记创建与管理</strong>：用户可以创建、编辑、删除自己的游记</li><li><strong>隐私控制</strong>：支持设置游记为公开或私密状态</li><li><strong>游记推荐</strong>：在规划完成后，自动推荐相同目的地的其他用户游记</li><li><strong>互动功能</strong>：支持点赞、评论、浏览统计等社交互动</li></ol><h2 id="功能特点" tabindex="-1">功能特点 <a class="header-anchor" href="#功能特点" aria-label="Permalink to &quot;功能特点&quot;">​</a></h2><h3 id="_1-游记编辑器" tabindex="-1">1. 游记编辑器 <a class="header-anchor" href="#_1-游记编辑器" aria-label="Permalink to &quot;1. 游记编辑器&quot;">​</a></h3><ul><li><strong>富文本编辑</strong>：支持Markdown格式的内容编辑</li><li><strong>图片上传</strong>：可上传封面图片和内容图片</li><li><strong>标签管理</strong>：为游记添加标签分类</li><li><strong>旅行信息</strong>：记录出发地、目的地、旅行天数、方式等信息</li><li><strong>隐私设置</strong>：可选择公开或私密状态</li></ul><h3 id="_2-游记展示" tabindex="-1">2. 游记展示 <a class="header-anchor" href="#_2-游记展示" aria-label="Permalink to &quot;2. 游记展示&quot;">​</a></h3><ul><li><strong>卡片式布局</strong>：美观的卡片式游记展示</li><li><strong>作者信息</strong>：显示游记作者头像、昵称和发布时间</li><li><strong>互动统计</strong>：显示点赞数、评论数、浏览数</li><li><strong>标签展示</strong>：展示游记标签信息</li></ul><h3 id="_3-游记推荐" tabindex="-1">3. 游记推荐 <a class="header-anchor" href="#_3-游记推荐" aria-label="Permalink to &quot;3. 游记推荐&quot;">​</a></h3><ul><li><strong>智能推荐</strong>：基于目的地自动推荐相关游记</li><li><strong>精美展示</strong>：渐变背景的推荐卡片展示</li><li><strong>快速预览</strong>：支持点击查看游记详情</li></ul><h3 id="_4-用户中心集成" tabindex="-1">4. 用户中心集成 <a class="header-anchor" href="#_4-用户中心集成" aria-label="Permalink to &quot;4. 用户中心集成&quot;">​</a></h3><ul><li><strong>我的游记</strong>：在用户中心新增游记管理选项卡</li><li><strong>筛选排序</strong>：支持按公开状态筛选、按时间/点赞数排序</li><li><strong>批量管理</strong>：支持批量操作游记</li></ul><h2 id="安装配置" tabindex="-1">安装配置 <a class="header-anchor" href="#安装配置" aria-label="Permalink to &quot;安装配置&quot;">​</a></h2><h3 id="_1-数据库设置" tabindex="-1">1. 数据库设置 <a class="header-anchor" href="#_1-数据库设置" aria-label="Permalink to &quot;1. 数据库设置&quot;">​</a></h3><p>执行以下SQL脚本创建相关数据表：</p><div class="language-sql vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">sql</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">-- 执行文件：topmeans_srv/db/social_features.sql</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">-- 这个文件包含了所有社交功能需要的数据库表结构</span></span></code></pre></div><h3 id="_2-后端api" tabindex="-1">2. 后端API <a class="header-anchor" href="#_2-后端api" aria-label="Permalink to &quot;2. 后端API&quot;">​</a></h3><p>社交功能的后端API已经集成到现有的后端服务中：</p><ul><li><strong>游记路由</strong>：<code>topmeans_srv/src/routes/journalRoutes.js</code></li><li><strong>游记服务</strong>：<code>topmeans_srv/src/services/journalService.js</code></li><li><strong>API接口</strong>：所有接口都以 <code>/api/journal</code> 为前缀</li></ul><p>主要API接口：</p><ul><li><code>POST /api/journal</code> - 创建游记</li><li><code>GET /api/journal/my</code> - 获取用户游记</li><li><code>GET /api/journal/public</code> - 获取公开游记</li><li><code>GET /api/journal/recommendations/:destination</code> - 获取推荐游记</li><li><code>PUT /api/journal/:id</code> - 更新游记</li><li><code>DELETE /api/journal/:id</code> - 删除游记</li><li><code>PATCH /api/journal/:id/toggle-public</code> - 切换公开状态</li><li><code>POST /api/journal/:id/like</code> - 点赞游记</li></ul><h3 id="_3-前端组件" tabindex="-1">3. 前端组件 <a class="header-anchor" href="#_3-前端组件" aria-label="Permalink to &quot;3. 前端组件&quot;">​</a></h3><p>新增的前端组件：</p><ul><li><code>JournalEditor.vue</code> - 游记编辑器</li><li><code>JournalCard.vue</code> - 游记卡片组件</li><li><code>JournalList.vue</code> - 游记列表组件</li><li><code>JournalRecommendation.vue</code> - 游记推荐组件</li></ul><h2 id="使用说明" tabindex="-1">使用说明 <a class="header-anchor" href="#使用说明" aria-label="Permalink to &quot;使用说明&quot;">​</a></h2><h3 id="_1-创建游记" tabindex="-1">1. 创建游记 <a class="header-anchor" href="#_1-创建游记" aria-label="Permalink to &quot;1. 创建游记&quot;">​</a></h3><ol><li>登录后，在用户中心的&quot;我的游记&quot;选项卡中点击&quot;写游记&quot;</li><li>填写游记标题、旅行信息、封面图片</li><li>使用编辑器编写游记内容，支持Markdown语法</li><li>添加标签分类</li><li>选择隐私设置（公开/私密）</li><li>点击&quot;发布&quot;完成创建</li></ol><h3 id="_2-管理游记" tabindex="-1">2. 管理游记 <a class="header-anchor" href="#_2-管理游记" aria-label="Permalink to &quot;2. 管理游记&quot;">​</a></h3><p>在用户中心的&quot;我的游记&quot;选项卡中：</p><ul><li><strong>查看游记</strong>：点击游记卡片查看详情</li><li><strong>编辑游记</strong>：点击游记卡片上的&quot;编辑&quot;按钮</li><li><strong>删除游记</strong>：点击游记卡片上的&quot;删除&quot;按钮</li><li><strong>切换公开状态</strong>：点击&quot;设为公开/私密&quot;按钮</li><li><strong>筛选游记</strong>：使用筛选器按公开状态筛选</li><li><strong>排序游记</strong>：支持按时间、点赞数、浏览数排序</li></ul><h3 id="_3-游记推荐-1" tabindex="-1">3. 游记推荐 <a class="header-anchor" href="#_3-游记推荐-1" aria-label="Permalink to &quot;3. 游记推荐&quot;">​</a></h3><p>在完成旅游规划后，系统会自动显示游记推荐区域：</p><ul><li>推荐相同目的地的其他用户公开游记</li><li>按浏览量、点赞数、发布时间排序</li><li>点击游记卡片查看详细内容</li><li>支持点赞和评论功能</li></ul><h3 id="_4-互动功能" tabindex="-1">4. 互动功能 <a class="header-anchor" href="#_4-互动功能" aria-label="Permalink to &quot;4. 互动功能&quot;">​</a></h3><ul><li><strong>点赞</strong>：点击❤️按钮为游记点赞</li><li><strong>评论</strong>：点击评论按钮查看和发表评论</li><li><strong>浏览统计</strong>：自动统计游记浏览次数</li></ul><h2 id="技术特点" tabindex="-1">技术特点 <a class="header-anchor" href="#技术特点" aria-label="Permalink to &quot;技术特点&quot;">​</a></h2><h3 id="_1-响应式设计" tabindex="-1">1. 响应式设计 <a class="header-anchor" href="#_1-响应式设计" aria-label="Permalink to &quot;1. 响应式设计&quot;">​</a></h3><ul><li>适配桌面、平板、手机等不同设备</li><li>流畅的用户体验</li></ul><h3 id="_2-性能优化" tabindex="-1">2. 性能优化 <a class="header-anchor" href="#_2-性能优化" aria-label="Permalink to &quot;2. 性能优化&quot;">​</a></h3><ul><li>分页加载减少服务器压力</li><li>图片懒加载优化页面性能</li><li>组件级别的状态管理</li></ul><h3 id="_3-数据安全" tabindex="-1">3. 数据安全 <a class="header-anchor" href="#_3-数据安全" aria-label="Permalink to &quot;3. 数据安全&quot;">​</a></h3><ul><li>JWT身份验证</li><li>权限控制确保数据安全</li><li>输入验证防止XSS攻击</li></ul><h3 id="_4-扩展性" tabindex="-1">4. 扩展性 <a class="header-anchor" href="#_4-扩展性" aria-label="Permalink to &quot;4. 扩展性&quot;">​</a></h3><ul><li>模块化设计便于功能扩展</li><li>标准化API接口</li><li>清晰的代码结构</li></ul><h2 id="注意事项" tabindex="-1">注意事项 <a class="header-anchor" href="#注意事项" aria-label="Permalink to &quot;注意事项&quot;">​</a></h2><ol><li><strong>图片上传</strong>：单张图片大小限制为5MB</li><li><strong>内容审核</strong>：公开游记需要遵守平台规范</li><li><strong>隐私保护</strong>：私密游记只有本人可见</li><li><strong>数据备份</strong>：建议定期备份重要游记内容</li></ol><h2 id="故障排除" tabindex="-1">故障排除 <a class="header-anchor" href="#故障排除" aria-label="Permalink to &quot;故障排除&quot;">​</a></h2><h3 id="常见问题" tabindex="-1">常见问题 <a class="header-anchor" href="#常见问题" aria-label="Permalink to &quot;常见问题&quot;">​</a></h3><ol><li><p><strong>游记推荐不显示</strong></p><ul><li>确保用户已登录</li><li>检查目的地信息是否正确</li><li>确认有其他用户发布了相同目的地的公开游记</li></ul></li><li><p><strong>图片上传失败</strong></p><ul><li>检查图片格式是否支持（jpg、png、gif、webp）</li><li>确认图片大小不超过5MB</li><li>检查网络连接是否正常</li></ul></li><li><p><strong>游记无法保存</strong></p><ul><li>确认所有必填字段已填写</li><li>检查网络连接</li><li>确认登录状态有效</li></ul></li></ol><h3 id="技术支持" tabindex="-1">技术支持 <a class="header-anchor" href="#技术支持" aria-label="Permalink to &quot;技术支持&quot;">​</a></h3><p>如果遇到其他问题，请检查：</p><ol><li>浏览器控制台是否有错误信息</li><li>网络请求是否正常</li><li>后端服务是否正常运行</li><li>数据库连接是否正常</li></ol><h2 id="未来规划" tabindex="-1">未来规划 <a class="header-anchor" href="#未来规划" aria-label="Permalink to &quot;未来规划&quot;">​</a></h2><ol><li><p><strong>增强互动功能</strong>：</p><ul><li>评论系统完善</li><li>用户关注功能</li><li>游记收藏功能</li></ul></li><li><p><strong>内容推荐优化</strong>：</p><ul><li>基于用户行为的个性化推荐</li><li>热门游记推荐</li><li>地区热门推荐</li></ul></li><li><p><strong>社交分享</strong>：</p><ul><li>分享到社交媒体</li><li>生成游记二维码</li><li>离线阅读功能</li></ul></li></ol><hr><p><em>本指南会根据功能更新持续完善，如有疑问请联系技术支持。</em></p>`,57)]))}const p=i(t,[["render",e]]);export{g as __pageData,p as default};
