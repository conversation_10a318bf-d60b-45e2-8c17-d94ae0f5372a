import{h as Kt,i as $t,E as k}from"./theme.DE6uTiF9.js";import{_ as Jt,p as L,x as Ot,C as z,c as F,o as V,G as M,w as S,j as C,t as j,a as Y,e as it,k as Bt}from"./framework.oPHriSgN.js";var Q={},jt=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},At={},N={};let pt;const Yt=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];N.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return t*4+17};N.getSymbolTotalCodewords=function(t){return Yt[t]};N.getBCHDigit=function(e){let t=0;for(;e!==0;)t++,e>>>=1;return t};N.setToSJISFunction=function(t){if(typeof t!="function")throw new Error('"toSJISFunc" is not a valid function.');pt=t};N.isKanjiModeEnabled=function(){return typeof pt<"u"};N.toSJIS=function(t){return pt(t)};var et={};(function(e){e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2};function t(s){if(typeof s!="string")throw new Error("Param is not a string");switch(s.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+s)}}e.isValid=function(o){return o&&typeof o.bit<"u"&&o.bit>=0&&o.bit<4},e.from=function(o,n){if(e.isValid(o))return o;try{return t(o)}catch{return n}}})(et);function bt(){this.buffer=[],this.length=0}bt.prototype={get:function(e){const t=Math.floor(e/8);return(this.buffer[t]>>>7-e%8&1)===1},put:function(e,t){for(let s=0;s<t;s++)this.putBit((e>>>t-s-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(e){const t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}};var qt=bt;function W(e){if(!e||e<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}W.prototype.set=function(e,t,s,o){const n=e*this.size+t;this.data[n]=s,o&&(this.reservedBit[n]=!0)};W.prototype.get=function(e,t){return this.data[e*this.size+t]};W.prototype.xor=function(e,t,s){this.data[e*this.size+t]^=s};W.prototype.isReserved=function(e,t){return this.reservedBit[e*this.size+t]};var Gt=W,Nt={};(function(e){const t=N.getSymbolSize;e.getRowColCoords=function(o){if(o===1)return[];const n=Math.floor(o/7)+2,r=t(o),i=r===145?26:Math.ceil((r-13)/(2*n-2))*2,l=[r-7];for(let a=1;a<n-1;a++)l[a]=l[a-1]-i;return l.push(6),l.reverse()},e.getPositions=function(o){const n=[],r=e.getRowColCoords(o),i=r.length;for(let l=0;l<i;l++)for(let a=0;a<i;a++)l===0&&a===0||l===0&&a===i-1||l===i-1&&a===0||n.push([r[l],r[a]]);return n}})(Nt);var Mt={};const Qt=N.getSymbolSize,It=7;Mt.getPositions=function(t){const s=Qt(t);return[[0,0],[s-It,0],[0,s-It]]};var St={};(function(e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const t={N1:3,N2:3,N3:40,N4:10};e.isValid=function(n){return n!=null&&n!==""&&!isNaN(n)&&n>=0&&n<=7},e.from=function(n){return e.isValid(n)?parseInt(n,10):void 0},e.getPenaltyN1=function(n){const r=n.size;let i=0,l=0,a=0,c=null,d=null;for(let E=0;E<r;E++){l=a=0,c=d=null;for(let m=0;m<r;m++){let f=n.get(E,m);f===c?l++:(l>=5&&(i+=t.N1+(l-5)),c=f,l=1),f=n.get(m,E),f===d?a++:(a>=5&&(i+=t.N1+(a-5)),d=f,a=1)}l>=5&&(i+=t.N1+(l-5)),a>=5&&(i+=t.N1+(a-5))}return i},e.getPenaltyN2=function(n){const r=n.size;let i=0;for(let l=0;l<r-1;l++)for(let a=0;a<r-1;a++){const c=n.get(l,a)+n.get(l,a+1)+n.get(l+1,a)+n.get(l+1,a+1);(c===4||c===0)&&i++}return i*t.N2},e.getPenaltyN3=function(n){const r=n.size;let i=0,l=0,a=0;for(let c=0;c<r;c++){l=a=0;for(let d=0;d<r;d++)l=l<<1&2047|n.get(c,d),d>=10&&(l===1488||l===93)&&i++,a=a<<1&2047|n.get(d,c),d>=10&&(a===1488||a===93)&&i++}return i*t.N3},e.getPenaltyN4=function(n){let r=0;const i=n.data.length;for(let a=0;a<i;a++)r+=n.data[a];return Math.abs(Math.ceil(r*100/i/5)-10)*t.N4};function s(o,n,r){switch(o){case e.Patterns.PATTERN000:return(n+r)%2===0;case e.Patterns.PATTERN001:return n%2===0;case e.Patterns.PATTERN010:return r%3===0;case e.Patterns.PATTERN011:return(n+r)%3===0;case e.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(r/3))%2===0;case e.Patterns.PATTERN101:return n*r%2+n*r%3===0;case e.Patterns.PATTERN110:return(n*r%2+n*r%3)%2===0;case e.Patterns.PATTERN111:return(n*r%3+(n+r)%2)%2===0;default:throw new Error("bad maskPattern:"+o)}}e.applyMask=function(n,r){const i=r.size;for(let l=0;l<i;l++)for(let a=0;a<i;a++)r.isReserved(a,l)||r.xor(a,l,s(n,a,l))},e.getBestMask=function(n,r){const i=Object.keys(e.Patterns).length;let l=0,a=1/0;for(let c=0;c<i;c++){r(c),e.applyMask(c,n);const d=e.getPenaltyN1(n)+e.getPenaltyN2(n)+e.getPenaltyN3(n)+e.getPenaltyN4(n);e.applyMask(c,n),d<a&&(a=d,l=c)}return l}})(St);var nt={};const U=et,Z=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],X=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];nt.getBlocksCount=function(t,s){switch(s){case U.L:return Z[(t-1)*4+0];case U.M:return Z[(t-1)*4+1];case U.Q:return Z[(t-1)*4+2];case U.H:return Z[(t-1)*4+3];default:return}};nt.getTotalCodewordsCount=function(t,s){switch(s){case U.L:return X[(t-1)*4+0];case U.M:return X[(t-1)*4+1];case U.Q:return X[(t-1)*4+2];case U.H:return X[(t-1)*4+3];default:return}};var _t={},ot={};const q=new Uint8Array(512),x=new Uint8Array(256);(function(){let t=1;for(let s=0;s<255;s++)q[s]=t,x[t]=s,t<<=1,t&256&&(t^=285);for(let s=255;s<512;s++)q[s]=q[s-255]})();ot.log=function(t){if(t<1)throw new Error("log("+t+")");return x[t]};ot.exp=function(t){return q[t]};ot.mul=function(t,s){return t===0||s===0?0:q[x[t]+x[s]]};(function(e){const t=ot;e.mul=function(o,n){const r=new Uint8Array(o.length+n.length-1);for(let i=0;i<o.length;i++)for(let l=0;l<n.length;l++)r[i+l]^=t.mul(o[i],n[l]);return r},e.mod=function(o,n){let r=new Uint8Array(o);for(;r.length-n.length>=0;){const i=r[0];for(let a=0;a<n.length;a++)r[a]^=t.mul(n[a],i);let l=0;for(;l<r.length&&r[l]===0;)l++;r=r.slice(l)}return r},e.generateECPolynomial=function(o){let n=new Uint8Array([1]);for(let r=0;r<o;r++)n=e.mul(n,new Uint8Array([1,t.exp(r)]));return n}})(_t);const Pt=_t;function yt(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}yt.prototype.initialize=function(t){this.degree=t,this.genPoly=Pt.generateECPolynomial(this.degree)};yt.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const s=new Uint8Array(t.length+this.degree);s.set(t);const o=Pt.mod(s,this.genPoly),n=this.degree-o.length;if(n>0){const r=new Uint8Array(this.degree);return r.set(o,n),r}return o};var Wt=yt,Rt={},D={},wt={};wt.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40};var P={};const Lt="[0-9]+",Zt="[A-Z $%*+\\-./:]+";let G="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";G=G.replace(/u/g,"\\u");const Xt="(?:(?![A-Z0-9 $%*+\\-./:]|"+G+`)(?:.|[\r
]))+`;P.KANJI=new RegExp(G,"g");P.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g");P.BYTE=new RegExp(Xt,"g");P.NUMERIC=new RegExp(Lt,"g");P.ALPHANUMERIC=new RegExp(Zt,"g");const xt=new RegExp("^"+G+"$"),te=new RegExp("^"+Lt+"$"),ee=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");P.testKanji=function(t){return xt.test(t)};P.testNumeric=function(t){return te.test(t)};P.testAlphanumeric=function(t){return ee.test(t)};(function(e){const t=wt,s=P;e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(r,i){if(!r.ccBits)throw new Error("Invalid mode: "+r);if(!t.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?r.ccBits[0]:i<27?r.ccBits[1]:r.ccBits[2]},e.getBestModeForData=function(r){return s.testNumeric(r)?e.NUMERIC:s.testAlphanumeric(r)?e.ALPHANUMERIC:s.testKanji(r)?e.KANJI:e.BYTE},e.toString=function(r){if(r&&r.id)return r.id;throw new Error("Invalid mode")},e.isValid=function(r){return r&&r.bit&&r.ccBits};function o(n){if(typeof n!="string")throw new Error("Param is not a string");switch(n.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+n)}}e.from=function(r,i){if(e.isValid(r))return r;try{return o(r)}catch{return i}}})(D);(function(e){const t=N,s=nt,o=et,n=D,r=wt,i=7973,l=t.getBCHDigit(i);function a(m,f,y){for(let w=1;w<=40;w++)if(f<=e.getCapacity(w,y,m))return w}function c(m,f){return n.getCharCountIndicator(m,f)+4}function d(m,f){let y=0;return m.forEach(function(w){const v=c(w.mode,f);y+=v+w.getBitsLength()}),y}function E(m,f){for(let y=1;y<=40;y++)if(d(m,y)<=e.getCapacity(y,f,n.MIXED))return y}e.from=function(f,y){return r.isValid(f)?parseInt(f,10):y},e.getCapacity=function(f,y,w){if(!r.isValid(f))throw new Error("Invalid QR Code version");typeof w>"u"&&(w=n.BYTE);const v=t.getSymbolTotalCodewords(f),p=s.getTotalCodewordsCount(f,y),h=(v-p)*8;if(w===n.MIXED)return h;const u=h-c(w,f);switch(w){case n.NUMERIC:return Math.floor(u/10*3);case n.ALPHANUMERIC:return Math.floor(u/11*2);case n.KANJI:return Math.floor(u/13);case n.BYTE:default:return Math.floor(u/8)}},e.getBestVersionForData=function(f,y){let w;const v=o.from(y,o.M);if(Array.isArray(f)){if(f.length>1)return E(f,v);if(f.length===0)return 1;w=f[0]}else w=f;return a(w.mode,w.getLength(),v)},e.getEncodedBits=function(f){if(!r.isValid(f)||f<7)throw new Error("Invalid QR Code version");let y=f<<12;for(;t.getBCHDigit(y)-l>=0;)y^=i<<t.getBCHDigit(y)-l;return f<<12|y}})(Rt);var kt={};const ft=N,Ut=1335,ne=21522,Tt=ft.getBCHDigit(Ut);kt.getEncodedBits=function(t,s){const o=t.bit<<3|s;let n=o<<10;for(;ft.getBCHDigit(n)-Tt>=0;)n^=Ut<<ft.getBCHDigit(n)-Tt;return(o<<10|n)^ne};var Dt={};const oe=D;function H(e){this.mode=oe.NUMERIC,this.data=e.toString()}H.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)};H.prototype.getLength=function(){return this.data.length};H.prototype.getBitsLength=function(){return H.getBitsLength(this.data.length)};H.prototype.write=function(t){let s,o,n;for(s=0;s+3<=this.data.length;s+=3)o=this.data.substr(s,3),n=parseInt(o,10),t.put(n,10);const r=this.data.length-s;r>0&&(o=this.data.substr(s),n=parseInt(o,10),t.put(n,r*3+1))};var re=H;const se=D,at=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function K(e){this.mode=se.ALPHANUMERIC,this.data=e}K.getBitsLength=function(t){return 11*Math.floor(t/2)+6*(t%2)};K.prototype.getLength=function(){return this.data.length};K.prototype.getBitsLength=function(){return K.getBitsLength(this.data.length)};K.prototype.write=function(t){let s;for(s=0;s+2<=this.data.length;s+=2){let o=at.indexOf(this.data[s])*45;o+=at.indexOf(this.data[s+1]),t.put(o,11)}this.data.length%2&&t.put(at.indexOf(this.data[s]),6)};var ie=K;const ae=D;function $(e){this.mode=ae.BYTE,typeof e=="string"?this.data=new TextEncoder().encode(e):this.data=new Uint8Array(e)}$.getBitsLength=function(t){return t*8};$.prototype.getLength=function(){return this.data.length};$.prototype.getBitsLength=function(){return $.getBitsLength(this.data.length)};$.prototype.write=function(e){for(let t=0,s=this.data.length;t<s;t++)e.put(this.data[t],8)};var le=$;const ue=D,ce=N;function J(e){this.mode=ue.KANJI,this.data=e}J.getBitsLength=function(t){return t*13};J.prototype.getLength=function(){return this.data.length};J.prototype.getBitsLength=function(){return J.getBitsLength(this.data.length)};J.prototype.write=function(e){let t;for(t=0;t<this.data.length;t++){let s=ce.toSJIS(this.data[t]);if(s>=33088&&s<=40956)s-=33088;else if(s>=57408&&s<=60351)s-=49472;else throw new Error("Invalid SJIS character: "+this.data[t]+`
Make sure your charset is UTF-8`);s=(s>>>8&255)*192+(s&255),e.put(s,13)}};var de=J,Ft={exports:{}};(function(e){var t={single_source_shortest_paths:function(s,o,n){var r={},i={};i[o]=0;var l=t.PriorityQueue.make();l.push(o,0);for(var a,c,d,E,m,f,y,w,v;!l.empty();){a=l.pop(),c=a.value,E=a.cost,m=s[c]||{};for(d in m)m.hasOwnProperty(d)&&(f=m[d],y=E+f,w=i[d],v=typeof i[d]>"u",(v||w>y)&&(i[d]=y,l.push(d,y),r[d]=c))}if(typeof n<"u"&&typeof i[n]>"u"){var p=["Could not find a path from ",o," to ",n,"."].join("");throw new Error(p)}return r},extract_shortest_path_from_predecessor_list:function(s,o){for(var n=[],r=o;r;)n.push(r),s[r],r=s[r];return n.reverse(),n},find_path:function(s,o,n){var r=t.single_source_shortest_paths(s,o,n);return t.extract_shortest_path_from_predecessor_list(r,n)},PriorityQueue:{make:function(s){var o=t.PriorityQueue,n={},r;s=s||{};for(r in o)o.hasOwnProperty(r)&&(n[r]=o[r]);return n.queue=[],n.sorter=s.sorter||o.default_sorter,n},default_sorter:function(s,o){return s.cost-o.cost},push:function(s,o){var n={value:s,cost:o};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};e.exports=t})(Ft);var fe=Ft.exports;(function(e){const t=D,s=re,o=ie,n=le,r=de,i=P,l=N,a=fe;function c(p){return unescape(encodeURIComponent(p)).length}function d(p,h,u){const g=[];let B;for(;(B=p.exec(u))!==null;)g.push({data:B[0],index:B.index,mode:h,length:B[0].length});return g}function E(p){const h=d(i.NUMERIC,t.NUMERIC,p),u=d(i.ALPHANUMERIC,t.ALPHANUMERIC,p);let g,B;return l.isKanjiModeEnabled()?(g=d(i.BYTE,t.BYTE,p),B=d(i.KANJI,t.KANJI,p)):(g=d(i.BYTE_KANJI,t.BYTE,p),B=[]),h.concat(u,g,B).sort(function(T,b){return T.index-b.index}).map(function(T){return{data:T.data,mode:T.mode,length:T.length}})}function m(p,h){switch(h){case t.NUMERIC:return s.getBitsLength(p);case t.ALPHANUMERIC:return o.getBitsLength(p);case t.KANJI:return r.getBitsLength(p);case t.BYTE:return n.getBitsLength(p)}}function f(p){return p.reduce(function(h,u){const g=h.length-1>=0?h[h.length-1]:null;return g&&g.mode===u.mode?(h[h.length-1].data+=u.data,h):(h.push(u),h)},[])}function y(p){const h=[];for(let u=0;u<p.length;u++){const g=p[u];switch(g.mode){case t.NUMERIC:h.push([g,{data:g.data,mode:t.ALPHANUMERIC,length:g.length},{data:g.data,mode:t.BYTE,length:g.length}]);break;case t.ALPHANUMERIC:h.push([g,{data:g.data,mode:t.BYTE,length:g.length}]);break;case t.KANJI:h.push([g,{data:g.data,mode:t.BYTE,length:c(g.data)}]);break;case t.BYTE:h.push([{data:g.data,mode:t.BYTE,length:c(g.data)}])}}return h}function w(p,h){const u={},g={start:{}};let B=["start"];for(let I=0;I<p.length;I++){const T=p[I],b=[];for(let _=0;_<T.length;_++){const A=T[_],O=""+I+_;b.push(O),u[O]={node:A,lastCount:0},g[O]={};for(let st=0;st<B.length;st++){const R=B[st];u[R]&&u[R].node.mode===A.mode?(g[R][O]=m(u[R].lastCount+A.length,A.mode)-m(u[R].lastCount,A.mode),u[R].lastCount+=A.length):(u[R]&&(u[R].lastCount=A.length),g[R][O]=m(A.length,A.mode)+4+t.getCharCountIndicator(A.mode,h))}}B=b}for(let I=0;I<B.length;I++)g[B[I]].end=0;return{map:g,table:u}}function v(p,h){let u;const g=t.getBestModeForData(p);if(u=t.from(h,g),u!==t.BYTE&&u.bit<g.bit)throw new Error('"'+p+'" cannot be encoded with mode '+t.toString(u)+`.
 Suggested mode is: `+t.toString(g));switch(u===t.KANJI&&!l.isKanjiModeEnabled()&&(u=t.BYTE),u){case t.NUMERIC:return new s(p);case t.ALPHANUMERIC:return new o(p);case t.KANJI:return new r(p);case t.BYTE:return new n(p)}}e.fromArray=function(h){return h.reduce(function(u,g){return typeof g=="string"?u.push(v(g,null)):g.data&&u.push(v(g.data,g.mode)),u},[])},e.fromString=function(h,u){const g=E(h,l.isKanjiModeEnabled()),B=y(g),I=w(B,u),T=a.find_path(I.map,"start","end"),b=[];for(let _=1;_<T.length-1;_++)b.push(I.table[T[_]].node);return e.fromArray(f(b))},e.rawSplit=function(h){return e.fromArray(E(h,l.isKanjiModeEnabled()))}})(Dt);const rt=N,lt=et,ge=qt,he=Gt,me=Nt,pe=Mt,gt=St,ht=nt,ye=Wt,tt=Rt,we=kt,Ce=D,ut=Dt;function Ee(e,t){const s=e.size,o=pe.getPositions(t);for(let n=0;n<o.length;n++){const r=o[n][0],i=o[n][1];for(let l=-1;l<=7;l++)if(!(r+l<=-1||s<=r+l))for(let a=-1;a<=7;a++)i+a<=-1||s<=i+a||(l>=0&&l<=6&&(a===0||a===6)||a>=0&&a<=6&&(l===0||l===6)||l>=2&&l<=4&&a>=2&&a<=4?e.set(r+l,i+a,!0,!0):e.set(r+l,i+a,!1,!0))}}function Be(e){const t=e.size;for(let s=8;s<t-8;s++){const o=s%2===0;e.set(s,6,o,!0),e.set(6,s,o,!0)}}function Ie(e,t){const s=me.getPositions(t);for(let o=0;o<s.length;o++){const n=s[o][0],r=s[o][1];for(let i=-2;i<=2;i++)for(let l=-2;l<=2;l++)i===-2||i===2||l===-2||l===2||i===0&&l===0?e.set(n+i,r+l,!0,!0):e.set(n+i,r+l,!1,!0)}}function Te(e,t){const s=e.size,o=tt.getEncodedBits(t);let n,r,i;for(let l=0;l<18;l++)n=Math.floor(l/3),r=l%3+s-8-3,i=(o>>l&1)===1,e.set(n,r,i,!0),e.set(r,n,i,!0)}function ct(e,t,s){const o=e.size,n=we.getEncodedBits(t,s);let r,i;for(r=0;r<15;r++)i=(n>>r&1)===1,r<6?e.set(r,8,i,!0):r<8?e.set(r+1,8,i,!0):e.set(o-15+r,8,i,!0),r<8?e.set(8,o-r-1,i,!0):r<9?e.set(8,15-r-1+1,i,!0):e.set(8,15-r-1,i,!0);e.set(o-8,8,1,!0)}function ve(e,t){const s=e.size;let o=-1,n=s-1,r=7,i=0;for(let l=s-1;l>0;l-=2)for(l===6&&l--;;){for(let a=0;a<2;a++)if(!e.isReserved(n,l-a)){let c=!1;i<t.length&&(c=(t[i]>>>r&1)===1),e.set(n,l-a,c),r--,r===-1&&(i++,r=7)}if(n+=o,n<0||s<=n){n-=o,o=-o;break}}}function Ae(e,t,s){const o=new ge;s.forEach(function(a){o.put(a.mode.bit,4),o.put(a.getLength(),Ce.getCharCountIndicator(a.mode,e)),a.write(o)});const n=rt.getSymbolTotalCodewords(e),r=ht.getTotalCodewordsCount(e,t),i=(n-r)*8;for(o.getLengthInBits()+4<=i&&o.put(0,4);o.getLengthInBits()%8!==0;)o.putBit(0);const l=(i-o.getLengthInBits())/8;for(let a=0;a<l;a++)o.put(a%2?17:236,8);return be(o,e,t)}function be(e,t,s){const o=rt.getSymbolTotalCodewords(t),n=ht.getTotalCodewordsCount(t,s),r=o-n,i=ht.getBlocksCount(t,s),l=o%i,a=i-l,c=Math.floor(o/i),d=Math.floor(r/i),E=d+1,m=c-d,f=new ye(m);let y=0;const w=new Array(i),v=new Array(i);let p=0;const h=new Uint8Array(e.buffer);for(let T=0;T<i;T++){const b=T<a?d:E;w[T]=h.slice(y,y+b),v[T]=f.encode(w[T]),y+=b,p=Math.max(p,b)}const u=new Uint8Array(o);let g=0,B,I;for(B=0;B<p;B++)for(I=0;I<i;I++)B<w[I].length&&(u[g++]=w[I][B]);for(B=0;B<m;B++)for(I=0;I<i;I++)u[g++]=v[I][B];return u}function Ne(e,t,s,o){let n;if(Array.isArray(e))n=ut.fromArray(e);else if(typeof e=="string"){let c=t;if(!c){const d=ut.rawSplit(e);c=tt.getBestVersionForData(d,s)}n=ut.fromString(e,c||40)}else throw new Error("Invalid data");const r=tt.getBestVersionForData(n,s);if(!r)throw new Error("The amount of data is too big to be stored in a QR Code");if(!t)t=r;else if(t<r)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+r+`.
`);const i=Ae(t,s,n),l=rt.getSymbolSize(t),a=new he(l);return Ee(a,t),Be(a),Ie(a,t),ct(a,s,0),t>=7&&Te(a,t),ve(a,i),isNaN(o)&&(o=gt.getBestMask(a,ct.bind(null,a,s))),gt.applyMask(o,a),ct(a,s,o),{modules:a,version:t,errorCorrectionLevel:s,maskPattern:o,segments:n}}At.create=function(t,s){if(typeof t>"u"||t==="")throw new Error("No input text");let o=lt.M,n,r;return typeof s<"u"&&(o=lt.from(s.errorCorrectionLevel,lt.M),n=tt.from(s.version),r=gt.from(s.maskPattern),s.toSJISFunc&&rt.setToSJISFunction(s.toSJISFunc)),Ne(t,n,o,r)};var Vt={},Ct={};(function(e){function t(s){if(typeof s=="number"&&(s=s.toString()),typeof s!="string")throw new Error("Color should be defined as hex string");let o=s.slice().replace("#","").split("");if(o.length<3||o.length===5||o.length>8)throw new Error("Invalid hex color: "+s);(o.length===3||o.length===4)&&(o=Array.prototype.concat.apply([],o.map(function(r){return[r,r]}))),o.length===6&&o.push("F","F");const n=parseInt(o.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:n&255,hex:"#"+o.slice(0,6).join("")}}e.getOptions=function(o){o||(o={}),o.color||(o.color={});const n=typeof o.margin>"u"||o.margin===null||o.margin<0?4:o.margin,r=o.width&&o.width>=21?o.width:void 0,i=o.scale||4;return{width:r,scale:r?4:i,margin:n,color:{dark:t(o.color.dark||"#000000ff"),light:t(o.color.light||"#ffffffff")},type:o.type,rendererOpts:o.rendererOpts||{}}},e.getScale=function(o,n){return n.width&&n.width>=o+n.margin*2?n.width/(o+n.margin*2):n.scale},e.getImageWidth=function(o,n){const r=e.getScale(o,n);return Math.floor((o+n.margin*2)*r)},e.qrToImageData=function(o,n,r){const i=n.modules.size,l=n.modules.data,a=e.getScale(i,r),c=Math.floor((i+r.margin*2)*a),d=r.margin*a,E=[r.color.light,r.color.dark];for(let m=0;m<c;m++)for(let f=0;f<c;f++){let y=(m*c+f)*4,w=r.color.light;if(m>=d&&f>=d&&m<c-d&&f<c-d){const v=Math.floor((m-d)/a),p=Math.floor((f-d)/a);w=E[l[v*i+p]?1:0]}o[y++]=w.r,o[y++]=w.g,o[y++]=w.b,o[y]=w.a}}})(Ct);(function(e){const t=Ct;function s(n,r,i){n.clearRect(0,0,r.width,r.height),r.style||(r.style={}),r.height=i,r.width=i,r.style.height=i+"px",r.style.width=i+"px"}function o(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}e.render=function(r,i,l){let a=l,c=i;typeof a>"u"&&(!i||!i.getContext)&&(a=i,i=void 0),i||(c=o()),a=t.getOptions(a);const d=t.getImageWidth(r.modules.size,a),E=c.getContext("2d"),m=E.createImageData(d,d);return t.qrToImageData(m.data,r,a),s(E,c,d),E.putImageData(m,0,0),c},e.renderToDataURL=function(r,i,l){let a=l;typeof a>"u"&&(!i||!i.getContext)&&(a=i,i=void 0),a||(a={});const c=e.render(r,i,a),d=a.type||"image/png",E=a.rendererOpts||{};return c.toDataURL(d,E.quality)}})(Vt);var zt={};const Me=Ct;function vt(e,t){const s=e.a/255,o=t+'="'+e.hex+'"';return s<1?o+" "+t+'-opacity="'+s.toFixed(2).slice(1)+'"':o}function dt(e,t,s){let o=e+t;return typeof s<"u"&&(o+=" "+s),o}function Se(e,t,s){let o="",n=0,r=!1,i=0;for(let l=0;l<e.length;l++){const a=Math.floor(l%t),c=Math.floor(l/t);!a&&!r&&(r=!0),e[l]?(i++,l>0&&a>0&&e[l-1]||(o+=r?dt("M",a+s,.5+c+s):dt("m",n,0),n=0,r=!1),a+1<t&&e[l+1]||(o+=dt("h",i),i=0)):n++}return o}zt.render=function(t,s,o){const n=Me.getOptions(s),r=t.modules.size,i=t.modules.data,l=r+n.margin*2,a=n.color.light.a?"<path "+vt(n.color.light,"fill")+' d="M0 0h'+l+"v"+l+'H0z"/>':"",c="<path "+vt(n.color.dark,"stroke")+' d="'+Se(i,r,n.margin)+'"/>',d='viewBox="0 0 '+l+" "+l+'"',m='<svg xmlns="http://www.w3.org/2000/svg" '+(n.width?'width="'+n.width+'" height="'+n.width+'" ':"")+d+' shape-rendering="crispEdges">'+a+c+`</svg>
`;return typeof o=="function"&&o(null,m),m};const _e=jt,mt=At,Ht=Vt,Pe=zt;function Et(e,t,s,o,n){const r=[].slice.call(arguments,1),i=r.length,l=typeof r[i-1]=="function";if(!l&&!_e())throw new Error("Callback required as last argument");if(l){if(i<2)throw new Error("Too few arguments provided");i===2?(n=s,s=t,t=o=void 0):i===3&&(t.getContext&&typeof n>"u"?(n=o,o=void 0):(n=o,o=s,s=t,t=void 0))}else{if(i<1)throw new Error("Too few arguments provided");return i===1?(s=t,t=o=void 0):i===2&&!t.getContext&&(o=s,s=t,t=void 0),new Promise(function(a,c){try{const d=mt.create(s,o);a(e(d,t,o))}catch(d){c(d)}})}try{const a=mt.create(s,o);n(null,e(a,t,o))}catch(a){n(a)}}Q.create=mt.create;Q.toCanvas=Et.bind(null,Ht.render);Q.toDataURL=Et.bind(null,Ht.renderToDataURL);Q.toString=Et.bind(null,function(e,t,s){return Pe.render(e,s)});const Re={class:"payment-methods"},Le={class:"payment-amount"},ke={class:"amount-value"},Ue={class:"recharge-recommendation"},De={class:"recharge-options"},Fe={class:"payment-actions"},Ve={class:"qr-code-container"},ze={class:"payment-info"},He={class:"amount-display"},Ke={class:"amount"},$e={key:0,class:"order-id"},Je={class:"payment-wrapper"},Oe={key:0,class:"qr-code-wrapper"},je={key:1,class:"redirect-wrapper"},Ye=["innerHTML"],qe={class:"payment-status"},Ge={key:0,class:"status-pending"},Qe={key:1,class:"status-success"},We={key:2,class:"status-closed"},Ze={class:"dialog-footer"},Xe={__name:"PaymentMethods",props:{amount:{type:Number,required:!0},subject:{type:String,default:"TopMeans服务购买"},goods:{type:Object,default:()=>({})},userId:{type:Number,default:null},userAccount:{type:String,default:null}},emits:["payment-success","payment-cancel"],setup(e,{emit:t}){const s=e,o=t,n=L("alipay"),r=L(!1),i=L(!1),l=L(null),a=L(""),c=L(""),d=L(null),E=L("redirect"),m=L(""),f=()=>"http://localhost:3999",y=async()=>{if(n.value!=="alipay"){k.warning("暂时只支持支付宝支付");return}r.value=!0;try{const u=await(await fetch(`${f()}/api/payment/create`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:s.amount,subject:s.subject,goods:s.goods,userId:s.userId,userAccount:s.userAccount,paymentMethod:n.value})})).json();u.success?(a.value=u.orderId,u.paymentUrl&&u.paymentUrl.includes("<form")?(E.value="redirect",m.value=u.paymentUrl,i.value=!0,c.value="pending",setTimeout(()=>{const g=document.querySelectorAll('form[name*="alipaySDKSubmit"]');g.length>0&&g[0].submit()},1e3),k.success("订单创建成功，正在跳转到支付页面...")):(E.value="qrcode",await w(u.paymentUrl),i.value=!0,c.value="pending",v(),k.success("订单创建成功，请扫码支付"))):k.error(u.message||"创建支付订单失败")}catch(h){console.error("支付请求失败:",h),k.error("网络请求失败，请重试")}finally{r.value=!1}},w=async h=>{try{l.value&&await Q.toCanvas(l.value,h,{width:200,margin:2,color:{dark:"#000000",light:"#FFFFFF"}})}catch(u){console.error("生成二维码失败:",u),k.error("生成二维码失败")}},v=()=>{d.value&&clearInterval(d.value),d.value=setInterval(async()=>{try{const u=await(await fetch(`${f()}/api/payment/status/${a.value}`)).json();u.success&&(u.status==="paid"?(c.value="paid",clearInterval(d.value),k.success("支付成功！"),setTimeout(()=>{i.value=!1,o("payment-success",{orderId:a.value,amount:s.amount,alipayTradeNo:u.alipayTradeNo})},2e3)):(u.status==="closed"||u.status==="cancelled")&&(c.value=u.status,clearInterval(d.value),k.warning("订单已关闭")))}catch(h){console.error("查询支付状态失败:",h)}},3e3)},p=async()=>{if(d.value&&clearInterval(d.value),a.value)try{await fetch(`${f()}/api/payment/cancel/${a.value}`,{method:"POST"})}catch(h){console.error("取消订单失败:",h)}i.value=!1,c.value="",a.value="",o("payment-cancel")};return Ot(()=>{d.value&&clearInterval(d.value)}),(h,u)=>{const g=z("el-radio-button"),B=z("el-radio-group"),I=z("el-button"),T=z("el-card"),b=z("el-icon"),_=z("el-dialog");return V(),F("div",Re,[M(T,{class:"payment-card"},{header:S(()=>u[5]||(u[5]=[C("div",{class:"card-header"},[C("span",null,"选择支付方式")],-1)])),default:S(()=>[M(B,{modelValue:n.value,"onUpdate:modelValue":u[0]||(u[0]=A=>n.value=A),class:"payment-methods-group"},{default:S(()=>[M(g,{label:"alipay"},{default:S(()=>u[6]||(u[6]=[C("img",{src:"/images/alipay.png?url",alt:"支付宝",class:"payment-icon"},null,-1),C("span",null,"支付宝",-1)])),_:1}),M(g,{label:"wechat"},{default:S(()=>u[7]||(u[7]=[C("img",{src:"/images/wechat.png?url",alt:"微信支付",class:"payment-icon"},null,-1),C("span",null,"微信支付",-1)])),_:1})]),_:1},8,["modelValue"]),C("div",Le,[u[8]||(u[8]=C("span",{class:"amount-label"},"支付金额：",-1)),C("span",ke,"¥"+j(e.amount),1)]),C("div",Ue,[u[12]||(u[12]=C("div",{class:"recommendation-header"},[C("span",{class:"recommendation-icon"},"💡"),C("span",{class:"recommendation-title"},"充值有优惠")],-1)),C("div",De,[C("div",{class:"recharge-option",onClick:u[1]||(u[1]=A=>h.handleRecharge(50))},u[9]||(u[9]=[C("span",{class:"option-amount"},"¥50",-1),C("span",{class:"option-bonus"},"送¥10",-1)])),C("div",{class:"recharge-option",onClick:u[2]||(u[2]=A=>h.handleRecharge(100))},u[10]||(u[10]=[C("span",{class:"option-amount"},"¥100",-1),C("span",{class:"option-bonus"},"送¥25",-1)])),C("div",{class:"recharge-option",onClick:u[3]||(u[3]=A=>h.handleRecharge(200))},u[11]||(u[11]=[C("span",{class:"option-amount"},"¥200",-1),C("span",{class:"option-bonus"},"送¥60",-1)]))]),u[13]||(u[13]=C("p",{class:"recharge-note"},"充值后余额可用于后续服务，享受更多优惠",-1))]),C("div",Fe,[M(I,{type:"primary",onClick:y,loading:r.value},{default:S(()=>u[14]||(u[14]=[Y(" 确认支付 ")])),_:1},8,["loading"])])]),_:1}),M(_,{modelValue:i.value,"onUpdate:modelValue":u[4]||(u[4]=A=>i.value=A),title:"扫码支付",width:"350px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!1},{footer:S(()=>[C("span",Ze,[M(I,{onClick:p,disabled:c.value==="paid"},{default:S(()=>[Y(j(c.value==="paid"?"支付成功":"取消支付"),1)]),_:1},8,["disabled"])])]),default:S(()=>[C("div",Ve,[C("div",ze,[C("p",He,[u[15]||(u[15]=Y("支付金额：")),C("span",Ke,"¥"+j(e.amount),1)]),a.value?(V(),F("p",$e,"订单号："+j(a.value),1)):it("",!0)]),C("div",Je,[E.value==="qrcode"?(V(),F("div",Oe,[C("canvas",{ref_key:"qrCodeCanvas",ref:l,class:"qr-code-canvas"},null,512)])):E.value==="redirect"?(V(),F("div",je,[C("div",{innerHTML:m.value,class:"payment-form"},null,8,Ye)])):it("",!0)]),C("div",qe,[c.value==="pending"?(V(),F("p",Ge,[M(b,{class:"loading-icon"},{default:S(()=>[M(Bt(Kt))]),_:1}),Y(" 请使用"+j(n.value==="alipay"?"支付宝":"微信")+"扫码支付 ",1)])):c.value==="paid"?(V(),F("p",Qe,[M(b,{class:"success-icon"},{default:S(()=>[M(Bt($t))]),_:1}),u[16]||(u[16]=Y(" 支付成功！ "))])):c.value==="closed"?(V(),F("p",We," 订单已关闭 ")):it("",!0)]),u[17]||(u[17]=C("div",{class:"payment-tips"},[C("p",null,"• 请在15分钟内完成支付"),C("p",null,"• 支付完成后页面将自动跳转")],-1))])]),_:1},8,["modelValue"])])}}},en=Jt(Xe,[["__scopeId","data-v-297a1d59"]]);export{en as _};
