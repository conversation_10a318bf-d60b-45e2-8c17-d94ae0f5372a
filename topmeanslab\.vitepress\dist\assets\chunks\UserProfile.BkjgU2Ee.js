import{_ as me,h as ce,C as h,c,o as r,j as e,e as B,t as f,G as s,w as l,k as b,b as E,a as V,F as Q,B as te,Z as he,n as be,p as P,a3 as ae,v as _e,X as Ie,q as $e,b3 as Be,ao as we,ap as ke,P as Ue}from"./framework.oPHriSgN.js";import{u as ve,m as Se,l as De,a as Le,c as Te,v as fe,b as je,E as v,d as ye,e as ge,p as Ce,f as ze,s as Ee,g as Me}from"./theme.DE6uTiF9.js";import{m as Ae}from"./TopmeansMarkdownService.C-2qduF6.js";const Je={class:"card-header"},Fe={class:"author-info"},Ge=["src","alt"],He={class:"author-details"},Oe={class:"author-name"},Re={class:"publish-time"},Ye={key:0,class:"card-actions"},qe={class:"action-trigger"},Ne={class:"card-content"},Ke={class:"journal-title"},We={class:"travel-info"},Ze={class:"travel-route"},Xe={class:"travel-days"},Qe={class:"travel-mode"},et={class:"travel-date"},tt={key:0,class:"content-preview"},st={key:1,class:"tags"},ot={key:0,class:"more-tags"},lt={key:0,class:"card-cover"},at=["src","alt"],nt={class:"card-footer"},rt={class:"engagement-stats"},it={class:"stat-button"},ut={key:0,class:"privacy-indicator"},dt={__name:"JournalCard",props:{journal:{type:Object,required:!0},showActions:{type:Boolean,default:!0},showLike:{type:Boolean,default:!0},showPrivacy:{type:Boolean,default:!1}},emits:["like","comment","edit","delete","togglePublic","click"],setup(d,{emit:W}){const i=d,H=W,x=ve(),D="http://localhost:3999/api",N=ce(()=>x.userInfo&&x.userInfo.id===i.journal.user_id),O=y=>{const p=new Date(y),T=new Date-p,F=Math.floor(T/(1e3*60*60*24));if(F===0){const J=Math.floor(T/36e5);if(J===0){const G=Math.floor(T/6e4);return G<1?"刚刚":`${G}分钟前`}return`${J}小时前`}else return F===1?"昨天":F<7?`${F}天前`:p.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"})},M=y=>{if(!y)return"";const p=y.replace(/!\[.*?\]\(.*?\)/g,"").replace(/\[.*?\]\(.*?\)/g,"").replace(/[#*>`-]/g,"").replace(/\n/g," ").trim();return p.length>100?p.substring(0,100)+"...":p},I=()=>{H("click",i.journal)},L=async()=>{if(!x.isLoggedIn){v.warning("请先登录");return}try{const p=await(await fetch(`${D}/journals/${i.journal.id}/like`,{method:"POST",headers:{Authorization:`Bearer ${x.token}`}})).json();p.success?H("like",{journalId:i.journal.id,isLiked:p.isLiked}):v.error(p.message||"操作失败")}catch(y){console.error("点赞失败:",y),v.error("操作失败，请稍后重试")}},a=()=>{H("comment",i.journal)},C=y=>{switch(y){case"edit":H("edit",i.journal);break;case"togglePublic":q();break;case"delete":w();break;case"report":A();break}},q=async()=>{try{const p=await(await fetch(`${D}/journals/${i.journal.id}/toggle-public`,{method:"PATCH",headers:{Authorization:`Bearer ${x.token}`}})).json();p.success?(v.success(p.message),H("togglePublic",{journalId:i.journal.id,isPublic:p.isPublic})):v.error(p.message||"操作失败")}catch(y){console.error("切换公开状态失败:",y),v.error("操作失败，请稍后重试")}},w=()=>{ye.confirm("确定要删除这篇游记吗？删除后无法恢复。","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const p=await(await fetch(`${D}/journals/${i.journal.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${x.token}`}})).json();p.success?(v.success("游记删除成功"),H("delete",i.journal.id)):v.error(p.message||"删除失败")}catch(y){console.error("删除游记失败:",y),v.error("删除失败，请稍后重试")}})},A=()=>{v.info("举报功能正在开发中")};return(y,p)=>{const S=h("el-icon"),T=h("el-dropdown-item"),F=h("el-dropdown-menu"),J=h("el-dropdown"),G=h("el-tag");return r(),c("div",{class:"journal-card",onClick:I},[e("div",Je,[e("div",Fe,[e("img",{src:d.journal.avatar,alt:d.journal.nickname,class:"author-avatar"},null,8,Ge),e("div",He,[e("div",Oe,f(d.journal.nickname),1),e("div",Re,f(O(d.journal.created_at)),1)])]),d.showActions?(r(),c("div",Ye,[s(J,{onCommand:C,trigger:"click"},{dropdown:l(()=>[s(F,null,{default:l(()=>[N.value?(r(),E(T,{key:0,command:"edit"},{default:l(()=>p[0]||(p[0]=[V("编辑")])),_:1})):B("",!0),N.value?(r(),E(T,{key:1,command:"togglePublic"},{default:l(()=>[V(f(d.journal.is_public?"设为私密":"设为公开"),1)]),_:1})):B("",!0),N.value?(r(),E(T,{key:2,command:"delete",divided:""},{default:l(()=>p[1]||(p[1]=[V("删除")])),_:1})):B("",!0),N.value?B("",!0):(r(),E(T,{key:3,command:"report"},{default:l(()=>p[2]||(p[2]=[V("举报")])),_:1}))]),_:1})]),default:l(()=>[e("span",qe,[s(S,null,{default:l(()=>[s(b(Se))]),_:1})])]),_:1})])):B("",!0)]),e("div",Ne,[e("h3",Ke,f(d.journal.title),1),e("div",We,[e("span",Ze,[s(S,null,{default:l(()=>[s(b(De))]),_:1}),V(" "+f(d.journal.start_location)+" → "+f(d.journal.destination),1)]),e("span",Xe,f(d.journal.travel_days)+"天",1),e("span",Qe,f(d.journal.travel_mode),1),e("span",et,f(O(d.journal.travel_date)),1)]),d.journal.content?(r(),c("div",tt,f(M(d.journal.content)),1)):B("",!0),d.journal.tags&&d.journal.tags.length>0?(r(),c("div",st,[(r(!0),c(Q,null,te(d.journal.tags.slice(0,3),z=>(r(),E(G,{key:z,size:"small",class:"tag-item"},{default:l(()=>[V(f(z),1)]),_:2},1024))),128)),d.journal.tags.length>3?(r(),c("span",ot," +"+f(d.journal.tags.length-3),1)):B("",!0)])):B("",!0)]),d.journal.cover_image?(r(),c("div",lt,[e("img",{src:d.journal.cover_image,alt:d.journal.title,class:"cover-image"},null,8,at)])):B("",!0),e("div",nt,[e("div",rt,[d.showLike?(r(),c("button",{key:0,class:be(["stat-button",{liked:d.journal.is_liked}]),onClick:he(L,["stop"])},[s(S,null,{default:l(()=>[s(b(Le))]),_:1}),e("span",null,f(d.journal.like_count||0),1)],2)):B("",!0),e("button",{class:"stat-button",onClick:he(a,["stop"])},[s(S,null,{default:l(()=>[s(b(Te))]),_:1}),e("span",null,f(d.journal.comment_count||0),1)]),e("button",it,[s(S,null,{default:l(()=>[s(b(fe))]),_:1}),e("span",null,f(d.journal.view_count||0),1)])]),d.showPrivacy?(r(),c("div",ut,[d.journal.is_public?(r(),E(S,{key:0},{default:l(()=>[s(b(fe))]),_:1})):(r(),E(S,{key:1},{default:l(()=>[s(b(je))]),_:1})),e("span",null,f(d.journal.is_public?"公开":"私密"),1)])):B("",!0)])])}}},ct=me(dt,[["__scopeId","data-v-a363e6e7"]]),mt={class:"journal-editor"},vt={class:"editor-container"},pt={class:"editor-header"},ft={class:"editor-title"},gt={class:"editor-actions"},_t={class:"editor-form"},yt={class:"form-item"},ht={class:"travel-info"},wt={class:"form-row"},kt={class:"form-item"},bt={class:"form-item"},$t={class:"form-row"},jt={class:"form-item"},Ct={class:"form-item"},Pt={class:"form-item"},Vt={class:"form-item"},xt={class:"cover-upload"},It={key:0,class:"cover-preview"},Bt=["src"],Ut={class:"cover-overlay"},St={key:1,class:"cover-placeholder"},Dt={class:"form-item"},Lt={class:"content-editor"},Tt={class:"editor-toolbar"},zt=["onClick","title"],Et={class:"form-item"},Mt={class:"tags-input"},At={class:"form-item"},Jt={class:"privacy-settings"},Ft={class:"privacy-option"},Gt={class:"privacy-option"},Ht={__name:"JournalEditor",props:{journal:{type:Object,default:null}},emits:["save","cancel"],setup(d,{emit:W}){const i=d,H=W,x=ve(),D="http://localhost:3999/api",N=[{name:"bold",icon:"B",syntax:"**粗体**",title:"粗体"},{name:"italic",icon:"I",syntax:"*斜体*",title:"斜体"},{name:"heading",icon:"H",syntax:"## 标题",title:"标题"},{name:"quote",icon:'"',syntax:"> 引用",title:"引用"},{name:"list",icon:"•",syntax:"- 列表项",title:"列表"},{name:"link",icon:"🔗",syntax:"[链接文字](URL)",title:"链接"}],O=P(!1),M=P(!1),I=P(""),L=ce(()=>!!i.journal),a=ae({title:"",content:"",coverImage:"",destination:"",startLocation:"",travelDays:1,travelMode:"自驾",travelDate:"",isPublic:!0,tags:[],images:[]}),C=`${D}/journals/upload-image`,q=ce(()=>({Authorization:`Bearer ${x.token}`}));_e(()=>{i.journal&&Object.assign(a,{...i.journal,travelDate:i.journal.travel_date,travelDays:i.journal.travel_days,travelMode:i.journal.travel_mode,startLocation:i.journal.start_location,coverImage:i.journal.cover_image,isPublic:i.journal.is_public,tags:i.journal.tags||[]})});const w=$=>{const o=$.type.startsWith("image/"),R=$.size/1024/1024<5;return o?R?!0:(v.error("图片大小不能超过 5MB！"),!1):(v.error("只能上传图片文件！"),!1)},A=$=>{$.success?(a.coverImage=`http://localhost:3999${$.imageUrl}`,v.success("封面上传成功")):v.error($.message||"封面上传失败")},y=$=>{if($.success){const o=`http://localhost:3999${$.imageUrl}`,R=`![图片](${o})`;a.content+=R,a.images||(a.images=[]),a.images.push(o),v.success("图片上传成功")}else v.error($.message||"图片上传失败")},p=$=>{console.error("上传失败:",$),v.error("上传失败，请稍后重试")},S=()=>{a.coverImage=""},T=$=>{a.content+=$},F=()=>{I.value&&!a.tags.includes(I.value)&&(a.tags.push(I.value),I.value=""),M.value=!1},J=$=>{const o=a.tags.indexOf($);o>-1&&a.tags.splice(o,1)},G=()=>a.title.trim()?a.content.trim()?a.destination.trim()?a.startLocation.trim()?a.travelDate?!0:(v.error("请选择旅行日期"),!1):(v.error("请输入出发地"),!1):(v.error("请输入目的地"),!1):(v.error("请输入游记内容"),!1):(v.error("请输入游记标题"),!1),z=async()=>{if(G())try{O.value=!0;const $={title:a.title,content:a.content,coverImage:a.coverImage,destination:a.destination,startLocation:a.startLocation,travelDays:a.travelDays,travelMode:a.travelMode,travelDate:a.travelDate,isPublic:a.isPublic,tags:a.tags,images:a.images},o=L.value?`${D}/journals/${i.journal.id}`:`${D}/journals`,R=L.value?"PUT":"POST",_=await(await fetch(o,{method:R,headers:{"Content-Type":"application/json",Authorization:`Bearer ${x.token}`},body:JSON.stringify($)})).json();_.success?(v.success(L.value?"游记更新成功":"游记发布成功"),H("save",_)):v.error(_.message||"操作失败")}catch($){console.error("保存游记失败:",$),v.error("保存失败，请稍后重试")}finally{O.value=!1}},oe=()=>{ye.confirm("确定要取消编辑吗？未保存的内容将丢失。","确认取消",{confirmButtonText:"确定",cancelButtonText:"继续编辑",type:"warning"}).then(()=>{H("cancel")}).catch(()=>{})};return($,o)=>{const R=h("el-button"),K=h("el-input"),_=h("el-date-picker"),g=h("el-option"),U=h("el-select"),j=h("el-icon"),Z=h("el-upload"),le=h("el-tag"),Y=h("el-radio"),ne=h("el-radio-group");return r(),c("div",mt,[e("div",vt,[e("div",pt,[e("h3",ft,f(L.value?"编辑游记":"写游记"),1),e("div",gt,[s(R,{onClick:oe,size:"small"},{default:l(()=>o[10]||(o[10]=[V("取消")])),_:1}),s(R,{onClick:z,loading:O.value,type:"primary",size:"small"},{default:l(()=>[V(f(L.value?"更新":"发布"),1)]),_:1},8,["loading"])])]),e("div",_t,[e("div",yt,[o[11]||(o[11]=e("label",{class:"form-label"},"游记标题",-1)),s(K,{modelValue:a.title,"onUpdate:modelValue":o[0]||(o[0]=u=>a.title=u),placeholder:"请输入游记标题",maxlength:"100","show-word-limit":"",class:"title-input"},null,8,["modelValue"])]),e("div",ht,[e("div",wt,[e("div",kt,[o[12]||(o[12]=e("label",{class:"form-label"},"目的地",-1)),s(K,{modelValue:a.destination,"onUpdate:modelValue":o[1]||(o[1]=u=>a.destination=u),placeholder:"请输入目的地",maxlength:"50"},null,8,["modelValue"])]),e("div",bt,[o[13]||(o[13]=e("label",{class:"form-label"},"出发地",-1)),s(K,{modelValue:a.startLocation,"onUpdate:modelValue":o[2]||(o[2]=u=>a.startLocation=u),placeholder:"请输入出发地",maxlength:"50"},null,8,["modelValue"])])]),e("div",$t,[e("div",jt,[o[14]||(o[14]=e("label",{class:"form-label"},"旅行日期",-1)),s(_,{modelValue:a.travelDate,"onUpdate:modelValue":o[3]||(o[3]=u=>a.travelDate=u),type:"date",placeholder:"选择旅行日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),e("div",Ct,[o[15]||(o[15]=e("label",{class:"form-label"},"旅行天数",-1)),s(U,{modelValue:a.travelDays,"onUpdate:modelValue":o[4]||(o[4]=u=>a.travelDays=u),placeholder:"选择天数"},{default:l(()=>[(r(),c(Q,null,te(30,u=>s(g,{key:u,label:`${u}天`,value:u},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),e("div",Pt,[o[16]||(o[16]=e("label",{class:"form-label"},"旅行方式",-1)),s(U,{modelValue:a.travelMode,"onUpdate:modelValue":o[5]||(o[5]=u=>a.travelMode=u),placeholder:"选择方式"},{default:l(()=>[s(g,{label:"自驾",value:"自驾"}),s(g,{label:"租车",value:"租车"}),s(g,{label:"公共交通",value:"公共交通"}),s(g,{label:"飞机",value:"飞机"}),s(g,{label:"火车",value:"火车"}),s(g,{label:"徒步",value:"徒步"})]),_:1},8,["modelValue"])])])]),e("div",Vt,[o[19]||(o[19]=e("label",{class:"form-label"},"封面图片",-1)),e("div",xt,[s(Z,{ref:"coverUpload",action:C,headers:q.value,"before-upload":w,"on-success":A,"on-error":p,"show-file-list":!1,accept:"image/*"},{default:l(()=>[a.coverImage?(r(),c("div",It,[e("img",{src:a.coverImage,alt:"封面"},null,8,Bt),e("div",Ut,[s(j,null,{default:l(()=>[s(b(ge))]),_:1}),o[17]||(o[17]=e("span",null,"更换封面",-1))])])):(r(),c("div",St,[s(j,null,{default:l(()=>[s(b(Ce))]),_:1}),o[18]||(o[18]=e("span",null,"点击上传封面",-1))]))]),_:1},8,["headers"]),a.coverImage?(r(),c("button",{key:0,onClick:S,class:"remove-cover"},[s(j,null,{default:l(()=>[s(b(ze))]),_:1})])):B("",!0)])]),e("div",Dt,[o[21]||(o[21]=e("label",{class:"form-label"},"游记内容",-1)),e("div",Lt,[e("div",Tt,[(r(),c(Q,null,te(N,u=>e("button",{key:u.name,onClick:n=>T(u.syntax),class:"toolbar-btn",title:u.title},f(u.icon),9,zt)),64)),s(Z,{action:C,headers:q.value,"before-upload":w,"on-success":y,"on-error":p,"show-file-list":!1,accept:"image/*",class:"image-upload"},{default:l(()=>o[20]||(o[20]=[e("button",{class:"toolbar-btn",title:"插入图片"}," 📷 ",-1)])),_:1},8,["headers"])]),s(K,{modelValue:a.content,"onUpdate:modelValue":o[6]||(o[6]=u=>a.content=u),type:"textarea",rows:15,placeholder:"请输入游记内容，支持Markdown语法",class:"content-textarea"},null,8,["modelValue"])])]),e("div",Et,[o[23]||(o[23]=e("label",{class:"form-label"},"标签",-1)),e("div",Mt,[(r(!0),c(Q,null,te(a.tags,u=>(r(),E(le,{key:u,onClose:n=>J(u),closable:""},{default:l(()=>[V(f(u),1)]),_:2},1032,["onClose"]))),128)),M.value?(r(),E(K,{key:0,modelValue:I.value,"onUpdate:modelValue":o[7]||(o[7]=u=>I.value=u),onKeyup:Ie(F,["enter"]),onBlur:F,size:"small",class:"new-tag-input",placeholder:"添加标签"},null,8,["modelValue"])):(r(),E(R,{key:1,onClick:o[8]||(o[8]=u=>M.value=!0),size:"small",class:"new-tag-btn"},{default:l(()=>o[22]||(o[22]=[V(" + 添加标签 ")])),_:1}))])]),e("div",At,[o[26]||(o[26]=e("label",{class:"form-label"},"隐私设置",-1)),e("div",Jt,[s(ne,{modelValue:a.isPublic,"onUpdate:modelValue":o[9]||(o[9]=u=>a.isPublic=u)},{default:l(()=>[s(Y,{label:!0},{default:l(()=>[e("div",Ft,[s(j,null,{default:l(()=>[s(b(fe))]),_:1}),o[24]||(o[24]=e("div",null,[e("div",{class:"option-title"},"公开"),e("div",{class:"option-desc"},"所有人都可以查看")],-1))])]),_:1}),s(Y,{label:!1},{default:l(()=>[e("div",Gt,[s(j,null,{default:l(()=>[s(b(je))]),_:1}),o[25]||(o[25]=e("div",null,[e("div",{class:"option-title"},"私密"),e("div",{class:"option-desc"},"只有自己可以查看")],-1))])]),_:1})]),_:1},8,["modelValue"])])])])])])}}},Ot=me(Ht,[["__scopeId","data-v-dee090d3"]]),Rt={class:"journal-list"},Yt={key:0,class:"list-header"},qt={class:"header-title"},Nt={key:0,class:"journal-count"},Kt={class:"header-actions"},Wt={class:"filter-controls"},Zt={class:"list-content"},Xt={key:0,class:"loading-container"},Qt={key:1,class:"empty-state"},es={class:"empty-text"},ts={key:2,class:"journals-grid"},ss={key:3,class:"pagination-container"},os={__name:"JournalList",props:{title:{type:String,default:"游记列表"},apiUrl:{type:String,required:!0},showHeader:{type:Boolean,default:!0},showCreateButton:{type:Boolean,default:!0},showActions:{type:Boolean,default:!0},showLike:{type:Boolean,default:!0},showPrivacy:{type:Boolean,default:!1},showPublicFilter:{type:Boolean,default:!1},showPagination:{type:Boolean,default:!0},emptyText:{type:String,default:"暂无游记"},autoLoad:{type:Boolean,default:!0}},emits:["journalClick","journalCreated","journalUpdated","journalDeleted"],setup(d,{expose:W,emit:i}){const H=d,x=i,D=ve(),N="http://localhost:3999/api",O=P(!1),M=P([]),I=P(0),L=P("created_at"),a=P(""),C=P(!1),q=P(null),w=ae({page:1,pageSize:10,total:0,totalPages:0});_e(()=>{H.autoLoad&&A()}),$e([L,a],()=>{w.page=1,A()});const A=async()=>{var _,g,U;if(!O.value)try{O.value=!0;const j=new URLSearchParams({page:w.page.toString(),pageSize:w.pageSize.toString(),sortBy:L.value});a.value&&j.append("public",a.value==="public"?"1":"0");const Z=`${N}${H.apiUrl}?${j}`,Y=await(await fetch(Z,{headers:D.token?{Authorization:`Bearer ${D.token}`}:{}})).json();Y.success?(M.value=Y.data||[],I.value=((_=Y.pagination)==null?void 0:_.total)||0,w.total=((g=Y.pagination)==null?void 0:g.total)||0,w.totalPages=((U=Y.pagination)==null?void 0:U.totalPages)||0):v.error(Y.message||"加载失败")}catch(j){console.error("加载游记列表失败:",j),v.error("加载失败，请稍后重试")}finally{O.value=!1}},y=()=>{w.page=1,A()},p=()=>{w.page=1,A()},S=_=>{w.page=_,A()},T=_=>{w.pageSize=_,w.page=1,A()},F=()=>{if(!D.isLoggedIn){v.warning("请先登录");return}q.value=null,C.value=!0},J=_=>{q.value=_,C.value=!0},G=_=>{C.value=!1,q.value=null,_.journalId?(x("journalCreated",_),v.success("游记发布成功")):(x("journalUpdated",_),v.success("游记更新成功")),A()},z=()=>{C.value=!1,q.value=null},oe=({journalId:_,isLiked:g})=>{const U=M.value.find(j=>j.id===_);U&&(U.is_liked=g,U.like_count+=g?1:-1)},$=_=>{x("journalClick",_)},o=_=>{M.value=M.value.filter(g=>g.id!==_),I.value--,w.total--,x("journalDeleted",_)},R=({journalId:_,isPublic:g})=>{const U=M.value.find(j=>j.id===_);U&&(U.is_public=g)},K=_=>{x("journalClick",_)};return W({refresh:A}),(_,g)=>{const U=h("el-button"),j=h("el-option"),Z=h("el-select"),le=h("el-skeleton"),Y=h("el-pagination"),ne=h("el-dialog");return r(),c("div",Rt,[d.showHeader?(r(),c("div",Yt,[e("div",qt,[e("h2",null,f(d.title),1),I.value>0?(r(),c("span",Nt,"共"+f(I.value)+"篇游记",1)):B("",!0)]),e("div",Kt,[d.showCreateButton?(r(),E(U,{key:0,type:"primary",onClick:F,icon:b(Ce)},{default:l(()=>g[5]||(g[5]=[V(" 写游记 ")])),_:1},8,["icon"])):B("",!0),e("div",Wt,[s(Z,{modelValue:L.value,"onUpdate:modelValue":g[0]||(g[0]=u=>L.value=u),placeholder:"排序方式",onChange:y,style:{width:"120px"}},{default:l(()=>[s(j,{label:"最新发布",value:"created_at"}),s(j,{label:"最多点赞",value:"like_count"}),s(j,{label:"最多浏览",value:"view_count"})]),_:1},8,["modelValue"]),d.showPublicFilter?(r(),E(Z,{key:0,modelValue:a.value,"onUpdate:modelValue":g[1]||(g[1]=u=>a.value=u),placeholder:"公开状态",onChange:p,style:{width:"100px"}},{default:l(()=>[s(j,{label:"全部",value:""}),s(j,{label:"公开",value:"public"}),s(j,{label:"私密",value:"private"})]),_:1},8,["modelValue"])):B("",!0)])])])):B("",!0),e("div",Zt,[O.value?(r(),c("div",Xt,[(r(),c(Q,null,te(3,u=>s(le,{key:u,rows:4,animated:"",class:"skeleton-item"})),64))])):M.value.length===0?(r(),c("div",Qt,[g[7]||(g[7]=e("div",{class:"empty-icon"},"📝",-1)),e("div",es,f(d.emptyText),1),d.showCreateButton?(r(),E(U,{key:0,type:"primary",onClick:F,class:"empty-action"},{default:l(()=>g[6]||(g[6]=[V(" 写下第一篇游记 ")])),_:1})):B("",!0)])):(r(),c("div",ts,[(r(!0),c(Q,null,te(M.value,u=>(r(),E(ct,{key:u.id,journal:u,"show-actions":d.showActions,"show-like":d.showLike,"show-privacy":d.showPrivacy,onLike:oe,onComment:$,onEdit:J,onDelete:o,onTogglePublic:R,onClick:K},null,8,["journal","show-actions","show-like","show-privacy"]))),128))])),d.showPagination&&w.totalPages>1?(r(),c("div",ss,[s(Y,{"current-page":w.page,"onUpdate:currentPage":g[2]||(g[2]=u=>w.page=u),"page-size":w.pageSize,"onUpdate:pageSize":g[3]||(g[3]=u=>w.pageSize=u),"page-sizes":[10,20,50],total:w.total,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:S,onSizeChange:T},null,8,["current-page","page-size","total"])])):B("",!0)]),s(ne,{modelValue:C.value,"onUpdate:modelValue":g[4]||(g[4]=u=>C.value=u),title:q.value?"编辑游记":"写游记",width:"90%","close-on-click-modal":!1,"close-on-press-escape":!1},{default:l(()=>[s(Ot,{journal:q.value,onSave:G,onCancel:z},null,8,["journal"])]),_:1},8,["modelValue","title"])])}}},ls=me(os,[["__scopeId","data-v-12941539"]]),as={class:"user-profile"},ns={key:0,class:"login-prompt"},rs={class:"profile-card"},is={class:"profile-header"},us={class:"profile-info"},ds={class:"avatar-wrapper"},cs=["src","alt"],ms={class:"user-details"},vs={class:"user-name"},ps={class:"editable-field",style:{position:"relative",display:"flex","align-items":"center"}},fs={class:"user-signature"},gs={class:"editable-field",style:{position:"relative",display:"flex","align-items":"center"}},_s={class:"profile-content"},ys={class:"guides-container"},hs={class:"guides-sidebar"},ws={class:"sidebar-header"},ks={class:"guide-count"},bs={class:"guides-list"},$s=["onClick"],js={class:"guide-item-header"},Cs={class:"guide-days"},Ps={class:"guide-item-meta"},Vs={class:"guide-date"},xs={key:0,class:"empty-guides"},Is={class:"guides-content"},Bs={key:0,class:"guide-detail"},Us={class:"guide-detail-header"},Ss={class:"guide-detail-meta"},Ds={class:"scroll-area guide-scroll-container"},Ls={key:0},Ts={class:"answer-area-container"},zs=["innerHTML"],Es={key:0,class:"vitepress-divider"},Ms={key:1,class:"no-plans"},As={key:1,class:"no-guide-selected"},Js={class:"journals-container"},Fs={class:"security-settings"},Gs={class:"card-header",style:{display:"flex","justify-content":"space-between","align-items":"center",width:"100%"}},Hs={class:"card-header",style:{display:"flex","justify-content":"space-between","align-items":"center",width:"100%"}},Os={class:"card-header",style:{display:"flex","justify-content":"space-between","align-items":"center",width:"100%"}},Rs={class:"card-header",style:{display:"flex","justify-content":"space-between","align-items":"center",width:"100%"}},Ys={class:"dialog-footer",style:{display:"flex","justify-content":"flex-end",gap:"12px"}},qs={__name:"UserProfile",setup(d){const W=Be(),i=ve(),{isLoggedIn:H}=Ee(i),x=P("guides"),D=P(!1),N=P(!1),O=P(!1),M="http://localhost:3999/api";ae({nickname:!1,signature:!1});const I=ae({nickname:!1,signature:!1}),L=P(null),a=P(null),C=ae({nickname:"",signature:""});(()=>{C.nickname=i.userInfo.nickname||"",C.signature=i.userInfo.signature||""})();const w=async n=>{I[n]=!0,await Ue();try{n==="nickname"&&L.value?L.value.focus():n==="signature"&&a.value&&a.value.focus()}catch(t){console.warn("无法聚焦到输入框:",t)}},A=async n=>{if(I[n]=!1,C[n]!==i.userInfo[n])try{await i.updateProfile({[n]:C[n]}),v.success({message:"更新成功",duration:1e3}),i.userInfo[n]=C[n],_("profile_update",{event_category:"user_profile",event_label:`update_${n}`,custom_parameters:{field_name:n,success:!0}})}catch{v.error({message:"更新失败",duration:1e3}),C[n]=i.userInfo[n],_("profile_update_failed",{event_category:"user_profile",event_label:`update_${n}_failed`,custom_parameters:{field_name:n,success:!1}})}};P({avatar:"",isVerified:!1,guideCount:0,followers:0,following:0,phone:"",email:""});const y=P({currentPassword:"",newPassword:"",confirmPassword:""}),p=P(!1),S=P(null),T=n=>{p.value||(S.value.validateField(n),n==="newPassword"&&S.value.validateField("confirmPassword"))},F=ae({currentPassword:[{required:!0,message:"当前密码不能为空",trigger:["blur","change"]}],newPassword:[{required:!0,pattern:/^(?=.*[a-zA-Z])(?=.*\d)[\x21-\x7e]{6,20}$/,message:"需6-20位, 必须包含字母和数字",trigger:["blur","input"]},{validator:(n,t,k)=>{t===y.value.currentPassword?k(new Error("新密码不能与当前密码相同")):k()},trigger:["input","blur"]}],confirmPassword:[{validator:(n,t,k)=>{if(!t||!y.value.newPassword)return k();t!==y.value.newPassword?k(new Error("两次输入密码不一致")):k()},trigger:["input","blur"]}]}),J=P([]),G=P(-1),z=ce(()=>G.value>=0?J.value[G.value]:null),oe=async()=>{var n;try{if(!((n=i.userInfo)!=null&&n.account))return;const t=await i.getUserGuides();J.value=t,t.length>0&&(G.value=0)}catch(t){v.error({message:"获取攻略失败: "+t.message,duration:2e3})}},$=n=>{G.value=n},o=n=>n.startLocation&&n.endLocation&&n.totalDays?`从${n.startLocation}到${n.endLocation}的${n.totalDays}天攻略`:`攻略 - ${U(n.createTime)}`,R=(n,t)=>{var ue;if(!n)return"";let k=n;return k=k.replace(/^# Smart Travel Plan\s*\n*/m,""),k=k.replace(/!\[([^\]]*)\]\((https?:\/\/[^)]+)\)/g,(X,se,ee)=>ee.match(/\.(png|jpe?g|gif|webp|svg)(\?.*)?$/i)?X:`[${se}](${ee})`),(ue=i.userInfo)!=null&&ue.account&&(k=k.replace(/!\[([^\]]*)\]\(\.?\/?([^)]+)\)/g,(X,se,ee)=>{if(ee.startsWith("http"))return X;const re=`${M}/public/${i.userInfo.account}/${ee}`;return`![${se}](${re})`})),Ae.parse(k)},K=()=>{W.go("/")},_=(n,t={})=>{typeof window<"u"&&window.gtag&&(window.gtag("event",n,{event_category:"user_interaction",event_label:"user_profile",...t}),console.log("GA 用户中心事件:",n,t))};_e(()=>{i.isLoggedIn&&x.value==="guides"&&oe(),_("profile_page_view",{event_category:"navigation",event_label:"user_profile_visit"})}),$e(x,n=>{n==="guides"&&i.isLoggedIn&&J.value.length===0&&oe()});const g=async()=>{var n,t;try{await S.value.validate(),(await i.changePassword({currentPassword:y.value.currentPassword,newPassword:y.value.newPassword})).success&&(v.success({message:"密码更新成功",duration:1e3}),S.value.resetFields(),D.value=!1)}catch(k){console.error("密码修改失败:",k),v.error({message:((t=(n=k.response)==null?void 0:n.data)==null?void 0:t.message)||"密码修改失败",duration:1500})}},U=n=>new Date(n).toLocaleDateString(),j=()=>{W.go("/user-center/login")},Z=()=>{ye.confirm("确定要注销当前登录吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{i.logout(),i.clearStoredPassword(),W.go("/user-center/login"),v.success({message:"已成功注销",duration:2e3})}).catch(()=>{})},le=n=>{console.log("游记创建成功:",n),v.success("游记创建成功")},Y=n=>{console.log("游记更新成功:",n),v.success("游记更新成功")},ne=n=>{console.log("游记删除成功:",n),v.success("游记删除成功")},u=n=>{console.log("点击游记:",n)};return(n,t)=>{const k=h("el-button"),ue=h("el-alert"),X=h("el-input"),se=h("el-icon"),ee=h("el-empty"),re=h("el-tab-pane"),de=h("el-card"),Pe=h("el-tabs"),pe=h("el-form-item"),Ve=h("el-form"),xe=h("el-dialog");return r(),c("div",as,[b(i).isLoggedIn?(r(),c(Q,{key:1},[e("div",rs,[e("div",is,[t[27]||(t[27]=e("div",{class:"profile-cover"},[e("div",{class:"cover-overlay"})],-1)),e("div",us,[e("div",ds,[e("img",{src:b(i).userInfo.avatar||"/images/default-avatar.jpg",alt:b(i).userInfo.nickname,class:"avatar"},null,8,cs)]),e("div",ms,[e("div",vs,[e("div",ps,[I.nickname?(r(),E(X,{key:0,ref_key:"nicknameInput",ref:L,modelValue:C.nickname,"onUpdate:modelValue":t[0]||(t[0]=m=>C.nickname=m),size:"small",style:{width:"300px"},onBlur:t[1]||(t[1]=m=>A("nickname"))},null,8,["modelValue"])):(r(),c("h1",{key:1,style:{flex:"1",cursor:"pointer",margin:"0","white-space":"nowrap"},onClick:t[2]||(t[2]=m=>w("nickname"))},f(b(i).userInfo.nickname||"未设置昵称"),1)),we(s(se,{class:"edit-icon",style:{"margin-left":"12px",color:"#666"},onClick:t[3]||(t[3]=m=>w("nickname"))},{default:l(()=>[s(b(ge))]),_:1},512),[[ke,!I.nickname]])])]),e("div",fs,[e("div",gs,[I.signature?(r(),E(X,{key:0,ref_key:"signatureInput",ref:a,modelValue:C.signature,"onUpdate:modelValue":t[4]||(t[4]=m=>C.signature=m),size:"small",style:{width:"300px"},onBlur:t[5]||(t[5]=m=>A("signature"))},null,8,["modelValue"])):(r(),c("p",{key:1,style:{flex:"1",cursor:"pointer",margin:"0","white-space":"nowrap"},onClick:t[6]||(t[6]=m=>w("signature"))},f(b(i).userInfo.signature||"这个人很懒，什么都没写~"),1)),we(s(se,{class:"edit-icon",style:{"margin-left":"12px",color:"#666"},onClick:t[7]||(t[7]=m=>w("signature"))},{default:l(()=>[s(b(ge))]),_:1},512),[[ke,!I.signature]])])])])])])]),e("div",_s,[s(Pe,{modelValue:x.value,"onUpdate:modelValue":t[11]||(t[11]=m=>x.value=m),class:"profile-tabs"},{default:l(()=>[s(re,{label:"我的攻略",name:"guides"},{default:l(()=>[e("div",ys,[e("div",hs,[e("div",ws,[t[28]||(t[28]=e("h3",null,"我的攻略",-1)),e("span",ks,f(J.value.length)+" 个攻略",1)]),e("div",bs,[(r(!0),c(Q,null,te(J.value,(m,ie)=>(r(),c("div",{key:m.createTime,class:be(["guide-item",{active:G.value===ie}]),onClick:Ns=>$(ie)},[e("div",js,[e("h4",null,f(o(m)),1),e("span",Cs,f(m.totalDays)+"天",1)]),e("div",Ps,[e("span",Vs,[s(se,null,{default:l(()=>[s(b(Me))]),_:1}),V(" "+f(U(m.createTime)),1)])])],10,$s))),128)),J.value.length===0?(r(),c("div",xs,[s(ee,{description:"暂无攻略记录"},{default:l(()=>[s(k,{type:"primary",onClick:K},{default:l(()=>t[29]||(t[29]=[V("去制作攻略")])),_:1})]),_:1})])):B("",!0)])]),e("div",Is,[z.value?(r(),c("div",Bs,[e("div",Us,[e("h2",null,f(o(z.value)),1),e("div",Ss,[e("span",null,"创建时间："+f(U(z.value.createTime)),1),e("span",null,"总天数："+f(z.value.totalDays)+"天",1)])]),e("div",Ds,[z.value.plans&&z.value.plans.length>0?(r(),c("div",Ls,[(r(!0),c(Q,null,te(z.value.plans,(m,ie)=>(r(),c("div",{key:ie},[e("div",Ts,[e("div",{class:"answer-area",innerHTML:R(m.content,z.value)},null,8,zs)]),ie<z.value.plans.length-1?(r(),c("hr",Es)):B("",!0)]))),128))])):(r(),c("div",Ms,t[30]||(t[30]=[e("p",null,"该攻略暂无内容",-1)])))])])):(r(),c("div",As,[s(ee,{description:"请选择一个攻略查看详情"})]))])])]),_:1}),s(re,{label:"我的游记",name:"journals"},{default:l(()=>[e("div",Js,[s(ls,{title:"我的游记","api-url":"/journals/my","show-create-button":!0,"show-actions":!0,"show-like":!1,"show-privacy":!0,"show-public-filter":!0,"empty-text":"还没有写过游记，快来分享你的旅行体验吧！",onJournalCreated:le,onJournalUpdated:Y,onJournalDeleted:ne,onJournalClick:u})])]),_:1}),s(re,{label:"账号安全",name:"security"},{default:l(()=>[e("div",Fs,[s(de,{class:"security-card"},{header:l(()=>[e("div",Gs,[t[32]||(t[32]=e("span",null,"修改密码",-1)),s(k,{type:"primary",link:"",size:"small",onClick:t[8]||(t[8]=m=>D.value=!0)},{default:l(()=>t[31]||(t[31]=[V(" 修改 ")])),_:1})])]),default:l(()=>[t[33]||(t[33]=e("p",null,"定期修改密码可以保护账号安全",-1))]),_:1}),s(de,{class:"security-card"},{header:l(()=>[e("div",Hs,[t[34]||(t[34]=e("span",null,"手机绑定",-1)),s(k,{type:"primary",link:"",size:"small",onClick:t[9]||(t[9]=m=>N.value=!0)},{default:l(()=>[V(f(b(i).userInfo.phone?"修改":"绑定"),1)]),_:1})])]),default:l(()=>[e("p",null,f(b(i).userInfo.phone||"未绑定手机号"),1)]),_:1}),s(de,{class:"security-card"},{header:l(()=>[e("div",Os,[t[35]||(t[35]=e("span",null,"邮箱绑定",-1)),s(k,{type:"primary",link:"",size:"small",onClick:t[10]||(t[10]=m=>O.value=!0),class:"action-btn"},{default:l(()=>[V(f(b(i).userInfo.email?"修改":"绑定"),1)]),_:1})])]),default:l(()=>[e("p",null,f(b(i).userInfo.email||"未绑定邮箱"),1)]),_:1}),s(de,{class:"security-card"},{header:l(()=>[e("div",Rs,[t[37]||(t[37]=e("span",null,"登录状态",-1)),s(k,{type:"danger",link:"",size:"small",class:"action-btn",onClick:Z},{default:l(()=>t[36]||(t[36]=[V(" 立即注销 ")])),_:1})])]),default:l(()=>[e("p",null,"当前登录账号："+f(b(i).userInfo.account),1)]),_:1})])]),_:1})]),_:1},8,["modelValue"])])],64)):(r(),c("div",ns,[s(ue,{title:"请先登录账号",type:"warning",closable:!1,"show-icon":!1},{default:l(()=>[s(k,{type:"primary",onClick:j},{default:l(()=>t[26]||(t[26]=[V("去登录")])),_:1})]),_:1})])),s(xe,{modelValue:D.value,"onUpdate:modelValue":t[25]||(t[25]=m=>D.value=m),title:"修改密码",width:"400px"},{footer:l(()=>[e("span",Ys,[s(k,{size:"small",onClick:t[24]||(t[24]=m=>D.value=!1)},{default:l(()=>t[38]||(t[38]=[V("取消")])),_:1}),s(k,{type:"primary",size:"small",onClick:g},{default:l(()=>t[39]||(t[39]=[V("确认修改")])),_:1})])]),default:l(()=>[s(Ve,{ref_key:"passwordFormRef",ref:S,model:y.value,rules:F,"label-width":"100px"},{default:l(()=>[s(pe,{label:"当前密码",prop:"currentPassword"},{default:l(()=>[s(X,{modelValue:y.value.currentPassword,"onUpdate:modelValue":t[12]||(t[12]=m=>y.value.currentPassword=m),type:"password","show-password":"",onInput:t[13]||(t[13]=m=>T("currentPassword")),onCompositionstart:t[14]||(t[14]=m=>p.value=!0),onCompositionend:t[15]||(t[15]=m=>p.value=!1)},null,8,["modelValue"])]),_:1}),s(pe,{label:"新密码",prop:"newPassword"},{default:l(()=>[s(X,{modelValue:y.value.newPassword,"onUpdate:modelValue":t[16]||(t[16]=m=>y.value.newPassword=m),type:"password","show-password":"",onInput:t[17]||(t[17]=m=>T("newPassword")),onCompositionstart:t[18]||(t[18]=m=>p.value=!0),onCompositionend:t[19]||(t[19]=m=>p.value=!1)},null,8,["modelValue"])]),_:1}),s(pe,{label:"确认密码",prop:"confirmPassword"},{default:l(()=>[s(X,{modelValue:y.value.confirmPassword,"onUpdate:modelValue":t[20]||(t[20]=m=>y.value.confirmPassword=m),modelModifiers:{trim:!0},type:"password","show-password":"",onInput:t[21]||(t[21]=m=>T("confirmPassword")),onCompositionstart:t[22]||(t[22]=m=>p.value=!0),onCompositionend:t[23]||(t[23]=m=>p.value=!1)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}},Xs=me(qs,[["__scopeId","data-v-f3ca034d"]]);export{Xs as U};
