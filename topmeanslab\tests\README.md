# TopmeansLab 测试套件

本目录包含了 TopmeansLab 项目的完整测试套件，使用 Vitest 作为测试框架。

## 测试结构

```
tests/
├── setup.js                          # 测试环境设置
├── services/                         # 服务层单元测试
│   ├── TopmeansApiService.test.js    # API 服务测试
│   ├── TopmeansFormManager.test.js   # 表单管理测试
│   ├── TopmeansMapService.test.js    # 地图服务测试
│   ├── TopmeansMarkdownService.test.js # Markdown 服务测试
│   ├── TopmeansScrollManager.test.js # 滚动管理测试
│   └── TopmeansStyleManager.test.js  # 样式管理测试
├── integration/                      # 集成测试
│   └── services-integration.test.js  # 服务集成测试
├── components/                       # 组件测试（待添加）
├── backend/                          # 后端测试（待添加）
├── utils/                            # 工具函数测试（待添加）
└── performance/                      # 性能测试（待添加）
```

## 运行测试

### 安装依赖

```bash
npm install
```

### 运行所有测试

```bash
npm test
```

### 运行测试并生成覆盖率报告

```bash
npm run test:coverage
```

### 运行测试 UI

```bash
npm run test:ui
```

### 运行特定测试文件

```bash
npm test TopmeansApiService.test.js
```

## 测试覆盖范围

### 服务层测试

#### TopmeansApiService
- ✅ API 调用方法测试
- ✅ 错误处理测试
- ✅ 请求取消功能测试
- ✅ 连接检查测试

#### TopmeansFormManager
- ✅ 表单数据持久化测试
- ✅ 数据验证测试
- ✅ 数据合并测试
- ✅ 导入导出功能测试

#### TopmeansMapService
- ✅ 地图初始化测试
- ✅ 自动完成功能测试
- ✅ 地理编码测试
- ✅ 地图实例管理测试

#### TopmeansMarkdownService
- ✅ Markdown 解析测试
- ✅ 插件配置测试
- ✅ 自定义规则测试
- ✅ 错误处理测试

#### TopmeansScrollManager
- ✅ 滚动监听测试
- ✅ 用户滚动检测测试
- ✅ 自动滚动管理测试
- ✅ 状态管理测试

#### TopmeansStyleManager
- ✅ 样式注入测试
- ✅ 主题切换测试
- ✅ 样式监控测试
- ✅ 资源清理测试

### 集成测试

#### 服务集成测试
- ✅ 表单和 API 集成
- ✅ 地图和样式集成
- ✅ 滚动和内容集成
- ✅ 错误处理集成
- ✅ 数据持久化集成
- ✅ 性能测试
- ✅ 清理功能集成
- ✅ 真实场景测试

## 测试配置

### Vitest 配置

测试使用 `vitest.config.js` 进行配置：

- 使用 jsdom 环境模拟浏览器环境
- 配置路径别名
- 设置覆盖率报告
- 配置测试设置文件

### 测试设置

`tests/setup.js` 包含：

- 环境变量模拟
- DOM API 模拟
- 第三方库模拟
- 全局测试清理

## 测试最佳实践

### 1. 测试结构

每个测试文件遵循以下结构：

```javascript
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ServiceClass } from '@services/ServiceClass'

describe('ServiceClass', () => {
  let serviceInstance

  beforeEach(() => {
    // 设置测试环境
    serviceInstance = new ServiceClass()
  })

  afterEach(() => {
    // 清理测试环境
    vi.clearAllMocks()
  })

  describe('methodName', () => {
    it('should do something', () => {
      // 测试实现
    })
  })
})
```

### 2. Mock 策略

- 使用 `vi.fn()` 创建函数模拟
- 使用 `vi.spyOn()` 监视方法调用
- 使用 `vi.mock()` 模拟模块
- 使用 `vi.stubEnv()` 模拟环境变量

### 3. 异步测试

```javascript
it('should handle async operation', async () => {
  const result = await serviceInstance.asyncMethod()
  expect(result).toBe(expectedValue)
})
```

### 4. 错误测试

```javascript
it('should throw error when invalid input', async () => {
  await expect(serviceInstance.method(invalidInput))
    .rejects.toThrow('Expected error message')
})
```

## 覆盖率目标

- 语句覆盖率：> 90%
- 分支覆盖率：> 85%
- 函数覆盖率：> 95%
- 行覆盖率：> 90%

## 持续集成

测试配置支持 CI/CD 环境：

- 自动运行测试套件
- 生成覆盖率报告
- 上传测试结果
- 失败时阻止部署

## 故障排除

### 常见问题

1. **测试环境问题**
   ```bash
   # 清理缓存
   npm run clear-cache
   # 重新安装依赖
   rm -rf node_modules && npm install
   ```

2. **Mock 问题**
   - 确保在 `beforeEach` 中重置 mocks
   - 检查 mock 函数的调用次数和参数

3. **异步测试超时**
   - 增加测试超时时间
   - 检查异步操作是否正确等待

### 调试测试

```bash
# 运行单个测试文件并显示详细输出
npm test -- --reporter=verbose TopmeansApiService.test.js

# 运行测试并保持监听模式
npm test -- --watch
```

## 贡献指南

### 添加新测试

1. 在相应的测试目录中创建测试文件
2. 遵循现有的测试结构和命名约定
3. 确保测试覆盖所有主要功能
4. 添加适当的错误处理测试
5. 更新此文档

### 测试代码审查

- 确保测试清晰易懂
- 验证测试覆盖了所有边界情况
- 检查 mock 使用是否合理
- 确认测试不会产生副作用

## 性能测试

性能测试确保应用在各种负载下表现良好：

- 大量数据处理测试
- 并发操作测试
- 内存使用测试
- 响应时间测试

## 安全测试

安全测试验证应用的安全性：

- 输入验证测试
- XSS 防护测试
- CSRF 防护测试
- 数据加密测试

## 未来计划

- [ ] 添加端到端测试
- [ ] 添加可视化回归测试
- [ ] 添加负载测试
- [ ] 添加安全测试
- [ ] 添加无障碍测试 