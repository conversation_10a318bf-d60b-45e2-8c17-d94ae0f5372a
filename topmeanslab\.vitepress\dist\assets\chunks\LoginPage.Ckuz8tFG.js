import{_ as N,p as b,v as R,c,o as v,ab as L,j as a,e as p,n as h,ao as y,aJ as k,t as f,G as U,a as T,aD as B}from"./framework.oPHriSgN.js";import{V as j}from"./Valicode.B4wGwRkJ.js";import{_ as g,u as F,E as z}from"./theme.DE6uTiF9.js";const D={class:"login-page"},G={class:"login-container"},O={class:"auth-section"},J={class:"auth-container"},Z={class:"auth-tabs"},H={key:0,class:"auth-form"},K={class:"form-group"},Q={key:0,class:"error-message"},W={class:"form-group"},X={key:0,class:"error-message"},Y={class:"form-group"},ee={class:"captcha-container"},ae={class:"captcha-wrapper"},se={key:0,class:"error-message"},oe={class:"form-options"},te={class:"remember-me"},re={key:0,class:"error-message general-error"},le=["disabled"],ne={key:0,class:"loading-spinner"},de={key:1,class:"auth-form"},ie={class:"form-group"},ue={key:0,class:"error-message"},ce={class:"form-group"},ve={key:0,class:"error-message"},pe={class:"form-group"},me={key:0,class:"error-message"},fe={class:"form-group"},ge={class:"captcha-container"},we={class:"captcha-wrapper"},be={key:0,class:"error-message"},he={key:0,class:"error-message general-error"},ye=["disabled"],_e={key:0,class:"loading-spinner"},ke={__name:"LoginPage",setup(Pe){const x="http://localhost:3999/api";g.setLevel("info");const C=()=>{u.value={account:"",password:"",valicode:"",general:""},i.value={account:"",password:"",confirmPassword:"",valicode:"",general:""}},m=(t,e,s=!0)=>{var o;C(),t==="general"?u.value.general=e:(u.value[t]=e,s&&(t==="account"?l.value.account="":t==="password"?l.value.password="":t==="valicode"&&(l.value.valicode="",(o=S.value)==null||o.refresh()))),console.error(`🔴 登录错误 [${t}]:`,e)},d=(t,e,s=!0)=>{var o;C(),t==="general"?i.value.general=e:(i.value[t]=e,s&&(t==="account"?r.value.account="":t==="password"?r.value.password="":t==="confirmPassword"?r.value.confirmPassword="":t==="valicode"&&(r.value.valicode="",(o=S.value)==null||o.refresh()))),console.error(`🔴 注册错误 [${t}]:`,e)},V=t=>{console.log("🟢 成功:",t);try{z.success({message:t,duration:2e3,showClose:!0,center:!0})}catch{alert(t)}},_=b("login"),P=F(),w=b(!1);b(!1);const u=b({account:"",password:"",valicode:"",general:""}),i=b({account:"",password:"",confirmPassword:"",valicode:"",general:""}),E=t=>{$.value=t},l=b({account:"",password:"",valicode:"",remember:!1}),r=b({account:"",password:"",confirmPassword:"",valicode:"",nickname:"",avatar:"/images/default-avatar.jpg",signature:"这个人很懒，什么都没写~"}),S=b(null),$=b(""),M=async()=>{if(console.log("🚀 开始登录处理"),C(),!l.value.account){m("account","请输入账号");return}if(!l.value.password){m("password","请输入密码");return}if(!l.value.valicode){m("valicode","请输入验证码");return}if(l.value.valicode.toLowerCase()!==$.value.toLowerCase()){m("valicode","验证码错误，请重新输入");return}w.value=!0;let t=0;const e=2,s=async()=>{try{g.info("尝试登录:",{account:l.value.account,apiBase:x});const o=await fetch(`${x}/user/login`,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({account:l.value.account,password:l.value.password}),signal:AbortSignal.timeout(1e4)});if(g.info("登录响应状态:",o.status),!o.ok){const A=await o.text();throw g.error("登录响应错误:",A),new Error(`服务器响应错误 (${o.status}): ${o.statusText}`)}const n=await o.json();g.info("登录响应结果:",n),n.success?(console.log("✅ 登录成功"),l.value.remember?P.setStoredPassword(l.value.password):P.clearStoredPassword(),P.setToken(n.token),P.setUserInfo(n.user),l.value.remember?localStorage.setItem("rememberedAccount",l.value.account):localStorage.removeItem("rememberedAccount"),V("登录成功，正在跳转...","登录成功"),setTimeout(()=>{window.location.reload()},1e3)):(console.log("❌ 登录失败:",n),n.message?n.message.includes("账号")||n.message.includes("用户")?m("account","账号不存在或格式错误"):n.message.includes("密码")?m("password","密码错误，请检查后重试"):n.message.includes("验证码")?m("valicode","验证码错误，请重新输入"):m("general",n.message,!1):m("general","登录失败，请稍后重试",!1))}catch(o){t++,g.error(`登录尝试 ${t} 失败:`,o),console.error("🔥 登录请求异常:",o);let n="登录失败，请稍后重试";if(o.name==="AbortError"||o.message.includes("timeout")?n="请求超时，请检查网络连接后重试":o.message.includes("Failed to fetch")?n="网络连接失败，请检查网络设置或稍后重试":o.message.includes("Mixed Content")?n="网络安全限制，请联系管理员解决":o.message.includes("服务器响应错误")&&(n=o.message),t<=e)return m("general",`${n}，正在重试... (${t}/${e})`),await new Promise(A=>setTimeout(A,1e3)),s();m("general",n,!1)}};try{await s()}finally{w.value=!1}},q=async()=>{if(console.log("🚀 开始注册处理"),C(),!r.value.account){d("account","请输入账号");return}if(!r.value.password){d("password","请输入密码");return}if(r.value.account.length<3){d("account","账号长度至少需要3个字符");return}if(!/^[\u4e00-\u9fa5a-zA-Z0-9]+$/.test(r.value.account)){d("account","账号只能包含中文、英文字母和数字");return}if(r.value.password.length<6){d("password","密码长度至少需要6个字符");return}if(!/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/.test(r.value.password)){d("password","密码必须包含至少一个字母和一个数字，长度不少于6位");return}if(!r.value.confirmPassword){d("confirmPassword","请确认密码");return}if(r.value.password!==r.value.confirmPassword){d("confirmPassword","两次输入的密码不一致");return}if(!r.value.valicode){d("valicode","请输入验证码");return}if(r.value.valicode.toLowerCase()!==$.value.toLowerCase()){d("valicode","验证码错误，请重新输入");return}w.value=!0;try{g.info("尝试注册:",{account:r.value.account,apiBase:x});const s=await fetch(`${x}/user/register`,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(r.value),signal:AbortSignal.timeout(1e4)});if(g.info("注册响应状态:",s.status),!s.ok){const n=await s.text();throw g.error("注册响应错误:",n),new Error(`服务器响应错误 (${s.status}): ${s.statusText}`)}const o=await s.json();g.info("注册响应结果:",o),o.success?(console.log("✅ 注册成功"),V("注册成功！请使用新账号登录"),_.value="login",l.value.account=r.value.account,r.value={account:"",password:"",confirmPassword:"",valicode:"",nickname:"",avatar:"/images/default-avatar.jpg",signature:"这个人很懒，什么都没写~"}):(console.log("❌ 注册失败:",o),o.message?o.message.includes("账号")&&o.message.includes("存在")?d("account","该账号已存在，请更换账号"):o.message.includes("格式")?d("account","账号格式不正确，请使用中文、英文字母或数字"):o.message.includes("密码")?d("password","密码格式不符合要求，请确保包含字母和数字且长度不少于6位"):o.message.includes("重复")?d("account","该账号已被注册，请更换其他账号"):d("general",o.message,!1):d("general","注册失败，请稍后重试",!1))}catch(s){g.error("注册错误:",s),console.error("🔥 注册请求异常:",s);let o="注册失败，请稍后重试";s.name==="AbortError"||s.message.includes("timeout")?o="请求超时，请检查网络连接后重试":s.message.includes("Failed to fetch")?o="网络连接失败，请检查网络设置或稍后重试":s.message.includes("Mixed Content")?o="网络安全限制，请联系管理员解决":s.message.includes("服务器响应错误")&&(o=s.message),d("general",o,!1)}finally{w.value=!1}},I=t=>{t.preventDefault(),m("general","忘记密码功能暂未开放，请联系管理员重置密码")};return R(()=>{if(P.storedPassword&&localStorage.getItem("rememberedAccount")){const t=localStorage.getItem("rememberedAccount");l.value.account=t,l.value.password=P.storedPassword,l.value.remember=!0}}),(t,e)=>(v(),c("div",D,[e[21]||(e[21]=L('<div class="background-decoration" data-v-da08b21d><div class="floating-shapes" data-v-da08b21d><div class="shape shape-1" data-v-da08b21d></div><div class="shape shape-2" data-v-da08b21d></div><div class="shape shape-3" data-v-da08b21d></div><div class="shape shape-4" data-v-da08b21d></div><div class="shape shape-5" data-v-da08b21d></div></div></div>',1)),a("div",G,[e[20]||(e[20]=L('<div class="brand-section" data-v-da08b21d><div class="brand-content" data-v-da08b21d><h1 class="brand-title" data-v-da08b21d>旅美</h1><p class="brand-subtitle" data-v-da08b21d>一段旅途，一段人生，智慧出行，美好常伴</p><div class="brand-features" data-v-da08b21d><div class="feature-item" data-v-da08b21d><div class="feature-icon" data-v-da08b21d>🗺️</div><span data-v-da08b21d>智能路线规划</span></div><div class="feature-item" data-v-da08b21d><div class="feature-icon" data-v-da08b21d>🏨</div><span data-v-da08b21d>酒店推荐</span></div><div class="feature-item" data-v-da08b21d><div class="feature-icon" data-v-da08b21d>🍽️</div><span data-v-da08b21d>美食发现</span></div><div class="feature-item" data-v-da08b21d><div class="feature-icon" data-v-da08b21d>📸</div><span data-v-da08b21d>景点攻略</span></div></div></div></div>',1)),a("div",O,[a("div",J,[e[19]||(e[19]=a("div",{class:"auth-header"},[a("h2",null,"欢迎使用 TopMeansLab"),a("p",null,"请登录您的账户开始旅程规划")],-1)),a("div",Z,[a("button",{class:h(["tab-btn",{active:_.value==="login"}]),onClick:e[0]||(e[0]=s=>_.value="login")}," 登录 ",2),a("button",{class:h(["tab-btn",{active:_.value==="register"}]),onClick:e[1]||(e[1]=s=>_.value="register")}," 注册 ",2)]),_.value==="login"?(v(),c("div",H,[a("div",K,[e[10]||(e[10]=a("label",null,"账号",-1)),y(a("input",{"onUpdate:modelValue":e[2]||(e[2]=s=>l.value.account=s),type:"text",placeholder:"请输入手机号或邮箱",class:h(["form-input",{error:u.value.account}]),required:""},null,2),[[k,l.value.account]]),u.value.account?(v(),c("div",Q,f(u.value.account),1)):p("",!0)]),a("div",W,[e[11]||(e[11]=a("label",null,"密码",-1)),y(a("input",{"onUpdate:modelValue":e[3]||(e[3]=s=>l.value.password=s),type:"password",placeholder:"请输入密码",class:h(["form-input",{error:u.value.password}]),required:""},null,2),[[k,l.value.password]]),u.value.password?(v(),c("div",X,f(u.value.password),1)):p("",!0)]),a("div",Y,[e[12]||(e[12]=a("label",null,"验证码",-1)),a("div",ee,[y(a("input",{"onUpdate:modelValue":e[4]||(e[4]=s=>l.value.valicode=s),type:"text",placeholder:"请输入验证码",class:h(["form-input captcha-input",{error:u.value.valicode}]),required:""},null,2),[[k,l.value.valicode]]),a("div",ae,[U(j,{ref_key:"valicode",ref:S,onGetCode:E},null,512)])]),u.value.valicode?(v(),c("div",se,f(u.value.valicode),1)):p("",!0)]),a("div",oe,[a("label",te,[y(a("input",{type:"checkbox","onUpdate:modelValue":e[5]||(e[5]=s=>l.value.remember=s)},null,512),[[B,l.value.remember]]),e[13]||(e[13]=a("span",{class:"checkmark"},null,-1)),e[14]||(e[14]=T(" 记住密码 "))]),a("a",{href:"#",class:"forgot-password",onClick:I},"忘记密码？")]),u.value.general?(v(),c("div",re,f(u.value.general),1)):p("",!0),a("button",{onClick:M,class:"submit-btn",disabled:w.value},[w.value?(v(),c("span",ne)):p("",!0),T(" "+f(w.value?"登录中...":"登录"),1)],8,le)])):p("",!0),_.value==="register"?(v(),c("div",de,[a("div",ie,[e[15]||(e[15]=a("label",null,"账号",-1)),y(a("input",{"onUpdate:modelValue":e[6]||(e[6]=s=>r.value.account=s),type:"text",placeholder:"请输入你的账号名称",class:h(["form-input",{error:i.value.account}]),required:""},null,2),[[k,r.value.account]]),i.value.account?(v(),c("div",ue,f(i.value.account),1)):p("",!0)]),a("div",ce,[e[16]||(e[16]=a("label",null,"密码",-1)),y(a("input",{"onUpdate:modelValue":e[7]||(e[7]=s=>r.value.password=s),type:"password",placeholder:"请输入密码，由6位以上的英文和数字组成",class:h(["form-input",{error:i.value.password}]),required:""},null,2),[[k,r.value.password]]),i.value.password?(v(),c("div",ve,f(i.value.password),1)):p("",!0)]),a("div",pe,[e[17]||(e[17]=a("label",null,"确认密码",-1)),y(a("input",{"onUpdate:modelValue":e[8]||(e[8]=s=>r.value.confirmPassword=s),type:"password",placeholder:"请再次输入密码，确保两次密码一致",class:h(["form-input",{error:i.value.confirmPassword}]),required:""},null,2),[[k,r.value.confirmPassword]]),i.value.confirmPassword?(v(),c("div",me,f(i.value.confirmPassword),1)):p("",!0)]),a("div",fe,[e[18]||(e[18]=a("label",null,"验证码",-1)),a("div",ge,[y(a("input",{"onUpdate:modelValue":e[9]||(e[9]=s=>r.value.valicode=s),type:"text",placeholder:"请输入验证码",class:h(["form-input captcha-input",{error:i.value.valicode}]),required:""},null,2),[[k,r.value.valicode]]),a("div",we,[U(j,{ref_key:"valicode",ref:S,onGetCode:E},null,512)])]),i.value.valicode?(v(),c("div",be,f(i.value.valicode),1)):p("",!0)]),i.value.general?(v(),c("div",he,f(i.value.general),1)):p("",!0),a("button",{onClick:q,class:"submit-btn",disabled:w.value},[w.value?(v(),c("span",_e)):p("",!0),T(" "+f(w.value?"注册中...":"注册"),1)],8,ye)])):p("",!0)])])])]))}},$e=N(ke,[["__scopeId","data-v-da08b21d"]]);export{$e as default};
