import{S as e}from"./chunks/ServicePurchase.D8dfbI0D.js";import{c as t,o as a,G as r}from"./chunks/framework.oPHriSgN.js";import"./chunks/PaymentMethods.BgGK8a_4.js";import"./chunks/theme.DE6uTiF9.js";const _=JSON.parse('{"title":"","description":"","frontmatter":{"layout":"page"},"headers":[],"relativePath":"service-purchase/index.md","filePath":"service-purchase/index.md"}'),s={name:"service-purchase/index.md"},l=Object.assign(s,{setup(c){return(i,o)=>(a(),t("div",null,[r(e)]))}});export{_ as __pageData,l as default};
