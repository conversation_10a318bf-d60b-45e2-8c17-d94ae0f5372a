import{d as g,p as u,v as M,C as y,c as V,o as k,j as h,G as w,w as T,a as B,_ as N}from"./framework.oPHriSgN.js";const m=c;function c(l,s){const r=_();return c=function(n,x){return n=n-0,r[n]},c(l,s)}const S={class:m(0),style:{display:"flex"}},A={ref:"canvas",width:m(1),height:"40"},j={class:"valicode-btn"};function _(){const l=["captcha","100","clearRect","value","moveTo","querySelector"];return _=function(){return l},_()}const b="ABCDEFGHIJKLMNPQRSTUVWXYZabcdefghijmnopqrstuvwxyz023456789",q=g({__name:"Valicode",emits:["getCode"],setup(l,{expose:s,emit:r}){const n=r,x=u(null),e=u(null),d=u(""),p=b.length;function C(){return b.charAt(Math.floor(Math.random()*p))}function i(){const t=c;if(!e.value)return;e.value[t(2)](0,0,x[t(3)].width,x.value.height);let a=10;d.value="";for(let o=0;o<4;o++){const f=C();d.value+=f,e[t(3)].font="bold 20px Arial",e.value.fillStyle="#333",e.value.fillText(f,a,25),a+=20}for(let o=0;o<10;o++)e.value.strokeStyle="#ccc",e[t(3)].beginPath(),e.value[t(4)](Math.random()*100,Math.random()*40),e.value.lineTo(Math.random()*100,Math.random()*40),e.value.stroke();n("getCode",d.value.toLowerCase())}function v(){i()}return s({refresh:v}),M(()=>{var a;const t=c;x.value=document[t(5)]("canvas"),e.value=(a=x.value)==null?void 0:a.getContext("2d"),i()}),(t,a)=>{const o=y("el-button");return k(),V("div",S,[h("canvas",A,null,512),h("div",j,[w(o,{type:"text",class:"link-button",onClick:v},{default:T(()=>a[0]||(a[0]=[B("看不清，换一张")])),_:1})])])}}}),G=N(q,[["__scopeId","data-v-aa6c3bb5"]]);export{G as V};
