// 本文件提供即梦AI相关服务
const crypto = require("crypto");
const { URL } = require("url");
const fetch = require("node-fetch");
const qs = require("querystring");
const logger = require('../log/logger');

const JM_IMG_URL = process.env.JIMENG_AI_IMG_URL;
const JM_IMG_ACTION = process.env.JIMENG_AI_IMG_ACTION;
const JM_IMG_VERSION = process.env.JIMENG_AI_IMG_VERSION;
const JM_IMG_ACCESS_ID = process.env.JIMENG_AI_ACCESS_ID;
const JM_IMG_ACCESS_KEY = process.env.JIMENG_AI_ACCESS_KEY;

function genSignedKey(prompt) {
    // 生成UTC时间
    const utcDate = getDateTimeNow();

    // 准备请求体
    const requestBody = JSON.stringify({
        req_key: `jimeng_high_aes_general_v21_L`,
        prompt: prompt,
        return_url: true
    });

    // 计算请求体的SHA256哈希值
    const bodySha256 = hash(requestBody);

    // 解析URL以获取主机名
    const urlObj = new URL(JM_IMG_URL);
    const host = urlObj.host;

    // 准备查询参数
    const query = {
        Action: JM_IMG_ACTION || 'CVProcess',
        Version: JM_IMG_VERSION || '2022-08-31'
    };

    // 准备签名参数
    const signParams = {
        headers: {
            'Content-Type': 'application/json',
            'Host': host,
            'X-Date': utcDate,
            'X-Content-Sha256': bodySha256
        },
        method: 'POST',
        pathName: urlObj.pathname,
        query: query,
        accessKeyId: JM_IMG_ACCESS_ID,
        secretAccessKey: JM_IMG_ACCESS_KEY,
        serviceName: 'cv',
        region: 'cn-north-1',
        bodySha: bodySha256
    };

    // 计算签名
    const authorization = sign(signParams);

    return { utcDate, requestBody, bodySha256, host, query, authorization };
}

async function genImg(req, res) {
    const { prompt } = req.body;

    if (!prompt) {
        logger.error(`缺少必要的参数:${req.body}`);
        return res.status(400).json({ error: '缺少必要的参数' });
    }

    try {
        // 计算签名
        const { utcDate, requestBody, bodySha256, host, query, authorization } = genSignedKey(prompt);

        logger.info(`prompt=${prompt}, authorization=${authorization}`);

        // 构建完整的URL，包含查询参数
        const fullUrl = `${JM_IMG_URL}?${qs.stringify(query)}`;

        const response = await fetch(fullUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Host': host,
                'X-Date': utcDate,
                'X-Content-Sha256': bodySha256,
                'Authorization': authorization
            },
            body: requestBody
        });

        // 检查响应状态
        if (!response.ok) {
            const errorText = await response.text();
            logger.error(`请求失败, 状态码: ${response.status}, 错误信息: ${errorText}`);
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }

        // 解析响应数据
        const data = await response.json();
        logger.info(`生成图片成功,URL:${data.data.image_urls}`);
        res.json({ success: true, url: data.data.image_urls });
    } catch (error) {
        logger.error(`生成图片失败:${req.body}, ${error.message}`);
        res.status(500).json({ error: '生成图片失败', details: error.message });
    }
}

/**
 * 不参与加签过程的 header key
 */
const HEADER_KEYS_TO_IGNORE = new Set([
    "authorization",
    "content-type",
    "content-length",
    "user-agent",
    "presigned-expires",
    "expect",
]);

// 注意：原doRequest函数已被移除，因为我们现在直接在genImg函数中计算签名

function sign(params) {
    const {
        headers = {},
        query = {},
        region = '',
        serviceName = '',
        method = '',
        pathName = '/',
        accessKeyId = '',
        secretAccessKey = '',
        needSignHeaderKeys = [],
        bodySha,
    } = params;

    const datetime = headers["X-Date"];
    const date = datetime.substring(0, 8); // YYYYMMDD
    // 创建正规化请求
    const [signedHeaders, canonicalHeaders] = getSignHeaders(headers, needSignHeaderKeys);
    const canonicalRequest = [
        method.toUpperCase(),
        pathName,
        queryParamsToString(query) || '',
        `${canonicalHeaders}\n`,
        signedHeaders,
        bodySha || hash(''),
    ].join('\n');
    const credentialScope = [date, region, serviceName, "request"].join('/');
    // 创建签名字符串
    const stringToSign = ["HMAC-SHA256", datetime, credentialScope, hash(canonicalRequest)].join('\n');
    // 计算签名
    const kDate = hmac(secretAccessKey, date);
    const kRegion = hmac(kDate, region);
    const kService = hmac(kRegion, serviceName);
    const kSigning = hmac(kService, "request");
    const signature = hmac(kSigning, stringToSign).toString('hex');

    return [
        "HMAC-SHA256",
        `Credential=${accessKeyId}/${credentialScope},`,
        `SignedHeaders=${signedHeaders},`,
        `Signature=${signature}`,
    ].join(' ');
}

function hmac(secret, s) {
    return crypto.createHmac('sha256', secret).update(s, 'utf8').digest();
}

function hash(s) {
    return crypto.createHash('sha256').update(s, 'utf8').digest('hex');
}

function queryParamsToString(params) {
    return Object.keys(params)
        .sort()
        .map((key) => {
            const val = params[key];
            if (typeof val === 'undefined' || val === null) {
                return undefined;
            }
            const escapedKey = uriEscape(key);
            if (!escapedKey) {
                return undefined;
            }
            if (Array.isArray(val)) {
                return `${escapedKey}=${val.map(uriEscape).sort().join(`&${escapedKey}=`)}`;
            }
            return `${escapedKey}=${uriEscape(val)}`;
        })
        .filter((v) => v)
        .join('&');
}

function getSignHeaders(originHeaders, needSignHeaders) {
    function trimHeaderValue(header) {
        return header.toString?.().trim().replace(/\s+/g, ' ') ?? '';
    }

    let h = Object.keys(originHeaders);
    // 根据 needSignHeaders 过滤
    if (Array.isArray(needSignHeaders)) {
        const needSignSet = new Set([...needSignHeaders, 'x-date', 'host'].map((k) => k.toLowerCase()));
        h = h.filter((k) => needSignSet.has(k.toLowerCase()));
    }
    // 根据 ignore headers 过滤
    h = h.filter((k) => !HEADER_KEYS_TO_IGNORE.has(k.toLowerCase()));
    const signedHeaderKeys = h
        .slice()
        .map((k) => k.toLowerCase())
        .sort()
        .join(';');
    const canonicalHeaders = h
        .sort((a, b) => (a.toLowerCase() < b.toLowerCase() ? -1 : 1))
        .map((k) => `${k.toLowerCase()}:${trimHeaderValue(originHeaders[k])}`)
        .join('\n');
    return [signedHeaderKeys, canonicalHeaders];
}

function uriEscape(str) {
    try {
        return encodeURIComponent(str)
            .replace(/[^A-Za-z0-9_.~\-%]+/g, (ch) => {
                return '%' + ch.charCodeAt(0).toString(16).toUpperCase();
            })
            .replace(/[*]/g, (ch) => `%${ch.charCodeAt(0).toString(16).toUpperCase()}`);
    } catch (e) {
        return '';
    }
}

function getDateTimeNow() {
    const now = new Date();
    return now.toISOString().replace(/[:-]|\.\d{3}/g, '');
}

module.exports = {
    genImg
};