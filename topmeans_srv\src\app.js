const express = require('express');
const https = require('https');
const http = require('http');
const fs = require('fs');
const apiRoutes = require('./routes/apiRoutes');
const rateLimiter = require('./middlewares/rateLimiter');
const path = require('path');
const logger = require('./log/logger');

const FRONTEND_URL = process.env.FRONTEND_URL;
const HTTP_PORT = process.env.HTTP_PORT;
const HTTPS_PORT = process.env.HTTPS_PORT;

const app = express();

// 动态设置 CORS
app.options('*', (req, res) => {
    res.setHeader('Access-Control-Allow-Origin', FRONTEND_URL);
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.sendStatus(200); // 返回 HTTP 200 状态码
});

// 增加请求体大小限制
app.use(express.json({ limit: '10mb' })); // 将限制设置为 10MB
app.use(express.urlencoded({ limit: '10mb', extended: true }));

// 中间件
app.use(express.json());
app.use(rateLimiter);

if (fs.existsSync(path.join('/', 'etc', 'nginx', 'ssl', 'topmeanslab.com.key')) && fs.existsSync(path.join('/', 'etc', 'nginx', 'ssl', 'topmeanslab.com.pem')) && fs.existsSync(path.join('/', 'etc', 'nginx', 'ssl', 'intermediate.pem'))) {
    const httpsOptions = {
        key: fs.readFileSync(path.join('/', 'etc', 'nginx', 'ssl', 'topmeanslab.com.key')),
        cert: fs.readFileSync(path.join('/', 'etc', 'nginx', 'ssl', 'topmeanslab.com.pem')),
        ca: fs.readFileSync(path.join('/', 'etc', 'nginx', 'ssl', 'intermediate.pem')),
    };

    const httpsServer = https.createServer(httpsOptions, app);
    const httpServer = http.createServer(app);

    app.use((req, res, next) => {
        res.setHeader('Access-Control-Allow-Origin', FRONTEND_URL);
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        res.setHeader('Access-Control-Allow-Credentials', 'true');
        next();
    });

    httpServer.listen(HTTP_PORT, () => {
        logger.info(`HTTP server is running on port ${HTTP_PORT}`);
    });

    httpsServer.listen(HTTPS_PORT, () => {
        logger.info(`HTTPS server is running on port ${HTTPS_PORT}`);
    });
} else {
    app.use((req, res, next) => {
        res.setHeader('Access-Control-Allow-Origin', FRONTEND_URL);
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        res.setHeader('Access-Control-Allow-Credentials', 'true');
        next();
    });

    app.listen(HTTPS_PORT, () => {
        logger.info(`topmeans服务已启动，监听 ${HTTPS_PORT} 端口中...`);
    });
}

// 路由
app.use('/api', apiRoutes);

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '..', 'public', 'uploads')));
// 添加public目录的静态文件服务，用于访问用户攻略图片
app.use('/api/public', express.static(path.join(__dirname, 'services', 'public')));
app.use('/images', express.static(path.join(__dirname, '..', 'public', 'images')));

module.exports = app;