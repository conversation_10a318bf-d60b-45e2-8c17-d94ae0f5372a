{"version": 3, "sources": ["../../../node_modules/markdown-it-attrs/utils.js", "../../../node_modules/markdown-it-attrs/patterns.js", "../../../node_modules/markdown-it-attrs/index.js"], "sourcesContent": ["/**\n * @typedef {import('.').Token} Token\n * @typedef {import('.').Options} Options\n * @typedef {import('.').AttributePair} AttributePair\n * @typedef {import('.').AllowedAttribute} AllowedAttribute\n * @typedef {import('.').DetectingStrRule} DetectingStrRule\n */\n/**\n * parse {.class #id key=val} strings\n * @param {string} str: string to parse\n * @param {number} start: where to start parsing (including {)\n * @param {Options} options\n * @returns {AttributePair[]}: [['key', 'val'], ['class', 'red']]\n */\nexports.getAttrs = function (str, start, options) {\n  // not tab, line feed, form feed, space, solidus, greater than sign, quotation mark, apostrophe and equals sign\n  const allowedKeyChars = /[^\\t\\n\\f />\"'=]/;\n  const pairSeparator = ' ';\n  const keySeparator = '=';\n  const classChar = '.';\n  const idChar = '#';\n\n  const attrs = [];\n  let key = '';\n  let value = '';\n  let parsingKey = true;\n  let valueInsideQuotes = false;\n\n  // read inside {}\n  // start + left delimiter length to avoid beginning {\n  // breaks when } is found or end of string\n  for (let i = start + options.leftDelimiter.length; i < str.length; i++) {\n    if (str.slice(i, i + options.rightDelimiter.length) === options.rightDelimiter) {\n      if (key !== '') { attrs.push([key, value]); }\n      break;\n    }\n    const char_ = str.charAt(i);\n\n    // switch to reading value if equal sign\n    if (char_ === keySeparator && parsingKey) {\n      parsingKey = false;\n      continue;\n    }\n\n    // {.class} {..css-module}\n    if (char_ === classChar && key === '') {\n      if (str.charAt(i + 1) === classChar) {\n        key = 'css-module';\n        i += 1;\n      } else {\n        key = 'class';\n      }\n      parsingKey = false;\n      continue;\n    }\n\n    // {#id}\n    if (char_ === idChar && key === '') {\n      key = 'id';\n      parsingKey = false;\n      continue;\n    }\n\n    // {value=\"inside quotes\"}\n    if (char_ === '\"' && value === '' && !valueInsideQuotes) {\n      valueInsideQuotes = true;\n      continue;\n    }\n    if (char_ === '\"' && valueInsideQuotes) {\n      valueInsideQuotes = false;\n      continue;\n    }\n\n    // read next key/value pair\n    if ((char_ === pairSeparator && !valueInsideQuotes)) {\n      if (key === '') {\n        // beginning or ending space: { .red } vs {.red}\n        continue;\n      }\n      attrs.push([key, value]);\n      key = '';\n      value = '';\n      parsingKey = true;\n      continue;\n    }\n\n    // continue if character not allowed\n    if (parsingKey && char_.search(allowedKeyChars) === -1) {\n      continue;\n    }\n\n    // no other conditions met; append to key/value\n    if (parsingKey) {\n      key += char_;\n      continue;\n    }\n    value += char_;\n  }\n\n  if (options.allowedAttributes && options.allowedAttributes.length) {\n    const allowedAttributes = options.allowedAttributes;\n\n    return attrs.filter(function (attrPair) {\n      const attr = attrPair[0];\n\n      /**\n       * @param {AllowedAttribute} allowedAttribute\n       */\n      function isAllowedAttribute (allowedAttribute) {\n        return (attr === allowedAttribute\n          || (allowedAttribute instanceof RegExp && allowedAttribute.test(attr))\n        );\n      }\n\n      return allowedAttributes.some(isAllowedAttribute);\n    });\n\n  }\n  return attrs;\n\n};\n\n/**\n * add attributes from [['key', 'val']] list\n * @param {AttributePair[]} attrs: [['key', 'val']]\n * @param {Token} token: which token to add attributes\n * @returns token\n */\nexports.addAttrs = function (attrs, token) {\n  for (let j = 0, l = attrs.length; j < l; ++j) {\n    const key = attrs[j][0];\n    if (key === 'class') {\n      token.attrJoin('class', attrs[j][1]);\n    } else if (key === 'css-module') {\n      token.attrJoin('css-module', attrs[j][1]);\n    } else {\n      token.attrPush(attrs[j]);\n    }\n  }\n  return token;\n};\n\n/**\n * Does string have properly formatted curly?\n *\n * start: '{.a} asdf'\n * end: 'asdf {.a}'\n * only: '{.a}'\n *\n * @param {'start'|'end'|'only'} where to expect {} curly. start, end or only.\n * @param {Options} options\n * @return {DetectingStrRule} Function which testes if string has curly.\n */\nexports.hasDelimiters = function (where, options) {\n\n  if (!where) {\n    throw new Error('Parameter `where` not passed. Should be \"start\", \"end\" or \"only\".');\n  }\n\n  /**\n   * @param {string} str\n   * @return {boolean}\n   */\n  return function (str) {\n    // we need minimum three chars, for example {b}\n    const minCurlyLength = options.leftDelimiter.length + 1 + options.rightDelimiter.length;\n    if (!str || typeof str !== 'string' || str.length < minCurlyLength) {\n      return false;\n    }\n\n    /**\n     * @param {string} curly\n     */\n    function validCurlyLength (curly) {\n      const isClass = curly.charAt(options.leftDelimiter.length) === '.';\n      const isId = curly.charAt(options.leftDelimiter.length) === '#';\n      return (isClass || isId)\n        ? curly.length >= (minCurlyLength + 1)\n        : curly.length >= minCurlyLength;\n    }\n\n    let start, end, slice, nextChar;\n    const rightDelimiterMinimumShift = minCurlyLength - options.rightDelimiter.length;\n    switch (where) {\n    case 'start':\n      // first char should be {, } found in char 2 or more\n      slice = str.slice(0, options.leftDelimiter.length);\n      start = slice === options.leftDelimiter ? 0 : -1;\n      end = start === -1 ? -1 : str.indexOf(options.rightDelimiter, rightDelimiterMinimumShift);\n      // check if next character is not one of the delimiters\n      nextChar = str.charAt(end + options.rightDelimiter.length);\n      if (nextChar && options.rightDelimiter.indexOf(nextChar) !== -1) {\n        end = -1;\n      }\n      break;\n\n    case 'end':\n      // last char should be }\n      start = str.lastIndexOf(options.leftDelimiter);\n      end = start === -1 ? -1 : str.indexOf(options.rightDelimiter, start + rightDelimiterMinimumShift);\n      end = end === str.length - options.rightDelimiter.length ? end : -1;\n      break;\n\n    case 'only':\n      // '{.a}'\n      slice = str.slice(0, options.leftDelimiter.length);\n      start = slice === options.leftDelimiter ? 0 : -1;\n      slice = str.slice(str.length - options.rightDelimiter.length);\n      end = slice === options.rightDelimiter ? str.length - options.rightDelimiter.length : -1;\n      break;\n\n    default:\n      throw new Error(`Unexpected case ${where}, expected 'start', 'end' or 'only'`);\n    }\n\n    return start !== -1 && end !== -1 && validCurlyLength(str.substring(start, end + options.rightDelimiter.length));\n  };\n};\n\n/**\n * Removes last curly from string.\n * @param {string} str\n * @param {Options} options\n */\nexports.removeDelimiter = function (str, options) {\n  const start = escapeRegExp(options.leftDelimiter);\n  const end = escapeRegExp(options.rightDelimiter);\n\n  const curly = new RegExp(\n    '[ \\\\n]?' + start + '[^' + start + end + ']+' + end + '$'\n  );\n  const pos = str.search(curly);\n\n  return pos !== -1 ? str.slice(0, pos) : str;\n};\n\n/**\n * Escapes special characters in string s such that the string\n * can be used in `new RegExp`. For example \"[\" becomes \"\\\\[\".\n *\n * @param {string} s Regex string.\n * @return {string} Escaped string.\n */\nfunction escapeRegExp (s) {\n  return s.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n}\nexports.escapeRegExp = escapeRegExp;\n\n/**\n * find corresponding opening block\n * @param {Token[]} tokens\n * @param {number} i\n */\nexports.getMatchingOpeningToken = function (tokens, i) {\n  if (tokens[i].type === 'softbreak') {\n    return false;\n  }\n  // non closing blocks, example img\n  if (tokens[i].nesting === 0) {\n    return tokens[i];\n  }\n\n  const level = tokens[i].level;\n  const type = tokens[i].type.replace('_close', '_open');\n\n  for (; i >= 0; --i) {\n    if (tokens[i].type === type && tokens[i].level === level) {\n      return tokens[i];\n    }\n  }\n\n  return false;\n};\n\n\n/**\n * from https://github.com/markdown-it/markdown-it/blob/master/lib/common/utils.js\n */\nconst HTML_ESCAPE_TEST_RE = /[&<>\"]/;\nconst HTML_ESCAPE_REPLACE_RE = /[&<>\"]/g;\nconst HTML_REPLACEMENTS = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;'\n};\n\n/**\n * @param {string} ch\n * @returns {string}\n */\nfunction replaceUnsafeChar(ch) {\n  return HTML_REPLACEMENTS[ch];\n}\n\n/**\n * @param {string} str\n * @returns {string}\n */\nexports.escapeHtml = function (str) {\n  if (HTML_ESCAPE_TEST_RE.test(str)) {\n    return str.replace(HTML_ESCAPE_REPLACE_RE, replaceUnsafeChar);\n  }\n  return str;\n};\n", "'use strict';\n/**\n * If a pattern matches the token stream,\n * then run transform.\n */\n\nconst utils = require('./utils.js');\n\n/**\n * @param {import('.').Options} options\n * @returns {import('.').CurlyAttrsPattern[]}\n */\nmodule.exports = options => {\n  const __hr = new RegExp('^ {0,3}[-*_]{3,} ?'\n                          + utils.escapeRegExp(options.leftDelimiter)\n                          + '[^' + utils.escapeRegExp(options.rightDelimiter) + ']');\n\n  return ([\n    {\n      /**\n       * ```python {.cls}\n       * for i in range(10):\n       *     print(i)\n       * ```\n       */\n      name: 'fenced code blocks',\n      tests: [\n        {\n          shift: 0,\n          block: true,\n          info: utils.hasDelimiters('end', options)\n        }\n      ],\n      transform: (tokens, i) => {\n        const token = tokens[i];\n        const start = token.info.lastIndexOf(options.leftDelimiter);\n        const attrs = utils.getAttrs(token.info, start, options);\n        utils.addAttrs(attrs, token);\n        token.info = utils.removeDelimiter(token.info, options);\n      }\n    }, {\n      /**\n       * bla `click()`{.c} ![](img.png){.d}\n       *\n       * differs from 'inline attributes' as it does\n       * not have a closing tag (nesting: -1)\n       */\n      name: 'inline nesting 0',\n      tests: [\n        {\n          shift: 0,\n          type: 'inline',\n          children: [\n            {\n              shift: -1,\n              type: (str) => str === 'image' || str === 'code_inline'\n            }, {\n              shift: 0,\n              type: 'text',\n              content: utils.hasDelimiters('start', options)\n            }\n          ]\n        }\n      ],\n      /**\n       * @param {!number} j\n       */\n      transform: (tokens, i, j) => {\n        const token = tokens[i].children[j];\n        const endChar = token.content.indexOf(options.rightDelimiter);\n        const attrToken = tokens[i].children[j - 1];\n        const attrs = utils.getAttrs(token.content, 0, options);\n        utils.addAttrs(attrs, attrToken);\n        if (token.content.length === (endChar + options.rightDelimiter.length)) {\n          tokens[i].children.splice(j, 1);\n        } else {\n          token.content = token.content.slice(endChar + options.rightDelimiter.length);\n        }\n      }\n    }, {\n      /**\n       * | h1 |\n       * | -- |\n       * | c1 |\n       *\n       * {.c}\n       */\n      name: 'tables',\n      tests: [\n        {\n          // let this token be i, such that for-loop continues at\n          // next token after tokens.splice\n          shift: 0,\n          type: 'table_close'\n        }, {\n          shift: 1,\n          type: 'paragraph_open'\n        }, {\n          shift: 2,\n          type: 'inline',\n          content: utils.hasDelimiters('only', options)\n        }\n      ],\n      transform: (tokens, i) => {\n        const token = tokens[i + 2];\n        const tableOpen = utils.getMatchingOpeningToken(tokens, i);\n        const attrs = utils.getAttrs(token.content, 0, options);\n        // add attributes\n        utils.addAttrs(attrs, tableOpen);\n        // remove <p>{.c}</p>\n        tokens.splice(i + 1, 3);\n      }\n    }, {\n      /**\n       * | A | B |\n       * | -- | -- |\n       * | 1 | 2 |\n       *\n       * | C | D |\n       * | -- | -- |\n       *\n       * only `| A | B |` sets the colsnum metadata\n       */\n      name: 'tables thead metadata',\n      tests: [\n        {\n          shift: 0,\n          type: 'tr_close',\n        }, {\n          shift: 1,\n          type: 'thead_close'\n        }, {\n          shift: 2,\n          type: 'tbody_open'\n        }\n      ],\n      transform: (tokens, i) => {\n        const tr = utils.getMatchingOpeningToken(tokens, i);\n        const th = tokens[i - 1];\n        let colsnum = 0;\n        let n = i;\n        while (--n) {\n          if (tokens[n] === tr) {\n            tokens[n - 1].meta = Object.assign({}, tokens[n + 2].meta, { colsnum });\n            break;\n          }\n          colsnum += (tokens[n].level === th.level && tokens[n].type === th.type) >> 0;\n        }\n        tokens[i + 2].meta = Object.assign({}, tokens[i + 2].meta, { colsnum });\n      }\n    }, {\n      /**\n       * | A | B | C | D |\n       * | -- | -- | -- | -- |\n       * | 1 | 11 | 111 | 1111 {rowspan=3} |\n       * | 2 {colspan=2 rowspan=2} | 22 | 222 | 2222 |\n       * | 3 | 33 | 333 | 3333 |\n       */\n      name: 'tables tbody calculate',\n      tests: [\n        {\n          shift: 0,\n          type: 'tbody_close',\n          hidden: false\n        }\n      ],\n      /**\n       * @param {number} i index of the tbody ending\n       */\n      transform: (tokens, i) => {\n        /** index of the tbody beginning */\n        let idx = i - 2;\n        while (idx > 0 && 'tbody_open' !== tokens[--idx].type);\n\n        const calc = tokens[idx].meta.colsnum >> 0;\n        if (calc < 2) { return; }\n\n        const level = tokens[i].level + 2;\n        for (let n = idx; n < i; n++) {\n          if (tokens[n].level > level) { continue; }\n\n          const token = tokens[n];\n          const rows = token.hidden ? 0 : token.attrGet('rowspan') >> 0;\n          const cols = token.hidden ? 0 : token.attrGet('colspan') >> 0;\n\n          if (rows > 1) {\n            let colsnum = calc - (cols > 0 ? cols : 1);\n            for (let k = n, num = rows; k < i, num > 1; k++) {\n              if ('tr_open' == tokens[k].type) {\n                tokens[k].meta = Object.assign({}, tokens[k].meta);\n                if (tokens[k].meta && tokens[k].meta.colsnum) {\n                  colsnum -= 1;\n                }\n                tokens[k].meta.colsnum = colsnum;\n                num--;\n              }\n            }\n          }\n\n          if ('tr_open' == token.type && token.meta && token.meta.colsnum) {\n            const max = token.meta.colsnum;\n            for (let k = n, num = 0; k < i; k++) {\n              if ('td_open' == tokens[k].type) {\n                num += 1;\n              } else if ('tr_close' == tokens[k].type) {\n                break;\n              }\n              num > max && (tokens[k].hidden || hidden(tokens[k]));\n            }\n          }\n\n          if (cols > 1) {\n            /** @type {number[]} index of one row's children */\n            const one = [];\n            /** last index of the row's children */\n            let end = n + 3;\n            /** number of the row's children */\n            let num = calc;\n\n            for (let k = n; k > idx; k--) {\n              if ('tr_open' == tokens[k].type) {\n                num = tokens[k].meta && tokens[k].meta.colsnum || num;\n                break;\n              } else if ('td_open' === tokens[k].type) {\n                one.unshift(k);\n              }\n            }\n\n            for (let k = n + 2; k < i; k++) {\n              if ('tr_close' == tokens[k].type) {\n                end = k;\n                break;\n              } else if ('td_open' == tokens[k].type) {\n                one.push(k);\n              }\n            }\n\n            const off = one.indexOf(n);\n            let real = num - off;\n            real = real > cols ? cols : real;\n            cols > real && token.attrSet('colspan', real + '');\n\n            for (let k = one.slice(num + 1 - calc - real)[0]; k < end; k++) {\n              tokens[k].hidden || hidden(tokens[k]);\n            }\n          }\n        }\n      }\n    }, {\n      /**\n       * *emphasis*{.with attrs=1}\n       */\n      name: 'inline attributes',\n      tests: [\n        {\n          shift: 0,\n          type: 'inline',\n          children: [\n            {\n              shift: -1,\n              nesting: -1  // closing inline tag, </em>{.a}\n            }, {\n              shift: 0,\n              type: 'text',\n              content: utils.hasDelimiters('start', options)\n            }\n          ]\n        }\n      ],\n      /**\n       * @param {!number} j\n       */\n      transform: (tokens, i, j) => {\n        const token = tokens[i].children[j];\n        const content = token.content;\n        const attrs = utils.getAttrs(content, 0, options);\n        const openingToken = utils.getMatchingOpeningToken(tokens[i].children, j - 1);\n        utils.addAttrs(attrs, openingToken);\n        token.content = content.slice(content.indexOf(options.rightDelimiter) + options.rightDelimiter.length);\n      }\n    }, {\n      /**\n       * - item\n       * {.a}\n       */\n      name: 'list softbreak',\n      tests: [\n        {\n          shift: -2,\n          type: 'list_item_open'\n        }, {\n          shift: 0,\n          type: 'inline',\n          children: [\n            {\n              position: -2,\n              type: 'softbreak'\n            }, {\n              position: -1,\n              type: 'text',\n              content: utils.hasDelimiters('only', options)\n            }\n          ]\n        }\n      ],\n      /**\n       * @param {!number} j\n       */\n      transform: (tokens, i, j) => {\n        const token = tokens[i].children[j];\n        const content = token.content;\n        const attrs = utils.getAttrs(content, 0, options);\n        let ii = i - 2;\n        while (tokens[ii - 1] &&\n          tokens[ii - 1].type !== 'ordered_list_open' &&\n          tokens[ii - 1].type !== 'bullet_list_open') { ii--; }\n        utils.addAttrs(attrs, tokens[ii - 1]);\n        tokens[i].children = tokens[i].children.slice(0, -2);\n      }\n    }, {\n      /**\n       * - nested list\n       *   - with double \\n\n       *   {.a} <-- apply to nested ul\n       *\n       * {.b} <-- apply to root <ul>\n       */\n      name: 'list double softbreak',\n      tests: [\n        {\n          // let this token be i = 0 so that we can erase\n          // the <p>{.a}</p> tokens below\n          shift: 0,\n          type: (str) =>\n            str === 'bullet_list_close' ||\n            str === 'ordered_list_close'\n        }, {\n          shift: 1,\n          type: 'paragraph_open'\n        }, {\n          shift: 2,\n          type: 'inline',\n          content: utils.hasDelimiters('only', options),\n          children: (arr) => arr.length === 1\n        }, {\n          shift: 3,\n          type: 'paragraph_close'\n        }\n      ],\n      transform: (tokens, i) => {\n        const token = tokens[i + 2];\n        const content = token.content;\n        const attrs = utils.getAttrs(content, 0, options);\n        const openingToken = utils.getMatchingOpeningToken(tokens, i);\n        utils.addAttrs(attrs, openingToken);\n        tokens.splice(i + 1, 3);\n      }\n    }, {\n      /**\n       * - end of {.list-item}\n       */\n      name: 'list item end',\n      tests: [\n        {\n          shift: -2,\n          type: 'list_item_open'\n        }, {\n          shift: 0,\n          type: 'inline',\n          children: [\n            {\n              position: -1,\n              type: 'text',\n              content: utils.hasDelimiters('end', options)\n            }\n          ]\n        }\n      ],\n      /**\n       * @param {!number} j\n       */\n      transform: (tokens, i, j) => {\n        const token = tokens[i].children[j];\n        const content = token.content;\n        const attrs = utils.getAttrs(content, content.lastIndexOf(options.leftDelimiter), options);\n        utils.addAttrs(attrs, tokens[i - 2]);\n        const trimmed = content.slice(0, content.lastIndexOf(options.leftDelimiter));\n        token.content = last(trimmed) !== ' ' ?\n          trimmed : trimmed.slice(0, -1);\n      }\n    }, {\n      /**\n       * something with softbreak\n       * {.cls}\n       */\n      name: '\\n{.a} softbreak then curly in start',\n      tests: [\n        {\n          shift: 0,\n          type: 'inline',\n          children: [\n            {\n              position: -2,\n              type: 'softbreak'\n            }, {\n              position: -1,\n              type: 'text',\n              content: utils.hasDelimiters('only', options)\n            }\n          ]\n        }\n      ],\n      /**\n       * @param {!number} j\n       */\n      transform: (tokens, i, j) => {\n        const token = tokens[i].children[j];\n        const attrs = utils.getAttrs(token.content, 0, options);\n        // find last closing tag\n        let ii = i + 1;\n        while (tokens[ii + 1] && tokens[ii + 1].nesting === -1) { ii++; }\n        const openingToken = utils.getMatchingOpeningToken(tokens, ii);\n        utils.addAttrs(attrs, openingToken);\n        tokens[i].children = tokens[i].children.slice(0, -2);\n      }\n    }, {\n      /**\n       * horizontal rule --- {#id}\n       */\n      name: 'horizontal rule',\n      tests: [\n        {\n          shift: 0,\n          type: 'paragraph_open'\n        },\n        {\n          shift: 1,\n          type: 'inline',\n          children: (arr) => arr.length === 1,\n          content: (str) => str.match(__hr) !== null,\n        },\n        {\n          shift: 2,\n          type: 'paragraph_close'\n        }\n      ],\n      transform: (tokens, i) => {\n        const token = tokens[i];\n        token.type = 'hr';\n        token.tag = 'hr';\n        token.nesting = 0;\n        const content = tokens[i + 1].content;\n        const start = content.lastIndexOf(options.leftDelimiter);\n        const attrs = utils.getAttrs(content, start, options);\n        utils.addAttrs(attrs, token);\n        token.markup = content;\n        tokens.splice(i + 1, 2);\n      }\n    }, {\n      /**\n       * end of {.block}\n       */\n      name: 'end of block',\n      tests: [\n        {\n          shift: 0,\n          type: 'inline',\n          children: [\n            {\n              position: -1,\n              content: utils.hasDelimiters('end', options),\n              type: (t) => t !== 'code_inline' && t !== 'math_inline'\n            }\n          ]\n        }\n      ],\n      /**\n       * @param {!number} j\n       */\n      transform: (tokens, i, j) => {\n        const token = tokens[i].children[j];\n        const content = token.content;\n        const attrs = utils.getAttrs(content, content.lastIndexOf(options.leftDelimiter), options);\n        let ii = i + 1;\n        do if (tokens[ii] && tokens[ii].nesting === -1) { break; } while (ii++ < tokens.length);\n        const openingToken = utils.getMatchingOpeningToken(tokens, ii);\n        utils.addAttrs(attrs, openingToken);\n        const trimmed = content.slice(0, content.lastIndexOf(options.leftDelimiter));\n        token.content = last(trimmed) !== ' ' ?\n          trimmed : trimmed.slice(0, -1);\n      }\n    }\n  ]);\n};\n\n// get last element of array or string\nfunction last(arr) {\n  return arr.slice(-1)[0];\n}\n\n/**\n * Hidden table's cells and them inline children,\n * specially cast inline's content as empty\n * to prevent that escapes the table's box model\n * @see https://github.com/markdown-it/markdown-it/issues/639\n * @param {import('.').Token} token\n */\nfunction hidden(token) {\n  token.hidden = true;\n  token.children && token.children.forEach(t => (\n    t.content = '',\n    hidden(t),\n    undefined\n  ));\n}\n", "'use strict';\n\nconst patternsConfig = require('./patterns.js');\n\n/**\n * @typedef {import('markdown-it')} MarkdownIt\n *\n * @typedef {import('markdown-it/lib/rules_core/state_core.mjs').default} StateCore\n *\n * @typedef {import('markdown-it/lib/token.mjs').default} Token\n *\n * @typedef {import('markdown-it/lib/token.mjs').Nesting} Nesting\n *\n * @typedef {Object} Options\n * @property {!string} leftDelimiter left delimiter, default is `{`(left curly bracket)\n * @property {!string} rightDelimiter right delimiter, default is `}`(right curly bracket)\n * @property {AllowedAttribute[]} allowedAttributes empty means no limit\n *\n * @typedef {string|RegExp} AllowedAttribute rule of allowed attribute\n *\n * @typedef {[string, string]} AttributePair\n *\n * @typedef {[number, number]} SourceLineInfo\n *\n * @typedef {Object} CurlyAttrsPattern\n * @property {string} name\n * @property {DetectingRule[]} tests\n * @property {(tokens: Token[], i: number, j?: number) => void} transform\n *\n * @typedef {Object} MatchedResult\n * @property {boolean} match true means matched\n * @property {number?} j postion index number of Array<{@link Token}>\n *\n * @typedef {(str: string) => boolean} DetectingStrRule\n *\n * @typedef {Object} DetectingRule rule for testing {@link Token}'s properties\n * @property {number=} shift offset index number of Array<{@link Token}>\n * @property {number=} position fixed index number of Array<{@link Token}>\n * @property {(string | DetectingStrRule)=} type\n * @property {(string | DetectingStrRule)=} tag\n * @property {DetectingRule[]=} children\n * @property {(string | DetectingStrRule)=} content\n * @property {(string | DetectingStrRule)=} markup\n * @property {(string | DetectingStrRule)=} info\n * @property {Nesting=} nesting\n * @property {number=} level\n * @property {boolean=} block\n * @property {boolean=} hidden\n * @property {AttributePair[]=} attrs\n * @property {SourceLineInfo[]=} map\n * @property {any=} meta\n */\n\n/** @type {Options} */\nconst defaultOptions = {\n  leftDelimiter: '{',\n  rightDelimiter: '}',\n  allowedAttributes: []\n};\n\n/**\n * @param {MarkdownIt} md\n * @param {Options=} options_\n */\nmodule.exports = function attributes(md, options_) {\n  let options = Object.assign({}, defaultOptions);\n  options = Object.assign(options, options_);\n\n  const patterns = patternsConfig(options);\n\n  /**\n   * @param {StateCore} state\n   */\n  function curlyAttrs(state) {\n    const tokens = state.tokens;\n\n    for (let i = 0; i < tokens.length; i++) {\n      for (let p = 0; p < patterns.length; p++) {\n        const pattern = patterns[p];\n        let j = null; // position of child with offset 0\n        const match = pattern.tests.every(t => {\n          const res = test(tokens, i, t);\n          if (res.j !== null) { j = res.j; }\n          return res.match;\n        });\n        if (match) {\n          try {\n            pattern.transform(tokens, i, j);\n            if (pattern.name === 'inline attributes' || pattern.name === 'inline nesting 0') {\n              // retry, may be several inline attributes\n              p--;\n            }\n          } catch (error) {\n            // eslint-disable-next-line no-console\n            console.error(`markdown-it-attrs: Error in pattern '${pattern.name}': ${error.message}`);\n            console.error(error.stack);\n          }\n        }\n      }\n    }\n  }\n\n  md.core.ruler.before('linkify', 'curly_attributes', curlyAttrs);\n};\n\n/**\n * Test if t matches token stream.\n *\n * @param {Token[]} tokens\n * @param {number} i\n * @param {DetectingRule} t\n * @returns {MatchedResult}\n */\nfunction test(tokens, i, t) {\n  /** @type {MatchedResult} */\n  const res = {\n    match: false,\n    j: null  // position of child\n  };\n\n  const ii = t.shift !== undefined\n    ? i + t.shift\n    : t.position;\n\n  if (t.shift !== undefined && ii < 0) {\n    // we should never shift to negative indexes (rolling around to back of array)\n    return res;\n  }\n\n  const token = get(tokens, ii);  // supports negative ii\n\n\n  if (token === undefined) { return res; }\n\n  for (const key of Object.keys(t)) {\n    if (key === 'shift' || key === 'position') { continue; }\n\n    if (token[key] === undefined) { return res; }\n\n    if (key === 'children' && isArrayOfObjects(t.children)) {\n      if (token.children.length === 0) {\n        return res;\n      }\n      let match;\n      /** @type {DetectingRule[]} */\n      const childTests = t.children;\n      /** @type {Token[]} */\n      const children = token.children;\n      if (childTests.every(tt => tt.position !== undefined)) {\n        // positions instead of shifts, do not loop all children\n        match = childTests.every(tt => test(children, tt.position, tt).match);\n        if (match) {\n          // we may need position of child in transform\n          const j = last(childTests).position;\n          res.j = j >= 0 ? j : children.length + j;\n        }\n      } else {\n        for (let j = 0; j < children.length; j++) {\n          match = childTests.every(tt => test(children, j, tt).match);\n          if (match) {\n            res.j = j;\n            // all tests true, continue with next key of pattern t\n            break;\n          }\n        }\n      }\n\n      if (match === false) { return res; }\n\n      continue;\n    }\n\n    switch (typeof t[key]) {\n    case 'boolean':\n    case 'number':\n    case 'string':\n      if (token[key] !== t[key]) { return res; }\n      break;\n    case 'function':\n      if (!t[key](token[key])) { return res; }\n      break;\n    case 'object':\n      if (isArrayOfFunctions(t[key])) {\n        const r = t[key].every(tt => tt(token[key]));\n        if (r === false) { return res; }\n        break;\n      }\n    // fall through for objects !== arrays of functions\n    default:\n      throw new Error(`Unknown type of pattern test (key: ${key}). Test should be of type boolean, number, string, function or array of functions.`);\n    }\n  }\n\n  // no tests returned false -> all tests returns true\n  res.match = true;\n  return res;\n}\n\nfunction isArrayOfObjects(arr) {\n  return Array.isArray(arr) && arr.length && arr.every(i => typeof i === 'object');\n}\n\nfunction isArrayOfFunctions(arr) {\n  return Array.isArray(arr) && arr.length && arr.every(i => typeof i === 'function');\n}\n\n/**\n * Get n item of array. Supports negative n, where -1 is last\n * element in array.\n * @param {Token[]} arr\n * @param {number} n\n * @returns {Token=}\n */\nfunction get(arr, n) {\n  return n >= 0 ? arr[n] : arr[arr.length + n];\n}\n\n/**\n * get last element of array, safe - returns {} if not found\n * @param {DetectingRule[]} arr\n * @returns {DetectingRule}\n */\nfunction last(arr) {\n  return arr.slice(-1)[0] || {};\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAcA,YAAQ,WAAW,SAAU,KAAK,OAAO,SAAS;AAEhD,YAAM,kBAAkB;AACxB,YAAM,gBAAgB;AACtB,YAAM,eAAe;AACrB,YAAM,YAAY;AAClB,YAAM,SAAS;AAEf,YAAM,QAAQ,CAAC;AACf,UAAI,MAAM;AACV,UAAI,QAAQ;AACZ,UAAI,aAAa;AACjB,UAAI,oBAAoB;AAKxB,eAAS,IAAI,QAAQ,QAAQ,cAAc,QAAQ,IAAI,IAAI,QAAQ,KAAK;AACtE,YAAI,IAAI,MAAM,GAAG,IAAI,QAAQ,eAAe,MAAM,MAAM,QAAQ,gBAAgB;AAC9E,cAAI,QAAQ,IAAI;AAAE,kBAAM,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,UAAG;AAC5C;AAAA,QACF;AACA,cAAM,QAAQ,IAAI,OAAO,CAAC;AAG1B,YAAI,UAAU,gBAAgB,YAAY;AACxC,uBAAa;AACb;AAAA,QACF;AAGA,YAAI,UAAU,aAAa,QAAQ,IAAI;AACrC,cAAI,IAAI,OAAO,IAAI,CAAC,MAAM,WAAW;AACnC,kBAAM;AACN,iBAAK;AAAA,UACP,OAAO;AACL,kBAAM;AAAA,UACR;AACA,uBAAa;AACb;AAAA,QACF;AAGA,YAAI,UAAU,UAAU,QAAQ,IAAI;AAClC,gBAAM;AACN,uBAAa;AACb;AAAA,QACF;AAGA,YAAI,UAAU,OAAO,UAAU,MAAM,CAAC,mBAAmB;AACvD,8BAAoB;AACpB;AAAA,QACF;AACA,YAAI,UAAU,OAAO,mBAAmB;AACtC,8BAAoB;AACpB;AAAA,QACF;AAGA,YAAK,UAAU,iBAAiB,CAAC,mBAAoB;AACnD,cAAI,QAAQ,IAAI;AAEd;AAAA,UACF;AACA,gBAAM,KAAK,CAAC,KAAK,KAAK,CAAC;AACvB,gBAAM;AACN,kBAAQ;AACR,uBAAa;AACb;AAAA,QACF;AAGA,YAAI,cAAc,MAAM,OAAO,eAAe,MAAM,IAAI;AACtD;AAAA,QACF;AAGA,YAAI,YAAY;AACd,iBAAO;AACP;AAAA,QACF;AACA,iBAAS;AAAA,MACX;AAEA,UAAI,QAAQ,qBAAqB,QAAQ,kBAAkB,QAAQ;AACjE,cAAM,oBAAoB,QAAQ;AAElC,eAAO,MAAM,OAAO,SAAU,UAAU;AACtC,gBAAM,OAAO,SAAS,CAAC;AAKvB,mBAAS,mBAAoB,kBAAkB;AAC7C,mBAAQ,SAAS,oBACX,4BAA4B,UAAU,iBAAiB,KAAK,IAAI;AAAA,UAExE;AAEA,iBAAO,kBAAkB,KAAK,kBAAkB;AAAA,QAClD,CAAC;AAAA,MAEH;AACA,aAAO;AAAA,IAET;AAQA,YAAQ,WAAW,SAAU,OAAO,OAAO;AACzC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC5C,cAAM,MAAM,MAAM,CAAC,EAAE,CAAC;AACtB,YAAI,QAAQ,SAAS;AACnB,gBAAM,SAAS,SAAS,MAAM,CAAC,EAAE,CAAC,CAAC;AAAA,QACrC,WAAW,QAAQ,cAAc;AAC/B,gBAAM,SAAS,cAAc,MAAM,CAAC,EAAE,CAAC,CAAC;AAAA,QAC1C,OAAO;AACL,gBAAM,SAAS,MAAM,CAAC,CAAC;AAAA,QACzB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAaA,YAAQ,gBAAgB,SAAU,OAAO,SAAS;AAEhD,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAMA,aAAO,SAAU,KAAK;AAEpB,cAAM,iBAAiB,QAAQ,cAAc,SAAS,IAAI,QAAQ,eAAe;AACjF,YAAI,CAAC,OAAO,OAAO,QAAQ,YAAY,IAAI,SAAS,gBAAgB;AAClE,iBAAO;AAAA,QACT;AAKA,iBAAS,iBAAkB,OAAO;AAChC,gBAAM,UAAU,MAAM,OAAO,QAAQ,cAAc,MAAM,MAAM;AAC/D,gBAAM,OAAO,MAAM,OAAO,QAAQ,cAAc,MAAM,MAAM;AAC5D,iBAAQ,WAAW,OACf,MAAM,UAAW,iBAAiB,IAClC,MAAM,UAAU;AAAA,QACtB;AAEA,YAAI,OAAO,KAAK,OAAO;AACvB,cAAM,6BAA6B,iBAAiB,QAAQ,eAAe;AAC3E,gBAAQ,OAAO;AAAA,UACf,KAAK;AAEH,oBAAQ,IAAI,MAAM,GAAG,QAAQ,cAAc,MAAM;AACjD,oBAAQ,UAAU,QAAQ,gBAAgB,IAAI;AAC9C,kBAAM,UAAU,KAAK,KAAK,IAAI,QAAQ,QAAQ,gBAAgB,0BAA0B;AAExF,uBAAW,IAAI,OAAO,MAAM,QAAQ,eAAe,MAAM;AACzD,gBAAI,YAAY,QAAQ,eAAe,QAAQ,QAAQ,MAAM,IAAI;AAC/D,oBAAM;AAAA,YACR;AACA;AAAA,UAEF,KAAK;AAEH,oBAAQ,IAAI,YAAY,QAAQ,aAAa;AAC7C,kBAAM,UAAU,KAAK,KAAK,IAAI,QAAQ,QAAQ,gBAAgB,QAAQ,0BAA0B;AAChG,kBAAM,QAAQ,IAAI,SAAS,QAAQ,eAAe,SAAS,MAAM;AACjE;AAAA,UAEF,KAAK;AAEH,oBAAQ,IAAI,MAAM,GAAG,QAAQ,cAAc,MAAM;AACjD,oBAAQ,UAAU,QAAQ,gBAAgB,IAAI;AAC9C,oBAAQ,IAAI,MAAM,IAAI,SAAS,QAAQ,eAAe,MAAM;AAC5D,kBAAM,UAAU,QAAQ,iBAAiB,IAAI,SAAS,QAAQ,eAAe,SAAS;AACtF;AAAA,UAEF;AACE,kBAAM,IAAI,MAAM,mBAAmB,KAAK,qCAAqC;AAAA,QAC/E;AAEA,eAAO,UAAU,MAAM,QAAQ,MAAM,iBAAiB,IAAI,UAAU,OAAO,MAAM,QAAQ,eAAe,MAAM,CAAC;AAAA,MACjH;AAAA,IACF;AAOA,YAAQ,kBAAkB,SAAU,KAAK,SAAS;AAChD,YAAM,QAAQ,aAAa,QAAQ,aAAa;AAChD,YAAM,MAAM,aAAa,QAAQ,cAAc;AAE/C,YAAM,QAAQ,IAAI;AAAA,QAChB,YAAY,QAAQ,OAAO,QAAQ,MAAM,OAAO,MAAM;AAAA,MACxD;AACA,YAAM,MAAM,IAAI,OAAO,KAAK;AAE5B,aAAO,QAAQ,KAAK,IAAI,MAAM,GAAG,GAAG,IAAI;AAAA,IAC1C;AASA,aAAS,aAAc,GAAG;AACxB,aAAO,EAAE,QAAQ,yBAAyB,MAAM;AAAA,IAClD;AACA,YAAQ,eAAe;AAOvB,YAAQ,0BAA0B,SAAU,QAAQ,GAAG;AACrD,UAAI,OAAO,CAAC,EAAE,SAAS,aAAa;AAClC,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,CAAC,EAAE,YAAY,GAAG;AAC3B,eAAO,OAAO,CAAC;AAAA,MACjB;AAEA,YAAM,QAAQ,OAAO,CAAC,EAAE;AACxB,YAAM,OAAO,OAAO,CAAC,EAAE,KAAK,QAAQ,UAAU,OAAO;AAErD,aAAO,KAAK,GAAG,EAAE,GAAG;AAClB,YAAI,OAAO,CAAC,EAAE,SAAS,QAAQ,OAAO,CAAC,EAAE,UAAU,OAAO;AACxD,iBAAO,OAAO,CAAC;AAAA,QACjB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAMA,QAAM,sBAAsB;AAC5B,QAAM,yBAAyB;AAC/B,QAAM,oBAAoB;AAAA,MACxB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAMA,aAAS,kBAAkB,IAAI;AAC7B,aAAO,kBAAkB,EAAE;AAAA,IAC7B;AAMA,YAAQ,aAAa,SAAU,KAAK;AAClC,UAAI,oBAAoB,KAAK,GAAG,GAAG;AACjC,eAAO,IAAI,QAAQ,wBAAwB,iBAAiB;AAAA,MAC9D;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;AChTA;AAAA;AAAA;AAMA,QAAM,QAAQ;AAMd,WAAO,UAAU,aAAW;AAC1B,YAAM,OAAO,IAAI,OAAO,uBACE,MAAM,aAAa,QAAQ,aAAa,IACxC,OAAO,MAAM,aAAa,QAAQ,cAAc,IAAI,GAAG;AAEjF,aAAQ;AAAA,QACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOE,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,OAAO;AAAA,cACP,MAAM,MAAM,cAAc,OAAO,OAAO;AAAA,YAC1C;AAAA,UACF;AAAA,UACA,WAAW,CAAC,QAAQ,MAAM;AACxB,kBAAM,QAAQ,OAAO,CAAC;AACtB,kBAAM,QAAQ,MAAM,KAAK,YAAY,QAAQ,aAAa;AAC1D,kBAAM,QAAQ,MAAM,SAAS,MAAM,MAAM,OAAO,OAAO;AACvD,kBAAM,SAAS,OAAO,KAAK;AAC3B,kBAAM,OAAO,MAAM,gBAAgB,MAAM,MAAM,OAAO;AAAA,UACxD;AAAA,QACF;AAAA,QAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOD,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,gBACR;AAAA,kBACE,OAAO;AAAA,kBACP,MAAM,CAAC,QAAQ,QAAQ,WAAW,QAAQ;AAAA,gBAC5C;AAAA,gBAAG;AAAA,kBACD,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,SAAS,MAAM,cAAc,SAAS,OAAO;AAAA,gBAC/C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA,WAAW,CAAC,QAAQ,GAAG,MAAM;AAC3B,kBAAM,QAAQ,OAAO,CAAC,EAAE,SAAS,CAAC;AAClC,kBAAM,UAAU,MAAM,QAAQ,QAAQ,QAAQ,cAAc;AAC5D,kBAAM,YAAY,OAAO,CAAC,EAAE,SAAS,IAAI,CAAC;AAC1C,kBAAM,QAAQ,MAAM,SAAS,MAAM,SAAS,GAAG,OAAO;AACtD,kBAAM,SAAS,OAAO,SAAS;AAC/B,gBAAI,MAAM,QAAQ,WAAY,UAAU,QAAQ,eAAe,QAAS;AACtE,qBAAO,CAAC,EAAE,SAAS,OAAO,GAAG,CAAC;AAAA,YAChC,OAAO;AACL,oBAAM,UAAU,MAAM,QAAQ,MAAM,UAAU,QAAQ,eAAe,MAAM;AAAA,YAC7E;AAAA,UACF;AAAA,QACF;AAAA,QAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQD,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA;AAAA;AAAA,cAGE,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YAAG;AAAA,cACD,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YAAG;AAAA,cACD,OAAO;AAAA,cACP,MAAM;AAAA,cACN,SAAS,MAAM,cAAc,QAAQ,OAAO;AAAA,YAC9C;AAAA,UACF;AAAA,UACA,WAAW,CAAC,QAAQ,MAAM;AACxB,kBAAM,QAAQ,OAAO,IAAI,CAAC;AAC1B,kBAAM,YAAY,MAAM,wBAAwB,QAAQ,CAAC;AACzD,kBAAM,QAAQ,MAAM,SAAS,MAAM,SAAS,GAAG,OAAO;AAEtD,kBAAM,SAAS,OAAO,SAAS;AAE/B,mBAAO,OAAO,IAAI,GAAG,CAAC;AAAA,UACxB;AAAA,QACF;AAAA,QAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAWD,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YAAG;AAAA,cACD,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YAAG;AAAA,cACD,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA,WAAW,CAAC,QAAQ,MAAM;AACxB,kBAAM,KAAK,MAAM,wBAAwB,QAAQ,CAAC;AAClD,kBAAM,KAAK,OAAO,IAAI,CAAC;AACvB,gBAAI,UAAU;AACd,gBAAI,IAAI;AACR,mBAAO,EAAE,GAAG;AACV,kBAAI,OAAO,CAAC,MAAM,IAAI;AACpB,uBAAO,IAAI,CAAC,EAAE,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC;AACtE;AAAA,cACF;AACA,0BAAY,OAAO,CAAC,EAAE,UAAU,GAAG,SAAS,OAAO,CAAC,EAAE,SAAS,GAAG,SAAS;AAAA,YAC7E;AACA,mBAAO,IAAI,CAAC,EAAE,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC;AAAA,UACxE;AAAA,QACF;AAAA,QAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQD,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,QAAQ;AAAA,YACV;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA,WAAW,CAAC,QAAQ,MAAM;AAExB,gBAAI,MAAM,IAAI;AACd,mBAAO,MAAM,KAAK,iBAAiB,OAAO,EAAE,GAAG,EAAE,KAAK;AAEtD,kBAAM,OAAO,OAAO,GAAG,EAAE,KAAK,WAAW;AACzC,gBAAI,OAAO,GAAG;AAAE;AAAA,YAAQ;AAExB,kBAAM,QAAQ,OAAO,CAAC,EAAE,QAAQ;AAChC,qBAAS,IAAI,KAAK,IAAI,GAAG,KAAK;AAC5B,kBAAI,OAAO,CAAC,EAAE,QAAQ,OAAO;AAAE;AAAA,cAAU;AAEzC,oBAAM,QAAQ,OAAO,CAAC;AACtB,oBAAM,OAAO,MAAM,SAAS,IAAI,MAAM,QAAQ,SAAS,KAAK;AAC5D,oBAAM,OAAO,MAAM,SAAS,IAAI,MAAM,QAAQ,SAAS,KAAK;AAE5D,kBAAI,OAAO,GAAG;AACZ,oBAAI,UAAU,QAAQ,OAAO,IAAI,OAAO;AACxC,yBAAS,IAAI,GAAG,MAAM,MAAM,IAAI,GAAG,MAAM,GAAG,KAAK;AAC/C,sBAAI,aAAa,OAAO,CAAC,EAAE,MAAM;AAC/B,2BAAO,CAAC,EAAE,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,EAAE,IAAI;AACjD,wBAAI,OAAO,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,KAAK,SAAS;AAC5C,iCAAW;AAAA,oBACb;AACA,2BAAO,CAAC,EAAE,KAAK,UAAU;AACzB;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAEA,kBAAI,aAAa,MAAM,QAAQ,MAAM,QAAQ,MAAM,KAAK,SAAS;AAC/D,sBAAM,MAAM,MAAM,KAAK;AACvB,yBAAS,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,KAAK;AACnC,sBAAI,aAAa,OAAO,CAAC,EAAE,MAAM;AAC/B,2BAAO;AAAA,kBACT,WAAW,cAAc,OAAO,CAAC,EAAE,MAAM;AACvC;AAAA,kBACF;AACA,wBAAM,QAAQ,OAAO,CAAC,EAAE,UAAU,OAAO,OAAO,CAAC,CAAC;AAAA,gBACpD;AAAA,cACF;AAEA,kBAAI,OAAO,GAAG;AAEZ,sBAAM,MAAM,CAAC;AAEb,oBAAI,MAAM,IAAI;AAEd,oBAAI,MAAM;AAEV,yBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,sBAAI,aAAa,OAAO,CAAC,EAAE,MAAM;AAC/B,0BAAM,OAAO,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,KAAK,WAAW;AAClD;AAAA,kBACF,WAAW,cAAc,OAAO,CAAC,EAAE,MAAM;AACvC,wBAAI,QAAQ,CAAC;AAAA,kBACf;AAAA,gBACF;AAEA,yBAAS,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AAC9B,sBAAI,cAAc,OAAO,CAAC,EAAE,MAAM;AAChC,0BAAM;AACN;AAAA,kBACF,WAAW,aAAa,OAAO,CAAC,EAAE,MAAM;AACtC,wBAAI,KAAK,CAAC;AAAA,kBACZ;AAAA,gBACF;AAEA,sBAAM,MAAM,IAAI,QAAQ,CAAC;AACzB,oBAAI,OAAO,MAAM;AACjB,uBAAO,OAAO,OAAO,OAAO;AAC5B,uBAAO,QAAQ,MAAM,QAAQ,WAAW,OAAO,EAAE;AAEjD,yBAAS,IAAI,IAAI,MAAM,MAAM,IAAI,OAAO,IAAI,EAAE,CAAC,GAAG,IAAI,KAAK,KAAK;AAC9D,yBAAO,CAAC,EAAE,UAAU,OAAO,OAAO,CAAC,CAAC;AAAA,gBACtC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QAAG;AAAA;AAAA;AAAA;AAAA,UAID,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,gBACR;AAAA,kBACE,OAAO;AAAA,kBACP,SAAS;AAAA;AAAA,gBACX;AAAA,gBAAG;AAAA,kBACD,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,SAAS,MAAM,cAAc,SAAS,OAAO;AAAA,gBAC/C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA,WAAW,CAAC,QAAQ,GAAG,MAAM;AAC3B,kBAAM,QAAQ,OAAO,CAAC,EAAE,SAAS,CAAC;AAClC,kBAAM,UAAU,MAAM;AACtB,kBAAM,QAAQ,MAAM,SAAS,SAAS,GAAG,OAAO;AAChD,kBAAM,eAAe,MAAM,wBAAwB,OAAO,CAAC,EAAE,UAAU,IAAI,CAAC;AAC5E,kBAAM,SAAS,OAAO,YAAY;AAClC,kBAAM,UAAU,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,cAAc,IAAI,QAAQ,eAAe,MAAM;AAAA,UACvG;AAAA,QACF;AAAA,QAAG;AAAA;AAAA;AAAA;AAAA;AAAA,UAKD,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YAAG;AAAA,cACD,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,gBACR;AAAA,kBACE,UAAU;AAAA,kBACV,MAAM;AAAA,gBACR;AAAA,gBAAG;AAAA,kBACD,UAAU;AAAA,kBACV,MAAM;AAAA,kBACN,SAAS,MAAM,cAAc,QAAQ,OAAO;AAAA,gBAC9C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA,WAAW,CAAC,QAAQ,GAAG,MAAM;AAC3B,kBAAM,QAAQ,OAAO,CAAC,EAAE,SAAS,CAAC;AAClC,kBAAM,UAAU,MAAM;AACtB,kBAAM,QAAQ,MAAM,SAAS,SAAS,GAAG,OAAO;AAChD,gBAAI,KAAK,IAAI;AACb,mBAAO,OAAO,KAAK,CAAC,KAClB,OAAO,KAAK,CAAC,EAAE,SAAS,uBACxB,OAAO,KAAK,CAAC,EAAE,SAAS,oBAAoB;AAAE;AAAA,YAAM;AACtD,kBAAM,SAAS,OAAO,OAAO,KAAK,CAAC,CAAC;AACpC,mBAAO,CAAC,EAAE,WAAW,OAAO,CAAC,EAAE,SAAS,MAAM,GAAG,EAAE;AAAA,UACrD;AAAA,QACF;AAAA,QAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQD,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA;AAAA;AAAA,cAGE,OAAO;AAAA,cACP,MAAM,CAAC,QACL,QAAQ,uBACR,QAAQ;AAAA,YACZ;AAAA,YAAG;AAAA,cACD,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YAAG;AAAA,cACD,OAAO;AAAA,cACP,MAAM;AAAA,cACN,SAAS,MAAM,cAAc,QAAQ,OAAO;AAAA,cAC5C,UAAU,CAAC,QAAQ,IAAI,WAAW;AAAA,YACpC;AAAA,YAAG;AAAA,cACD,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA,WAAW,CAAC,QAAQ,MAAM;AACxB,kBAAM,QAAQ,OAAO,IAAI,CAAC;AAC1B,kBAAM,UAAU,MAAM;AACtB,kBAAM,QAAQ,MAAM,SAAS,SAAS,GAAG,OAAO;AAChD,kBAAM,eAAe,MAAM,wBAAwB,QAAQ,CAAC;AAC5D,kBAAM,SAAS,OAAO,YAAY;AAClC,mBAAO,OAAO,IAAI,GAAG,CAAC;AAAA,UACxB;AAAA,QACF;AAAA,QAAG;AAAA;AAAA;AAAA;AAAA,UAID,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YAAG;AAAA,cACD,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,gBACR;AAAA,kBACE,UAAU;AAAA,kBACV,MAAM;AAAA,kBACN,SAAS,MAAM,cAAc,OAAO,OAAO;AAAA,gBAC7C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA,WAAW,CAAC,QAAQ,GAAG,MAAM;AAC3B,kBAAM,QAAQ,OAAO,CAAC,EAAE,SAAS,CAAC;AAClC,kBAAM,UAAU,MAAM;AACtB,kBAAM,QAAQ,MAAM,SAAS,SAAS,QAAQ,YAAY,QAAQ,aAAa,GAAG,OAAO;AACzF,kBAAM,SAAS,OAAO,OAAO,IAAI,CAAC,CAAC;AACnC,kBAAM,UAAU,QAAQ,MAAM,GAAG,QAAQ,YAAY,QAAQ,aAAa,CAAC;AAC3E,kBAAM,UAAU,KAAK,OAAO,MAAM,MAChC,UAAU,QAAQ,MAAM,GAAG,EAAE;AAAA,UACjC;AAAA,QACF;AAAA,QAAG;AAAA;AAAA;AAAA;AAAA;AAAA,UAKD,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,gBACR;AAAA,kBACE,UAAU;AAAA,kBACV,MAAM;AAAA,gBACR;AAAA,gBAAG;AAAA,kBACD,UAAU;AAAA,kBACV,MAAM;AAAA,kBACN,SAAS,MAAM,cAAc,QAAQ,OAAO;AAAA,gBAC9C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA,WAAW,CAAC,QAAQ,GAAG,MAAM;AAC3B,kBAAM,QAAQ,OAAO,CAAC,EAAE,SAAS,CAAC;AAClC,kBAAM,QAAQ,MAAM,SAAS,MAAM,SAAS,GAAG,OAAO;AAEtD,gBAAI,KAAK,IAAI;AACb,mBAAO,OAAO,KAAK,CAAC,KAAK,OAAO,KAAK,CAAC,EAAE,YAAY,IAAI;AAAE;AAAA,YAAM;AAChE,kBAAM,eAAe,MAAM,wBAAwB,QAAQ,EAAE;AAC7D,kBAAM,SAAS,OAAO,YAAY;AAClC,mBAAO,CAAC,EAAE,WAAW,OAAO,CAAC,EAAE,SAAS,MAAM,GAAG,EAAE;AAAA,UACrD;AAAA,QACF;AAAA,QAAG;AAAA;AAAA;AAAA;AAAA,UAID,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU,CAAC,QAAQ,IAAI,WAAW;AAAA,cAClC,SAAS,CAAC,QAAQ,IAAI,MAAM,IAAI,MAAM;AAAA,YACxC;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA,WAAW,CAAC,QAAQ,MAAM;AACxB,kBAAM,QAAQ,OAAO,CAAC;AACtB,kBAAM,OAAO;AACb,kBAAM,MAAM;AACZ,kBAAM,UAAU;AAChB,kBAAM,UAAU,OAAO,IAAI,CAAC,EAAE;AAC9B,kBAAM,QAAQ,QAAQ,YAAY,QAAQ,aAAa;AACvD,kBAAM,QAAQ,MAAM,SAAS,SAAS,OAAO,OAAO;AACpD,kBAAM,SAAS,OAAO,KAAK;AAC3B,kBAAM,SAAS;AACf,mBAAO,OAAO,IAAI,GAAG,CAAC;AAAA,UACxB;AAAA,QACF;AAAA,QAAG;AAAA;AAAA;AAAA;AAAA,UAID,MAAM;AAAA,UACN,OAAO;AAAA,YACL;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,gBACR;AAAA,kBACE,UAAU;AAAA,kBACV,SAAS,MAAM,cAAc,OAAO,OAAO;AAAA,kBAC3C,MAAM,CAAC,MAAM,MAAM,iBAAiB,MAAM;AAAA,gBAC5C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA,WAAW,CAAC,QAAQ,GAAG,MAAM;AAC3B,kBAAM,QAAQ,OAAO,CAAC,EAAE,SAAS,CAAC;AAClC,kBAAM,UAAU,MAAM;AACtB,kBAAM,QAAQ,MAAM,SAAS,SAAS,QAAQ,YAAY,QAAQ,aAAa,GAAG,OAAO;AACzF,gBAAI,KAAK,IAAI;AACb;AAAG,kBAAI,OAAO,EAAE,KAAK,OAAO,EAAE,EAAE,YAAY,IAAI;AAAE;AAAA,cAAO;AAAA,mBAAS,OAAO,OAAO;AAChF,kBAAM,eAAe,MAAM,wBAAwB,QAAQ,EAAE;AAC7D,kBAAM,SAAS,OAAO,YAAY;AAClC,kBAAM,UAAU,QAAQ,MAAM,GAAG,QAAQ,YAAY,QAAQ,aAAa,CAAC;AAC3E,kBAAM,UAAU,KAAK,OAAO,MAAM,MAChC,UAAU,QAAQ,MAAM,GAAG,EAAE;AAAA,UACjC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,aAAS,KAAK,KAAK;AACjB,aAAO,IAAI,MAAM,EAAE,EAAE,CAAC;AAAA,IACxB;AASA,aAAS,OAAO,OAAO;AACrB,YAAM,SAAS;AACf,YAAM,YAAY,MAAM,SAAS,QAAQ,QACvC,EAAE,UAAU,IACZ,OAAO,CAAC,GACR,OACD;AAAA,IACH;AAAA;AAAA;;;AClgBA;AAAA;AAEA,QAAM,iBAAiB;AAoDvB,QAAM,iBAAiB;AAAA,MACrB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,mBAAmB,CAAC;AAAA,IACtB;AAMA,WAAO,UAAU,SAAS,WAAW,IAAI,UAAU;AACjD,UAAI,UAAU,OAAO,OAAO,CAAC,GAAG,cAAc;AAC9C,gBAAU,OAAO,OAAO,SAAS,QAAQ;AAEzC,YAAM,WAAW,eAAe,OAAO;AAKvC,eAAS,WAAW,OAAO;AACzB,cAAM,SAAS,MAAM;AAErB,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,kBAAM,UAAU,SAAS,CAAC;AAC1B,gBAAI,IAAI;AACR,kBAAM,QAAQ,QAAQ,MAAM,MAAM,OAAK;AACrC,oBAAM,MAAM,KAAK,QAAQ,GAAG,CAAC;AAC7B,kBAAI,IAAI,MAAM,MAAM;AAAE,oBAAI,IAAI;AAAA,cAAG;AACjC,qBAAO,IAAI;AAAA,YACb,CAAC;AACD,gBAAI,OAAO;AACT,kBAAI;AACF,wBAAQ,UAAU,QAAQ,GAAG,CAAC;AAC9B,oBAAI,QAAQ,SAAS,uBAAuB,QAAQ,SAAS,oBAAoB;AAE/E;AAAA,gBACF;AAAA,cACF,SAAS,OAAO;AAEd,wBAAQ,MAAM,wCAAwC,QAAQ,IAAI,MAAM,MAAM,OAAO,EAAE;AACvF,wBAAQ,MAAM,MAAM,KAAK;AAAA,cAC3B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,SAAG,KAAK,MAAM,OAAO,WAAW,oBAAoB,UAAU;AAAA,IAChE;AAUA,aAAS,KAAK,QAAQ,GAAG,GAAG;AAE1B,YAAM,MAAM;AAAA,QACV,OAAO;AAAA,QACP,GAAG;AAAA;AAAA,MACL;AAEA,YAAM,KAAK,EAAE,UAAU,SACnB,IAAI,EAAE,QACN,EAAE;AAEN,UAAI,EAAE,UAAU,UAAa,KAAK,GAAG;AAEnC,eAAO;AAAA,MACT;AAEA,YAAM,QAAQ,IAAI,QAAQ,EAAE;AAG5B,UAAI,UAAU,QAAW;AAAE,eAAO;AAAA,MAAK;AAEvC,iBAAW,OAAO,OAAO,KAAK,CAAC,GAAG;AAChC,YAAI,QAAQ,WAAW,QAAQ,YAAY;AAAE;AAAA,QAAU;AAEvD,YAAI,MAAM,GAAG,MAAM,QAAW;AAAE,iBAAO;AAAA,QAAK;AAE5C,YAAI,QAAQ,cAAc,iBAAiB,EAAE,QAAQ,GAAG;AACtD,cAAI,MAAM,SAAS,WAAW,GAAG;AAC/B,mBAAO;AAAA,UACT;AACA,cAAI;AAEJ,gBAAM,aAAa,EAAE;AAErB,gBAAM,WAAW,MAAM;AACvB,cAAI,WAAW,MAAM,QAAM,GAAG,aAAa,MAAS,GAAG;AAErD,oBAAQ,WAAW,MAAM,QAAM,KAAK,UAAU,GAAG,UAAU,EAAE,EAAE,KAAK;AACpE,gBAAI,OAAO;AAET,oBAAM,IAAI,KAAK,UAAU,EAAE;AAC3B,kBAAI,IAAI,KAAK,IAAI,IAAI,SAAS,SAAS;AAAA,YACzC;AAAA,UACF,OAAO;AACL,qBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,sBAAQ,WAAW,MAAM,QAAM,KAAK,UAAU,GAAG,EAAE,EAAE,KAAK;AAC1D,kBAAI,OAAO;AACT,oBAAI,IAAI;AAER;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,cAAI,UAAU,OAAO;AAAE,mBAAO;AAAA,UAAK;AAEnC;AAAA,QACF;AAEA,gBAAQ,OAAO,EAAE,GAAG,GAAG;AAAA,UACvB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,gBAAI,MAAM,GAAG,MAAM,EAAE,GAAG,GAAG;AAAE,qBAAO;AAAA,YAAK;AACzC;AAAA,UACF,KAAK;AACH,gBAAI,CAAC,EAAE,GAAG,EAAE,MAAM,GAAG,CAAC,GAAG;AAAE,qBAAO;AAAA,YAAK;AACvC;AAAA,UACF,KAAK;AACH,gBAAI,mBAAmB,EAAE,GAAG,CAAC,GAAG;AAC9B,oBAAM,IAAI,EAAE,GAAG,EAAE,MAAM,QAAM,GAAG,MAAM,GAAG,CAAC,CAAC;AAC3C,kBAAI,MAAM,OAAO;AAAE,uBAAO;AAAA,cAAK;AAC/B;AAAA,YACF;AAAA,UAEF;AACE,kBAAM,IAAI,MAAM,sCAAsC,GAAG,oFAAoF;AAAA,QAC/I;AAAA,MACF;AAGA,UAAI,QAAQ;AACZ,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB,KAAK;AAC7B,aAAO,MAAM,QAAQ,GAAG,KAAK,IAAI,UAAU,IAAI,MAAM,OAAK,OAAO,MAAM,QAAQ;AAAA,IACjF;AAEA,aAAS,mBAAmB,KAAK;AAC/B,aAAO,MAAM,QAAQ,GAAG,KAAK,IAAI,UAAU,IAAI,MAAM,OAAK,OAAO,MAAM,UAAU;AAAA,IACnF;AASA,aAAS,IAAI,KAAK,GAAG;AACnB,aAAO,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,SAAS,CAAC;AAAA,IAC7C;AAOA,aAAS,KAAK,KAAK;AACjB,aAAO,IAAI,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC;AAAA,IAC9B;AAAA;AAAA;", "names": []}