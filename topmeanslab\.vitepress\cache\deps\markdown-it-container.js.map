{"version": 3, "sources": ["../../../node_modules/markdown-it-container/index.mjs"], "sourcesContent": ["// Process block-level custom containers\n//\nexport default function container_plugin (md, name, options) {\n  // Second param may be useful if you decide\n  // to increase minimal allowed marker length\n  function validateDefault (params/*, markup */) {\n    return params.trim().split(' ', 2)[0] === name\n  }\n\n  function renderDefault (tokens, idx, _options, env, slf) {\n    // add a class to the opening tag\n    if (tokens[idx].nesting === 1) {\n      tokens[idx].attrJoin('class', name)\n    }\n\n    return slf.renderToken(tokens, idx, _options, env, slf)\n  }\n\n  options = options || {}\n\n  const min_markers = 3\n  const marker_str  = options.marker || ':'\n  const marker_char = marker_str.charCodeAt(0)\n  const marker_len  = marker_str.length\n  const validate    = options.validate || validateDefault\n  const render      = options.render || renderDefault\n\n  function container (state, startLine, endLine, silent) {\n    let pos\n    let auto_closed = false\n    let start = state.bMarks[startLine] + state.tShift[startLine]\n    let max = state.eMarks[startLine]\n\n    // Check out the first character quickly,\n    // this should filter out most of non-containers\n    //\n    if (marker_char !== state.src.charCodeAt(start)) { return false }\n\n    // Check out the rest of the marker string\n    //\n    for (pos = start + 1; pos <= max; pos++) {\n      if (marker_str[(pos - start) % marker_len] !== state.src[pos]) {\n        break\n      }\n    }\n\n    const marker_count = Math.floor((pos - start) / marker_len)\n    if (marker_count < min_markers) { return false }\n    pos -= (pos - start) % marker_len\n\n    const markup = state.src.slice(start, pos)\n    const params = state.src.slice(pos, max)\n    if (!validate(params, markup)) { return false }\n\n    // Since start is found, we can report success here in validation mode\n    //\n    if (silent) { return true }\n\n    // Search for the end of the block\n    //\n    let nextLine = startLine\n\n    for (;;) {\n      nextLine++\n      if (nextLine >= endLine) {\n        // unclosed block should be autoclosed by end of document.\n        // also block seems to be autoclosed by end of parent\n        break\n      }\n\n      start = state.bMarks[nextLine] + state.tShift[nextLine]\n      max = state.eMarks[nextLine]\n\n      if (start < max && state.sCount[nextLine] < state.blkIndent) {\n        // non-empty line with negative indent should stop the list:\n        // - ```\n        //  test\n        break\n      }\n\n      if (marker_char !== state.src.charCodeAt(start)) { continue }\n\n      if (state.sCount[nextLine] - state.blkIndent >= 4) {\n        // closing fence should be indented less than 4 spaces\n        continue\n      }\n\n      for (pos = start + 1; pos <= max; pos++) {\n        if (marker_str[(pos - start) % marker_len] !== state.src[pos]) {\n          break\n        }\n      }\n\n      // closing code fence must be at least as long as the opening one\n      if (Math.floor((pos - start) / marker_len) < marker_count) { continue }\n\n      // make sure tail has spaces only\n      pos -= (pos - start) % marker_len\n      pos = state.skipSpaces(pos)\n\n      if (pos < max) { continue }\n\n      // found!\n      auto_closed = true\n      break\n    }\n\n    const old_parent = state.parentType\n    const old_line_max = state.lineMax\n    state.parentType = 'container'\n\n    // this will prevent lazy continuations from ever going past our end marker\n    state.lineMax = nextLine\n\n    const token_o  = state.push('container_' + name + '_open', 'div', 1)\n    token_o.markup = markup\n    token_o.block  = true\n    token_o.info   = params\n    token_o.map    = [startLine, nextLine]\n\n    state.md.block.tokenize(state, startLine + 1, nextLine)\n\n    const token_c  = state.push('container_' + name + '_close', 'div', -1)\n    token_c.markup = state.src.slice(start, pos)\n    token_c.block  = true\n\n    state.parentType = old_parent\n    state.lineMax = old_line_max\n    state.line = nextLine + (auto_closed ? 1 : 0)\n\n    return true\n  }\n\n  md.block.ruler.before('fence', 'container_' + name, container, {\n    alt: ['paragraph', 'reference', 'blockquote', 'list']\n  })\n  md.renderer.rules['container_' + name + '_open'] = render\n  md.renderer.rules['container_' + name + '_close'] = render\n};\n"], "mappings": ";;;AAEe,SAAR,iBAAmC,IAAI,MAAM,SAAS;AAG3D,WAAS,gBAAiB,QAAqB;AAC7C,WAAO,OAAO,KAAK,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM;AAAA,EAC5C;AAEA,WAAS,cAAe,QAAQ,KAAK,UAAU,KAAK,KAAK;AAEvD,QAAI,OAAO,GAAG,EAAE,YAAY,GAAG;AAC7B,aAAO,GAAG,EAAE,SAAS,SAAS,IAAI;AAAA,IACpC;AAEA,WAAO,IAAI,YAAY,QAAQ,KAAK,UAAU,KAAK,GAAG;AAAA,EACxD;AAEA,YAAU,WAAW,CAAC;AAEtB,QAAM,cAAc;AACpB,QAAM,aAAc,QAAQ,UAAU;AACtC,QAAM,cAAc,WAAW,WAAW,CAAC;AAC3C,QAAM,aAAc,WAAW;AAC/B,QAAM,WAAc,QAAQ,YAAY;AACxC,QAAM,SAAc,QAAQ,UAAU;AAEtC,WAAS,UAAW,OAAO,WAAW,SAAS,QAAQ;AACrD,QAAI;AACJ,QAAI,cAAc;AAClB,QAAI,QAAQ,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS;AAC5D,QAAI,MAAM,MAAM,OAAO,SAAS;AAKhC,QAAI,gBAAgB,MAAM,IAAI,WAAW,KAAK,GAAG;AAAE,aAAO;AAAA,IAAM;AAIhE,SAAK,MAAM,QAAQ,GAAG,OAAO,KAAK,OAAO;AACvC,UAAI,YAAY,MAAM,SAAS,UAAU,MAAM,MAAM,IAAI,GAAG,GAAG;AAC7D;AAAA,MACF;AAAA,IACF;AAEA,UAAM,eAAe,KAAK,OAAO,MAAM,SAAS,UAAU;AAC1D,QAAI,eAAe,aAAa;AAAE,aAAO;AAAA,IAAM;AAC/C,YAAQ,MAAM,SAAS;AAEvB,UAAM,SAAS,MAAM,IAAI,MAAM,OAAO,GAAG;AACzC,UAAM,SAAS,MAAM,IAAI,MAAM,KAAK,GAAG;AACvC,QAAI,CAAC,SAAS,QAAQ,MAAM,GAAG;AAAE,aAAO;AAAA,IAAM;AAI9C,QAAI,QAAQ;AAAE,aAAO;AAAA,IAAK;AAI1B,QAAI,WAAW;AAEf,eAAS;AACP;AACA,UAAI,YAAY,SAAS;AAGvB;AAAA,MACF;AAEA,cAAQ,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AACtD,YAAM,MAAM,OAAO,QAAQ;AAE3B,UAAI,QAAQ,OAAO,MAAM,OAAO,QAAQ,IAAI,MAAM,WAAW;AAI3D;AAAA,MACF;AAEA,UAAI,gBAAgB,MAAM,IAAI,WAAW,KAAK,GAAG;AAAE;AAAA,MAAS;AAE5D,UAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,aAAa,GAAG;AAEjD;AAAA,MACF;AAEA,WAAK,MAAM,QAAQ,GAAG,OAAO,KAAK,OAAO;AACvC,YAAI,YAAY,MAAM,SAAS,UAAU,MAAM,MAAM,IAAI,GAAG,GAAG;AAC7D;AAAA,QACF;AAAA,MACF;AAGA,UAAI,KAAK,OAAO,MAAM,SAAS,UAAU,IAAI,cAAc;AAAE;AAAA,MAAS;AAGtE,cAAQ,MAAM,SAAS;AACvB,YAAM,MAAM,WAAW,GAAG;AAE1B,UAAI,MAAM,KAAK;AAAE;AAAA,MAAS;AAG1B,oBAAc;AACd;AAAA,IACF;AAEA,UAAM,aAAa,MAAM;AACzB,UAAM,eAAe,MAAM;AAC3B,UAAM,aAAa;AAGnB,UAAM,UAAU;AAEhB,UAAM,UAAW,MAAM,KAAK,eAAe,OAAO,SAAS,OAAO,CAAC;AACnE,YAAQ,SAAS;AACjB,YAAQ,QAAS;AACjB,YAAQ,OAAS;AACjB,YAAQ,MAAS,CAAC,WAAW,QAAQ;AAErC,UAAM,GAAG,MAAM,SAAS,OAAO,YAAY,GAAG,QAAQ;AAEtD,UAAM,UAAW,MAAM,KAAK,eAAe,OAAO,UAAU,OAAO,EAAE;AACrE,YAAQ,SAAS,MAAM,IAAI,MAAM,OAAO,GAAG;AAC3C,YAAQ,QAAS;AAEjB,UAAM,aAAa;AACnB,UAAM,UAAU;AAChB,UAAM,OAAO,YAAY,cAAc,IAAI;AAE3C,WAAO;AAAA,EACT;AAEA,KAAG,MAAM,MAAM,OAAO,SAAS,eAAe,MAAM,WAAW;AAAA,IAC7D,KAAK,CAAC,aAAa,aAAa,cAAc,MAAM;AAAA,EACtD,CAAC;AACD,KAAG,SAAS,MAAM,eAAe,OAAO,OAAO,IAAI;AACnD,KAAG,SAAS,MAAM,eAAe,OAAO,QAAQ,IAAI;AACtD;", "names": []}