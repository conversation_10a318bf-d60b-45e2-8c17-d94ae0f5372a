<template>
  <!-- Google Analytics 组件 - 无需渲染内容 -->
</template>

<script setup>
import { onMounted, nextTick } from 'vue'

// Google Analytics 配置
const GA_MEASUREMENT_ID = 'G-XRPE3VXBG1'

// 初始化 Google Analytics
const initGA = () => {
  // 检查是否已经加载过 gtag
  if (window.gtag) {
    console.log('Google Analytics 已初始化')
    return
  }

  try {
    // 创建 dataLayer
    window.dataLayer = window.dataLayer || []
    
    // 定义 gtag 函数
    function gtag() {
      window.dataLayer.push(arguments)
    }
    
    // 将 gtag 函数挂载到 window 对象
    window.gtag = gtag
    
    // 初始化 GA
    gtag('js', new Date())
    gtag('config', GA_MEASUREMENT_ID, {
      // 可以添加更多配置选项
      page_title: document.title,
      page_location: window.location.href
    })
    
    console.log('Google Analytics 初始化成功')
  } catch (error) {
    console.error('Google Analytics 初始化失败:', error)
  }
}

// 发送页面浏览事件
const trackPageView = (pagePath = null) => {
  if (window.gtag) {
    const path = pagePath || window.location.pathname
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_path: path,
      page_title: document.title,
      page_location: window.location.href
    })
    console.log('GA 页面浏览事件已发送:', path)
  }
}

// 发送自定义事件
const trackEvent = (eventName, parameters = {}) => {
  if (window.gtag) {
    window.gtag('event', eventName, {
      event_category: 'engagement',
      event_label: window.location.pathname,
      ...parameters
    })
    console.log('GA 自定义事件已发送:', eventName, parameters)
  }
}

// 组件挂载时初始化
onMounted(async () => {
  await nextTick()
  
  // 检查是否在浏览器环境
  if (typeof window !== 'undefined') {
    initGA()
    
    // 发送初始页面浏览事件
    setTimeout(() => {
      trackPageView()
    }, 100)
  }
})

// 导出方法供其他组件使用
defineExpose({
  trackPageView,
  trackEvent
})
</script>

<script>
// 全局方法，可以在任何地方调用
export const useGoogleAnalytics = () => {
  const trackPageView = (pagePath = null) => {
    if (window.gtag) {
      const path = pagePath || window.location.pathname
      window.gtag('config', 'G-XRPE3VXBG1', {
        page_path: path,
        page_title: document.title,
        page_location: window.location.href
      })
    }
  }

  const trackEvent = (eventName, parameters = {}) => {
    if (window.gtag) {
      window.gtag('event', eventName, {
        event_category: 'engagement',
        event_label: window.location.pathname,
        ...parameters
      })
    }
  }

  const trackUserAction = (action, category = 'user_interaction') => {
    trackEvent(action, {
      event_category: category,
      event_label: window.location.pathname
    })
  }

  return {
    trackPageView,
    trackEvent,
    trackUserAction
  }
}
</script>

<style scoped>
/* 无需样式 */
</style>
