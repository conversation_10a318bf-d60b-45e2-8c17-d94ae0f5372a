import MarkdownIt from 'markdown-it';
import markdownItAttrs from 'markdown-it-attrs';
import markdownItContainer from 'markdown-it-container';

/**
 * Markdown 解析服务 - 使用 markdown-it 提供更优秀的渲染效果
 */
export class TopmeansMarkdownService {
    constructor() {
        this.md = null;
        this.initialize();
    }

    /**
     * 初始化 markdown-it 实例
     */
    initialize() {
        this.md = new MarkdownIt({
            // 启用 HTML 标签
            html: true,
            // 自动链接化 URL
            linkify: true,
            // 启用换行符转换
            breaks: true,
            // 启用排版增强
            typographer: true,
            // 引号美化
            quotes: '""\'\'',
        });

        // 配置插件
        this.setupPlugins();

        // 自定义渲染规则
        this.setupCustomRules();
    }

    /**
     * 配置插件
     */
    setupPlugins() {
        // 属性插件 - 允许为元素添加 class、id 等属性
        this.md.use(markdownItAttrs, {
            // 允许的属性
            allowedAttributes: ['class', 'id', 'style', 'target', 'rel']
        });

        // 自定义Emoji支持
        this.setupCustomEmoji();

        // 容器插件 - 支持自定义块级容器
        this.md.use(markdownItContainer, 'tip', {
            validate: function(params) {
                return params.trim().match(/^tip\s+(.*)$/);
            },
            render: function (tokens, idx) {
                const m = tokens[idx].info.trim().match(/^tip\s+(.*)$/);
                if (tokens[idx].nesting === 1) {
                    return '<div class="tip-container" style="background: linear-gradient(135deg, #e8f5e8, #f0f9f0) !important; border-left: 4px solid #52c41a !important; border-radius: 12px !important; padding: 20px !important; margin: 20px 0 !important; box-shadow: 0 8px 32px rgba(82, 196, 26, 0.15) !important; position: relative !important; overflow: hidden !important;">\n<div class="tip-title" style="font-weight: 700 !important; color: #389e0d !important; margin-bottom: 12px !important; display: flex !important; align-items: center !important; font-size: 1.1rem !important;"><span style="margin-right: 8px; font-size: 1.2em;">💡</span>' +
                           (m && m[1] ? m[1] : '提示') + '</div>\n';
                } else {
                    return '</div>\n';
                }
            }
        });

        this.md.use(markdownItContainer, 'warning', {
            validate: function(params) {
                return params.trim().match(/^warning\s+(.*)$/);
            },
            render: function (tokens, idx) {
                const m = tokens[idx].info.trim().match(/^warning\s+(.*)$/);
                if (tokens[idx].nesting === 1) {
                    return '<div class="warning-container" style="background: linear-gradient(135deg, #fff7e6, #fefcf6) !important; border-left: 4px solid #fa8c16 !important; border-radius: 12px !important; padding: 20px !important; margin: 20px 0 !important; box-shadow: 0 8px 32px rgba(250, 140, 22, 0.15) !important; position: relative !important; overflow: hidden !important;">\n<div class="warning-title" style="font-weight: 700 !important; color: #d46b08 !important; margin-bottom: 12px !important; display: flex !important; align-items: center !important; font-size: 1.1rem !important;"><span style="margin-right: 8px; font-size: 1.2em;">⚠️</span>' +
                           (m && m[1] ? m[1] : '警告') + '</div>\n';
                } else {
                    return '</div>\n';
                }
            }
        });
    }

    /**
     * 设置自定义Emoji支持
     */
    setupCustomEmoji() {
        // 常用emoji映射表
        const emojiMap = {
            ':sunny:': '☀️',
            ':sun:': '☀️',
            ':cloud:': '☁️',
            ':partly_sunny:': '⛅',
            ':rain:': '🌧️',
            ':thermometer:': '🌡️',
            ':droplet:': '💧',
            ':wind:': '🌬️',
            ':umbrella:': '☂️',
            ':sunglasses:': '🕶️',
            ':backpack:': '🎒',
            ':car:': '🚗',
            ':hotel:': '🏨',
            ':restaurant:': '🍽️',
            ':mountain:': '🏔️',
            ':camera:': '📷',
            ':map:': '🗺️',
            ':location:': '📍',
            ':calendar:': '📅',
            ':clock:': '🕐',
            ':money:': '💰',
            ':credit_card:': '💳',
            ':warning:': '⚠️',
            ':info:': 'ℹ️',
            ':bulb:': '💡',
            ':fire:': '🔥',
            ':star:': '⭐',
            ':heart:': '❤️',
            ':check:': '✅',
            ':x:': '❌'
        };

        // 添加emoji替换规则
        this.md.core.ruler.after('normalize', 'emoji', (state) => {
            let text = state.src;
            for (const [shortcode, emoji] of Object.entries(emojiMap)) {
                text = text.replace(new RegExp(shortcode.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), emoji);
            }
            state.src = text;
        });
    }

    /**
     * 设置自定义渲染规则
     */
    setupCustomRules() {
        // 自定义标题渲染 - 添加内联样式确保优先级
        const originalHeadingRender = this.md.renderer.rules.heading_open || function(tokens, idx, options, env, renderer) {
            return renderer.renderToken(tokens, idx, options);
        };

        this.md.renderer.rules.heading_open = function(tokens, idx, options, env, renderer) {
            const token = tokens[idx];
            const tag = token.tag;

            // 添加炫酷的内联样式
            const styleMap = {
                'h1': 'font-size: 2.2rem !important; font-weight: 700 !important; color: var(--vp-c-text-1) !important; margin: 32px 0 20px 0 !important; line-height: 1.2 !important; border-bottom: 2px solid var(--vp-c-divider-light) !important; padding-bottom: 12px !important; position: relative !important; text-rendering: optimizeLegibility !important; -webkit-font-smoothing: antialiased !important;',
                'h2': 'font-size: 1.6rem !important; font-weight: 600 !important; color: var(--vp-c-text-1) !important; margin: 28px 0 18px 0 !important; line-height: 1.3 !important; border-bottom: 1px solid var(--vp-c-divider) !important; padding-bottom: 8px !important; position: relative !important; padding-left: 20px !important;',
                'h3': 'font-size: 1.35rem !important; font-weight: 600 !important; color: var(--vp-c-brand-1) !important; margin: 24px 0 16px 0 !important; line-height: 1.3 !important; position: relative !important; padding-left: 16px !important;',
                'h4': 'font-size: 1.15rem !important; font-weight: 600 !important; color: var(--vp-c-text-1) !important; margin: 20px 0 14px 0 !important; line-height: 1.3 !important; position: relative !important; padding-left: 12px !important;',
                'h5': 'font-size: 1.05rem !important; font-weight: 600 !important; color: var(--vp-c-text-1) !important; margin: 18px 0 12px 0 !important; line-height: 1.3 !important;',
                'h6': 'font-size: 0.9rem !important; font-weight: 600 !important; color: var(--vp-c-text-2) !important; margin: 16px 0 10px 0 !important; line-height: 1.3 !important; text-transform: uppercase !important; letter-spacing: 0.1em !important;'
            };

            const style = styleMap[tag] || '';
            if (style) {
                token.attrPush(['style', style]);
            }

            return originalHeadingRender(tokens, idx, options, env, renderer);
        };

        // 自定义链接渲染
        const defaultLinkRender = this.md.renderer.rules.link_open || function(tokens, idx, options, env, renderer) {
            return renderer.renderToken(tokens, idx, options);
        };

        this.md.renderer.rules.link_open = function (tokens, idx, options, env, renderer) {
            const aIndex = tokens[idx].attrIndex('target');

            if (aIndex < 0) {
                tokens[idx].attrPush(['target', '_blank']);
            } else {
                tokens[idx].attrs[aIndex][1] = '_blank';
            }

            const relIndex = tokens[idx].attrIndex('rel');
            if (relIndex < 0) {
                tokens[idx].attrPush(['rel', 'noopener noreferrer']);
            } else {
                tokens[idx].attrs[relIndex][1] = 'noopener noreferrer';
            }

            return defaultLinkRender(tokens, idx, options, env, renderer);
        };

        // 自定义表格渲染
        this.md.renderer.rules.table_open = function() {
            return '<div class="table-container" style="margin: 20px 0; overflow-x: auto; border-radius: 12px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12); backdrop-filter: blur(10px); border: 1px solid var(--vp-c-divider-light);"><table class="markdown-table" style="width: 100% !important; border-collapse: collapse !important; background: linear-gradient(135deg, var(--vp-c-bg), var(--vp-c-bg-soft)) !important; font-size: 0.9rem !important; margin: 0 !important; border-radius: 12px !important; overflow: hidden !important;">\n';
        };

        this.md.renderer.rules.table_close = function() {
            return '</table></div>\n';
        };

        // 自定义表格头部和单元格渲染
        const originalThRender = this.md.renderer.rules.th_open || function(tokens, idx, options, env, renderer) {
            return renderer.renderToken(tokens, idx, options);
        };

        this.md.renderer.rules.th_open = function(tokens, idx, options, env, renderer) {
            const token = tokens[idx];
            token.attrPush(['style', 'padding: 14px 18px !important; text-align: left !important; border-bottom: 3px solid var(--vp-c-brand) !important; background: linear-gradient(135deg, var(--vp-c-brand-soft), var(--vp-c-brand)) !important; font-weight: 700 !important; color: white !important; text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important; position: relative !important;']);
            return originalThRender(tokens, idx, options, env, renderer);
        };

        const originalTdRender = this.md.renderer.rules.td_open || function(tokens, idx, options, env, renderer) {
            return renderer.renderToken(tokens, idx, options);
        };

        this.md.renderer.rules.td_open = function(tokens, idx, options, env, renderer) {
            const token = tokens[idx];
            token.attrPush(['style', 'padding: 12px 18px !important; text-align: left !important; border-bottom: 1px solid var(--vp-c-divider-light) !important; color: var(--vp-c-text-1) !important; transition: all 0.3s ease !important; position: relative !important;']);
            return originalTdRender(tokens, idx, options, env, renderer);
        };

        // 自定义代码块渲染
        const defaultCodeBlockRender = this.md.renderer.rules.code_block || function(tokens, idx, options, env, renderer) {
            return renderer.renderToken(tokens, idx, options);
        };

        this.md.renderer.rules.code_block = function(tokens, idx, options, env, renderer) {
            const token = tokens[idx];
            const langClass = token.info ? ` class="language-${token.info}"` : '';
            const lang = token.info || 'text';

            return `<div class="code-container" style="margin: 20px 0 !important; border-radius: 12px !important; overflow: hidden !important; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important; border: 1px solid var(--vp-c-divider-light) !important; background: linear-gradient(135deg, var(--vp-c-bg-alt), var(--vp-c-bg-mute)) !important;">
                <div class="code-header" style="background: linear-gradient(135deg, var(--vp-c-brand-soft), var(--vp-c-brand)) !important; padding: 8px 16px !important; font-size: 0.75rem !important; color: white !important; font-weight: 600 !important; text-transform: uppercase !important; letter-spacing: 0.1em !important;">${lang}</div>
                <pre${langClass} style="background: transparent !important; padding: 20px !important; margin: 0 !important; overflow-x: auto !important; font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important; font-size: 0.875rem !important; line-height: 1.6 !important; color: var(--vp-c-text-1) !important; border-left: 4px solid var(--vp-c-brand) !important;"><code>${token.content}</code></pre>
            </div>\n`;
        };

        // 自定义段落渲染
        const originalParagraphRender = this.md.renderer.rules.paragraph_open || function(tokens, idx, options, env, renderer) {
            return renderer.renderToken(tokens, idx, options);
        };

        this.md.renderer.rules.paragraph_open = function(tokens, idx, options, env, renderer) {
            const token = tokens[idx];
            token.attrPush(['style', 'margin: 12px 0 !important; line-height: 1.7 !important; color: var(--vp-c-text-1) !important;']);
            return originalParagraphRender(tokens, idx, options, env, renderer);
        };

        // 自定义行内代码渲染
        this.md.renderer.rules.code_inline = function(tokens, idx) {
            const token = tokens[idx];
            return `<code class="inline-code" style="background: var(--vp-c-bg-soft) !important; color: var(--vp-c-brand-1) !important; padding: 2px 6px !important; border-radius: 4px !important; font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important; font-size: 0.875em !important; font-weight: 500 !important; border: 1px solid var(--vp-c-divider-light) !important;">${token.content}</code>`;
        };

        // 自定义列表渲染
        const originalUlRender = this.md.renderer.rules.bullet_list_open || function(tokens, idx, options, env, renderer) {
            return renderer.renderToken(tokens, idx, options);
        };

        this.md.renderer.rules.bullet_list_open = function(tokens, idx, options, env, renderer) {
            const token = tokens[idx];
            token.attrPush(['style', 'margin: 16px 0 !important; padding-left: 24px !important;']);
            return originalUlRender(tokens, idx, options, env, renderer);
        };

        const originalOlRender = this.md.renderer.rules.ordered_list_open || function(tokens, idx, options, env, renderer) {
            return renderer.renderToken(tokens, idx, options);
        };

        this.md.renderer.rules.ordered_list_open = function(tokens, idx, options, env, renderer) {
            const token = tokens[idx];
            token.attrPush(['style', 'margin: 16px 0 !important; padding-left: 24px !important;']);
            return originalOlRender(tokens, idx, options, env, renderer);
        };

        const originalLiRender = this.md.renderer.rules.list_item_open || function(tokens, idx, options, env, renderer) {
            return renderer.renderToken(tokens, idx, options);
        };

        this.md.renderer.rules.list_item_open = function(tokens, idx, options, env, renderer) {
            const token = tokens[idx];
            token.attrPush(['style', 'margin: 8px 0 !important; line-height: 1.6 !important;']);
            return originalLiRender(tokens, idx, options, env, renderer);
        };

        // 自定义图片渲染
        this.md.renderer.rules.image = function(tokens, idx) {
            const token = tokens[idx];
            const src = token.attrs[token.attrIndex('src')][1];
            const alt = token.content;
            const title = token.attrs && token.attrs[token.attrIndex('title')]
                ? token.attrs[token.attrIndex('title')][1]
                : '';

            return `<div class="image-container" style="margin: 20px 0; text-align: center;">
                <img src="${src}" alt="${alt}" title="${title}" class="markdown-image" loading="lazy" style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); transition: all 0.3s ease;">
                ${title ? `<div class="image-caption" style="margin-top: 8px; font-size: 0.875rem; color: var(--vp-c-text-2); font-style: italic;">${title}</div>` : ''}
            </div>`;
        };
    }

    /**
     * 解析 markdown 文本
     */
    parse(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }

        try {
            return this.md.render(text);
        } catch (error) {
            console.error('Markdown parsing error:', error);
            // 降级处理：返回原始文本
            return `<pre>${text}</pre>`;
        }
    }

    /**
     * 解析行内 markdown
     */
    parseInline(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }

        try {
            return this.md.renderInline(text);
        } catch (error) {
            console.error('Inline markdown parsing error:', error);
            return text;
        }
    }

    /**
     * 获取 markdown-it 实例（用于高级自定义）
     */
    getInstance() {
        return this.md;
    }

    /**
     * 添加自定义插件
     */
    use(plugin, options) {
        if (this.md) {
            this.md.use(plugin, options);
        }
        return this;
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.md = null;
    }
}

// 创建单例实例
export const markdownService = new TopmeansMarkdownService();