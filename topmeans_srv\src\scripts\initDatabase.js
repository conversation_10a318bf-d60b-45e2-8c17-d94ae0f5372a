const orderService = require('../services/orderService');
const logger = require('../log/logger');

async function initDatabase() {
  try {
    logger.info('开始初始化数据库...');
    
    // 初始化订单表
    const result = await orderService.initOrderTable();
    
    if (result.success) {
      logger.info('数据库初始化成功');
      console.log('✅ 数据库初始化成功');
    } else {
      logger.error('数据库初始化失败:', result.message);
      console.error('❌ 数据库初始化失败:', result.message);
    }
    
    process.exit(0);
  } catch (error) {
    logger.error('数据库初始化异常:', error);
    console.error('❌ 数据库初始化异常:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

module.exports = { initDatabase };
