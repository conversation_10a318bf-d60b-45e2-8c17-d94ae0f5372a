import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { TopmeansMapService } from '@services/TopmeansMapService'

describe('TopmeansMapService', () => {
  let mapService
  let mockFetch

  beforeEach(() => {
    mapService = new TopmeansMapService()
    mockFetch = vi.fn()
    global.fetch = mockFetch

    // Mock global AMap
    global.AMap = {
      Map: vi.fn(),
      Driving: vi.fn(),
      Marker: vi.fn(),
      LngLat: vi.fn()
    }

    // Mock window._AMapSecurityConfig
    global.window = {
      _AMapSecurityConfig: {},
      innerHeight: 800,
      document: {
        createElement: vi.fn((tag) => {
          const element = {
            src: '',
            onload: vi.fn(),
            onerror: vi.fn(),
            id: '',
            className: '',
            style: {
              display: '',
              position: '',
              top: '',
              left: '',
              width: '',
              height: '',
              background: '',
              borderColor: '',
              boxShadow: '',
              cssText: '',
              setProperty: vi.fn(),
              removeProperty: vi.fn()
            },
            classList: {
              add: vi.fn(),
              remove: vi.fn(),
              contains: vi.fn()
            },
            getBoundingClientRect: vi.fn(() => ({
              top: 100,
              left: 200,
              width: 300,
              height: 50,
              bottom: 150
            })),
            querySelectorAll: vi.fn(() => []),
            querySelector: vi.fn(),
            addEventListener: vi.fn(),
            removeEventListener: vi.fn(),
            blur: vi.fn(),
            focus: vi.fn(),
            appendChild: vi.fn(),
            innerHTML: ''
          }
          return element
        }),
        head: {
          appendChild: vi.fn()
        },
        getElementById: vi.fn(),
        body: {
          appendChild: vi.fn()
        }
      }
    }
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with null AMap and empty mapInstances', () => {
      expect(mapService.AMap).toBeNull()
      expect(mapService.mapInstances).toEqual([])
    })
  })

  describe('initialize', () => {
    it('should initialize map service successfully', async () => {
      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({
          AMAP_CODE: 'test_security_code',
          AMAP_KEY: 'test_api_key'
        })
      }
      mockFetch.mockResolvedValue(mockResponse)

      // Mock AMap script loading
      const mockAMap = {
        plugin: vi.fn(),
        PlaceSearch: vi.fn(),
        Geocoder: vi.fn()
      }
      vi.spyOn(mapService, 'loadAMapScript').mockResolvedValue(mockAMap)

      const result = await mapService.initialize()

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/amap_keys', {
        method: 'GET',
        credentials: 'include'
      })
      expect(window._AMapSecurityConfig.securityJsCode).toBe('test_security_code')
      expect(result).toBe(mockAMap)
    })

    it('should throw error when API keys fetch fails', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      await expect(mapService.initialize()).rejects.toThrow('Network error')
    })

    it('should throw error when response is not ok', async () => {
      const mockResponse = { ok: false }
      mockFetch.mockResolvedValue(mockResponse)

      await expect(mapService.initialize()).rejects.toThrow('获取API密钥失败，请检查网络连接')
    })

    it('should handle server-side rendering', async () => {
      const originalWindow = global.window
      delete global.window

      const result = await mapService.initialize()

      expect(result).toBeUndefined()

      global.window = originalWindow
    })
  })

  describe('setupAutoComplete', () => {
    it('should setup auto complete when AMap is initialized', () => {
      const mockAMap = {
        plugin: vi.fn(),
        PlaceSearch: vi.fn()
      }
      mapService.AMap = mockAMap

      const onStartSelect = vi.fn()
      const onEndSelect = vi.fn()

      vi.spyOn(mapService, 'createCustomAutoComplete')

      mapService.setupAutoComplete('start-input', 'end-input', onStartSelect, onEndSelect)

      expect(mapService.createCustomAutoComplete).toHaveBeenCalledWith('start-input', onStartSelect)
      expect(mapService.createCustomAutoComplete).toHaveBeenCalledWith('end-input', onEndSelect)
    })

    it('should throw error when AMap is not initialized', () => {
      expect(() => {
        mapService.setupAutoComplete('start-input', 'end-input', vi.fn(), vi.fn())
      }).toThrow('地图服务未初始化')
    })
  })

  describe('createCustomAutoComplete', () => {
    it('should handle missing input element', () => {
      const mockAMap = {
        plugin: vi.fn(),
        PlaceSearch: vi.fn()
      }
      mapService.AMap = mockAMap

      window.document.getElementById.mockReturnValue(null)

      expect(() => {
        mapService.createCustomAutoComplete('non-existent-input', vi.fn())
      }).not.toThrow()
    })

    it('should handle AMap not initialized', () => {
      mapService.AMap = null

      // Mock a valid input element so the method reaches the AMap.plugin call
      const mockInput = {
        addEventListener: vi.fn(),
        value: '',
        getBoundingClientRect: vi.fn(() => ({
          top: 100,
          left: 200,
          width: 300,
          height: 50
        }))
      }
      window.document.getElementById.mockReturnValue(mockInput)

      // The method should not throw when AMap is null, it should just return early
      expect(() => {
        mapService.createCustomAutoComplete('test-input', vi.fn())
      }).not.toThrow()
    })
  })

  describe('createSuggestContainer', () => {
    it('should handle missing input element', () => {
      window.document.getElementById.mockReturnValue(null)

      expect(() => {
        mapService.createSuggestContainer('non-existent-input')
      }).not.toThrow()
    })
  })

  describe('positionSuggestContainer', () => {
    it('should position suggestion container correctly', () => {
      const mockInput = {
        getBoundingClientRect: vi.fn(() => ({
          top: 100,
          left: 200,
          width: 300,
          height: 50,
          bottom: 150
        }))
      }

      const mockContainer = {
        style: {
          top: '',
          left: '',
          width: '',
          maxHeight: ''
        }
      }

      mapService.positionSuggestContainer(mockContainer, mockInput)

      expect(mockContainer.style.left).toBe('200px')
      expect(mockContainer.style.width).toBe('300px')
    })
  })

  describe('searchPlaces', () => {
    it('should search places and show suggestions', async () => {
      const mockPlaceSearch = {
        search: vi.fn((query, callback) => {
          callback('complete', { 
            poiList: { 
              pois: [{ name: '测试地点', address: '测试地址' }] 
            } 
          })
        })
      }

      const mockContainer = {
        style: {
          display: 'none',
          setProperty: vi.fn()
        },
        innerHTML: '',
        querySelectorAll: vi.fn().mockReturnValue([]),
        appendChild: vi.fn()
      }
      const onSelect = vi.fn()
      const mockInput = { 
        value: 'test',
        getBoundingClientRect: vi.fn(() => ({
          top: 100,
          left: 200,
          width: 300,
          height: 50,
          bottom: 150
        }))
      }

      vi.spyOn(mapService, 'showSuggestions')

      await mapService.searchPlaces(mockPlaceSearch, '测试', mockContainer, onSelect, mockInput)

      expect(mockPlaceSearch.search).toHaveBeenCalledWith('测试', expect.any(Function))
      expect(mapService.showSuggestions).toHaveBeenCalled()
    })

    it('should handle search error', async () => {
      const mockPlaceSearch = {
        search: vi.fn((query, callback) => {
          callback('error', { poiList: { pois: [] } })
        })
      }

      const mockContainer = {
        style: {
          display: 'block'
        },
        innerHTML: 'some content'
      }
      const onSelect = vi.fn()
      const mockInput = { value: 'test' }

      vi.spyOn(mapService, 'hideSuggestions')

      await mapService.searchPlaces(mockPlaceSearch, '测试', mockContainer, onSelect, mockInput)

      expect(mapService.hideSuggestions).toHaveBeenCalledWith(mockContainer)
    })
  })

  describe('showSuggestions', () => {
    it('should show suggestions in container', () => {
      const mockContainer = {
        innerHTML: '',
        style: { 
          display: 'none',
          setProperty: vi.fn()
        },
        appendChild: vi.fn(),
        querySelectorAll: vi.fn().mockReturnValue([])
      }

      const pois = [
        { name: '地点1', address: '地址1' },
        { name: '地点2', address: '地址2' }
      ]

      const onSelect = vi.fn()
      const mockInput = { 
        value: 'test',
        getBoundingClientRect: vi.fn(() => ({
          top: 100,
          left: 200,
          width: 300,
          height: 50,
          bottom: 150
        }))
      }

      mapService.showSuggestions(mockContainer, pois, onSelect, mockInput)

      expect(mockContainer.style.display).toBe('block')
      expect(mockContainer.appendChild).toHaveBeenCalled()
    })
  })

  describe('hideSuggestions', () => {
    it('should hide suggestions container', () => {
      const mockContainer = {
        style: { display: 'block' }
      }

      mapService.hideSuggestions(mockContainer)

      expect(mockContainer.style.display).toBe('none')
    })
  })

  describe('updateSelection', () => {
    it('should update selection in suggestion items', () => {
      const mockParentNode = {
        querySelectorAll: vi.fn().mockReturnValue([
          { 
            classList: { remove: vi.fn(), add: vi.fn() },
            style: { removeProperty: vi.fn() }
          },
          { 
            classList: { remove: vi.fn(), add: vi.fn() },
            style: { removeProperty: vi.fn() }
          }
        ])
      }

      const mockItems = [
        { 
          parentNode: mockParentNode,
          classList: { add: vi.fn() },
          scrollIntoView: vi.fn()
        },
        { 
          parentNode: mockParentNode,
          classList: { add: vi.fn() },
          scrollIntoView: vi.fn()
        }
      ]

      mapService.updateSelection(mockItems, 1)

      expect(mockParentNode.querySelectorAll).toHaveBeenCalledWith('.custom-suggest-item')
      expect(mockItems[1].classList.add).toHaveBeenCalledWith('selected')
    })
  })

  describe('clearSelection', () => {
    it('should clear selection from all items', () => {
      const mockContainer = {
        querySelectorAll: vi.fn().mockReturnValue([
          { 
            classList: { remove: vi.fn() },
            style: { removeProperty: vi.fn() }
          },
          { 
            classList: { remove: vi.fn() },
            style: { removeProperty: vi.fn() }
          }
        ])
      }

      mapService.clearSelection(mockContainer)

      expect(mockContainer.querySelectorAll).toHaveBeenCalledWith('.custom-suggest-item')
    })
  })

  describe('selectSuggestion', () => {
    it('should select suggestion and call onSelect', () => {
      const mockItem = { 
        querySelector: vi.fn((selector) => {
          if (selector === 'div:first-child') {
            return { textContent: '测试地点' }
          }
          if (selector === 'div:last-child') {
            return { textContent: '测试地址' }
          }
          return null
        })
      }
      const onSelect = vi.fn()
      const mockInput = { 
        value: '',
        blur: vi.fn()
      }
      const mockContainer = { style: { display: 'block' } }

      const poi = {
        name: '测试地点',
        address: '测试地址',
        location: { lng: 116.397, lat: 39.916 }
      }

      mapService.selectSuggestion(mockItem, onSelect, mockInput, mockContainer, poi)

      expect(mockInput.value).toBe('测试地点')
      expect(mockContainer.style.display).toBe('none')
      expect(onSelect).toHaveBeenCalledWith('测试地点', expect.any(Object))
    })
  })

  describe('updateThemeStyles', () => {
    it('should update theme styles for suggestion container', () => {
      const mockContainer = {
        style: {
          setProperty: vi.fn()
        },
        querySelectorAll: vi.fn().mockReturnValue([
          { 
            querySelector: vi.fn().mockReturnValue({ style: { setProperty: vi.fn() } }),
            style: { setProperty: vi.fn() } 
          },
          { 
            querySelector: vi.fn().mockReturnValue({ style: { setProperty: vi.fn() } }),
            style: { setProperty: vi.fn() } 
          }
        ])
      }

      // Mock theme detection
      const mockDocumentElement = {
        classList: {
          contains: vi.fn().mockReturnValue(true) // dark theme
        }
      }
      window.document.documentElement = mockDocumentElement

      mapService.updateThemeStyles(mockContainer)

      expect(mockContainer.style.setProperty).toHaveBeenCalled()
    })
  })

  describe('extractLocationInfo', () => {
    it('should extract location info from POI', () => {
      const poi = {
        name: '测试地点',
        address: '北京市东城区天安门广场',
        location: { lng: 116.397, lat: 39.916 },
        pname: '北京市',
        cityname: '北京市',
        adname: '东城区'
      }

      const result = mapService.extractLocationInfo(poi)

      expect(result).toEqual({
        lng: 116.397,
        lat: 39.916,
        province: '北京市',
        city: null,
        district: '东城区',
        address: '北京市东城区天安门广场',
        adcode: null
      })
    })

    it('should handle POI without location', () => {
      const poi = {
        name: '测试地点',
        address: '北京市东城区天安门广场'
      }

      const result = mapService.extractLocationInfo(poi)

      expect(result.lng).toBeNull()
      expect(result.lat).toBeNull()
    })
  })

  describe('parseAddress', () => {
    it('should parse address components', () => {
      const address = '北京市东城区天安门广场'

      const result = mapService.parseAddress(address)

      expect(result.province).toBe('北京市')
      expect(result.city).toBe(null)
      expect(result.district).toBe('东城区')
    })

    it('should handle simple address', () => {
      const address = '北京市'

      const result = mapService.parseAddress(address)

      expect(result.province).toBe('北京市')
      expect(result.city).toBe(null)
      expect(result.district).toBe(null)
    })
  })

  describe('loadAMapScript', () => {
    it('should handle AMap already loaded', async () => {
      // Mock window.AMap as already loaded
      window.AMap = { test: 'amap' }

      const result = await mapService.loadAMapScript('test_key')

      expect(result).toBe(window.AMap)
    })

    it('should handle fetch error', async () => {
      // Mock window.AMap as undefined initially
      delete window.AMap

      mockFetch.mockRejectedValue(new Error('Network error'))

      await expect(mapService.loadAMapScript('test_key')).rejects.toThrow('地图脚本加载失败，请检查网络连接')
    })
  })

  describe('getGeocodePosition', () => {
    it('should get geocode position successfully', async () => {
      const mockGeocoder = {
        getLocation: vi.fn((address, callback) => {
          callback('complete', { 
            geocodes: [{ 
              location: { lng: 116.397, lat: 39.916 } 
            }] 
          })
        })
      }

      const result = await mapService.getGeocodePosition(mockGeocoder, '北京市东城区天安门广场')

      expect(mockGeocoder.getLocation).toHaveBeenCalledWith('北京市东城区天安门广场', expect.any(Function))
      expect(result).toEqual({ lng: 116.397, lat: 39.916 })
    })

    it('should handle geocoding error', async () => {
      const mockGeocoder = {
        getLocation: vi.fn((address, callback) => {
          callback('error', { geocodes: [] })
        })
      }

      await expect(mapService.getGeocodePosition(mockGeocoder, '无效地址')).rejects.toThrow('无法解析地址: 无效地址')
    })
  })

  describe('initMap', () => {
    it('should initialize map successfully', async () => {
      const mockAMap = {
        Map: vi.fn(() => ({
          setFitView: vi.fn(),
          add: vi.fn(),
          on: vi.fn()
        })),
        Driving: vi.fn(() => ({
          search: vi.fn((start, end, options, callback) => {
            callback('complete', { routes: [] })
          }),
          on: vi.fn()
        })),
        Marker: vi.fn(() => ({
          setMap: vi.fn()
        })),
        LngLat: vi.fn()
      }
      mapService.AMap = mockAMap

      const start = { s_lng: 116.397, s_lat: 39.916 }
      const end = { e_lng: 121.473, e_lat: 31.230 }

      const result = await mapService.initMap(0, start, end, 0, [])

      expect(mockAMap.Map).toHaveBeenCalled()
      expect(mockAMap.Driving).toHaveBeenCalled()
      expect(result).toBeDefined()
    })
  })

  describe('drivingPlanning', () => {
    it('should handle missing map container', async () => {
      // Mock DOM element for map container to return null
      window.document.getElementById.mockReturnValue(null)

      const start = { s_lng: 116.397, s_lat: 39.916 }
      const end = { e_lng: 121.473, e_lat: 31.230 }

      await expect(mapService.drivingPlanning(0, start, end, 0, [])).rejects.toThrow('地图容器未找到')
    })
  })

  describe('saveMapAsImage', () => {
    it('should handle missing map container', async () => {
      // Mock DOM element for map container to return null
      window.document.getElementById.mockReturnValue(null)

      await expect(mapService.saveMapAsImage(0, 'testuser', '2024-01-01')).rejects.toThrow('地图容器未找到')
    })
  })

  describe('destroyMapInstance', () => {
    it('should destroy map instance', () => {
      const mockMap = {
        destroy: vi.fn()
      }
      mapService.mapInstances[0] = mockMap

      mapService.destroyMapInstance(0)

      expect(mockMap.destroy).toHaveBeenCalled()
      expect(mapService.mapInstances[0]).toBeNull()
    })
  })

  describe('cleanup', () => {
    it('should cleanup all map instances', () => {
      const mockMap1 = { destroy: vi.fn() }
      const mockMap2 = { destroy: vi.fn() }
      mapService.mapInstances = [mockMap1, mockMap2]

      mapService.cleanup()

      expect(mockMap1.destroy).toHaveBeenCalled()
      expect(mockMap2.destroy).toHaveBeenCalled()
      expect(mapService.mapInstances).toEqual([])
      expect(mapService.AMap).toBeNull()
    })
  })

  describe('getAccurateCoordinates', () => {
    it('should get accurate coordinates successfully', async () => {
      const mockAMap = {
        plugin: vi.fn((pluginName, callback) => callback()),
        Geocoder: vi.fn(() => ({
          getLocation: vi.fn((address, callback) => {
            callback('complete', {
              geocodes: [{
                location: { lng: 116.397, lat: 39.916 }
              }]
            })
          })
        }))
      }
      mapService.AMap = mockAMap

      const result = await mapService.getAccurateCoordinates('天安门', '北京市', '北京市')

      expect(mockAMap.plugin).toHaveBeenCalledWith('AMap.Geocoder', expect.any(Function))
      expect(result).toEqual({ lng: 116.397, lat: 39.916 })
    })

    it('should handle geocoding failure', async () => {
      const mockAMap = {
        plugin: vi.fn((pluginName, callback) => callback()),
        Geocoder: vi.fn(() => ({
          getLocation: vi.fn((address, callback) => {
            callback('error', { geocodes: [] })
          })
        }))
      }
      mapService.AMap = mockAMap

      const result = await mapService.getAccurateCoordinates('InvalidAddress', '北京市', '北京市')

      expect(result).toEqual({})
    })
  })

  describe('calculateDistance', () => {
    it('should calculate distance between two points', () => {
      const lng1 = 116.397
      const lat1 = 39.916
      const lng2 = 121.473
      const lat2 = 31.230

      const result = mapService.calculateDistance(lng1, lat1, lng2, lat2)

      expect(typeof result).toBe('number')
      expect(result).toBeGreaterThan(0)
    })
  })

  describe('getBatchAccurateCoordinates', () => {
    it('should get batch coordinates successfully', async () => {
      const addresses = ['北京', '上海', '广州']
      
      const mockAMap = {
        plugin: vi.fn((pluginName, callback) => callback()),
        Geocoder: vi.fn(() => ({
          getLocation: vi.fn((address, callback) => {
            callback('complete', {
              geocodes: [{
                location: { lng: 116.397, lat: 39.916 }
              }]
            })
          })
        }))
      }
      mapService.AMap = mockAMap

      const result = await mapService.getBatchAccurateCoordinates(addresses)

      expect(result).toHaveLength(3)
      expect(mockAMap.plugin).toHaveBeenCalled()
    })
  })
}) 