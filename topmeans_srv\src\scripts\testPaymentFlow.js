require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:3999';

async function testPaymentFlow() {
  try {
    console.log('🚀 开始测试支付流程...\n');

    // 1. 测试创建支付订单
    console.log('1️⃣ 测试创建支付订单');
    const createPaymentResponse = await axios.post(`${BASE_URL}/api/payment/create`, {
      amount: 1,
      subject: 'TopMeans测试订单',
      goods: {
        type: 'single_service',
        name: '高级服务',
        description: '提供专业的一对一服务'
      },
      userId: 1,
      userAccount: '<EMAIL>'
    });

    console.log('✅ 创建支付订单成功');
    console.log('订单ID:', createPaymentResponse.data.orderId);
    console.log('支付URL长度:', createPaymentResponse.data.paymentUrl?.length || 0);
    console.log('是否包含表单:', createPaymentResponse.data.paymentUrl?.includes('<form') ? '是' : '否');

    const orderId = createPaymentResponse.data.orderId;

    // 2. 测试查询订单状态
    console.log('\n2️⃣ 测试查询订单状态');
    const statusResponse = await axios.get(`${BASE_URL}/api/payment/status/${orderId}`);
    
    console.log('✅ 查询订单状态成功');
    console.log('订单状态:', statusResponse.data.status);
    console.log('订单金额:', statusResponse.data.amount);

    // 3. 测试取消订单
    console.log('\n3️⃣ 测试取消订单');
    const cancelResponse = await axios.post(`${BASE_URL}/api/payment/cancel/${orderId}`);
    
    console.log('✅ 取消订单成功');
    console.log('取消结果:', cancelResponse.data.message);

    // 4. 再次查询订单状态
    console.log('\n4️⃣ 再次查询订单状态');
    const finalStatusResponse = await axios.get(`${BASE_URL}/api/payment/status/${orderId}`);
    
    console.log('✅ 查询最终状态成功');
    console.log('最终状态:', finalStatusResponse.data.status);

    console.log('\n🎉 支付流程测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testPaymentFlow();
