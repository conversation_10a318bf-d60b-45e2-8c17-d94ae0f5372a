import { defineConfig } from 'vitepress';

export default defineConfig(async () => {
  // 准备Vite插件数组
  const vitePlugins = [];
  
  // 只在生产环境启用代码混淆
  if (process.env.NODE_ENV === 'production') {
    // 动态导入混淆插件（兼容ESM）
    const { default: obfuscatorPlugin } = await import('vite-plugin-javascript-obfuscator');
    
    vitePlugins.push(
      obfuscatorPlugin({
        options: {
          compact: true,                // 基本压缩，内存消耗低
          controlFlowFlattening: false,  // 禁用 - 最耗内存的选项
          deadCodeInjection: false,      // 禁用 - 高内存消耗
          debugProtection: false,         // 禁用 - 不需要
          identifierNamesGenerator: 'hexadecimal', // 基本重命名
          log: false,
          renameGlobals: true,           // 全局变量重命名
          selfDefending: false,          // 禁用 - 节省内存
          simplify: true,                // 简单优化
          splitStrings: false,           // 禁用 - 节省内存
          stringArray: true,             // 保留基本字符串混淆
          stringArrayThreshold: 0.1,     // 很低的阈值
          stringArrayEncoding: [],       // 禁用编码 - 节省内存
          stringArrayIndexShift: false,  // 禁用
          stringArrayRotate: false,      // 禁用
          stringArrayShuffle: false,     // 禁用
          transformObjectKeys: false,    // 禁用对象键转换
          unicodeEscapeSequence: false   // 禁用
        }
      })
    );
  }

  return {
    title: "TopMeansLab",
    description: "TopMeansLab Documentation",
    head: [
      ['link', { rel: 'icon', href: '/favicon.ico' }],
      ['link', { rel: 'shortcut icon', href: '/favicon.ico' }],
      ['link', { rel: 'apple-touch-icon', href: '/favicon.ico' }],
      // Google Analytics
      ['script', { async: true, src: 'https://www.googletagmanager.com/gtag/js?id=G-XRPE3VXBG1' }],
      ['script', {}, `
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-XRPE3VXBG1');
      `]
    ],
    themeConfig: {
      nav: [
        { text: '首页', link: '/' },
        { text: '用户中心', link: '/user-center/profile' },
        { text: '服务购买', link: '/service-purchase/' },
        { text: 'ShowCase', link: '/showcase/' }
      ],
      sidebar: {
        '/user-center/': [
          {
            text: '用户中心',
            items: [
              { text: '登录/注册', link: '/user-center/login' },
              { text: '个人主页', link: '/user-center/profile' }
            ]
          }
        ],
        '/service-purchase/': [
          {
            text: '服务购买',
            items: [
              { text: '套餐选择', link: '/service-purchase/' }
            ]
          }
        ],
        '/showcase/': [
          {
            text: 'ShowCase',
            items: [
              { text: 'Tour Planning Examples', link: '/showcase/' }
            ]
          }
        ]
      }
    },
    vite: {
      plugins: vitePlugins,
      ssr: {
        noExternal: ['element-plus']
      },
      server: {
        host: '0.0.0.0',  // 允许所有IP访问
        port: 5173,       // 指定端口
        strictPort: true, // 端口被占用时直接退出
        hmr: {
          host: 'localhost' // 热更新主机
        }
      }
    }
  };
});