/**
 * 服务模块统一导出
 * 提供所有Topmeans相关服务的统一入口
 */

// 导入并重新导出所有服务类和实例
import { mapService, TopmeansMapService } from './TopmeansMapService.js';
import { scrollManager, TopmeansScrollManager } from './TopmeansScrollManager.js';
import { formManager, TopmeansFormManager } from './TopmeansFormManager.js';
import { apiService, TopmeansApiService } from './TopmeansApiService.js';
import { styleManager, TopmeansStyleManager } from './TopmeansStyleManager.js';
import { markdownService, TopmeansMarkdownService } from './TopmeansMarkdownService.js';

// 重新导出所有服务
export {
    mapService, TopmeansMapService,
    scrollManager, TopmeansScrollManager,
    formManager, TopmeansFormManager,
    apiService, TopmeansApiService,
    styleManager, TopmeansStyleManager,
    markdownService, TopmeansMarkdownService
};

/**
 * 服务管理器 - 统一管理所有服务的生命周期
 */
export class TopmeansServiceManager {
    constructor() {
        this.services = {
            map: null,
            scroll: null,
            form: null,
            api: null,
            style: null
        };
        this.initialized = false;
    }

    /**
     * 初始化所有服务
     */
    async initialize() {
        if (this.initialized) return;

        try {
            // 使用已经静态导入的服务实例
            this.services.map = mapService;
            this.services.scroll = scrollManager;
            this.services.form = formManager;
            this.services.api = apiService;
            this.services.style = styleManager;

            // 初始化各个服务
            if (this.services.map && typeof this.services.map.initialize === 'function') {
                await this.services.map.initialize();
            }
            if (this.services.style && typeof this.services.style.initialize === 'function') {
                await this.services.style.initialize();
            }

            this.initialized = true;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 获取地图服务
     */
    getMapService() {
        return this.services.map;
    }

    /**
     * 获取滚动管理器
     */
    getScrollManager() {
        return this.services.scroll;
    }

    /**
     * 获取表单管理器
     */
    getFormManager() {
        return this.services.form;
    }

    /**
     * 获取API服务
     */
    getApiService() {
        return this.services.api;
    }

    /**
     * 获取样式管理器
     */
    getStyleManager() {
        return this.services.style;
    }

    /**
     * 清理所有服务
     */
    cleanup() {
        Object.values(this.services).forEach(service => {
            if (service && typeof service.cleanup === 'function') {
                service.cleanup();
            }
        });

        this.services = {
            map: null,
            scroll: null,
            form: null,
            api: null,
            style: null
        };

        this.initialized = false;
    }

    /**
     * 重新初始化所有服务
     */
    async reinitialize() {
        this.cleanup();
        await this.initialize();
    }

    /**
     * 检查服务是否已初始化
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * 获取所有服务的状态
     */
    getServicesStatus() {
        return {
            initialized: this.initialized,
            services: {
                map: !!this.services.map,
                scroll: !!this.services.scroll,
                form: !!this.services.form,
                api: !!this.services.api,
                style: !!this.services.style
            }
        };
    }
}

// 创建服务管理器单例
export const serviceManager = new TopmeansServiceManager();

/**
 * 便捷的初始化函数
 */
export async function initializeTopmeansServices() {
    await serviceManager.initialize();
    return serviceManager;
}

/**
 * 便捷的清理函数
 */
export function cleanupTopmeansServices() {
    serviceManager.cleanup();
}

/**
 * 获取所有服务实例的便捷函数
 */
export function getTopmeansServices() {
    if (!serviceManager.isInitialized()) {
        throw new Error('服务管理器未初始化，请先调用 initializeTopmeansServices()');
    }

    return {
        mapService: serviceManager.getMapService(),
        scrollManager: serviceManager.getScrollManager(),
        formManager: serviceManager.getFormManager(),
        apiService: serviceManager.getApiService(),
        styleManager: serviceManager.getStyleManager()
    };
}
