import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { TopmeansScrollManager } from '@services/TopmeansScrollManager'

describe('TopmeansScrollManager', () => {
  let scrollManager
  let mockWindow

  beforeEach(() => {
    scrollManager = new TopmeansScrollManager()
    
    // Mock window object
    mockWindow = {
      pageYOffset: 0,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      scrollTo: vi.fn(),
      setTimeout: vi.fn((callback, delay) => {
        setTimeout(callback, delay)
        return 123 // mock timeout ID
      }),
      clearTimeout: vi.fn(),
      dispatchEvent: vi.fn()
    }
    
    global.window = mockWindow
    global.document = {
      documentElement: {
        scrollTop: 0
      },
      body: {
        scrollTop: 0,
        scrollHeight: 1000
      }
    }
    
    // Mock global clearTimeout
    global.clearTimeout = vi.fn()
  })

  afterEach(() => {
    vi.clearAllMocks()
    vi.clearAllTimers()
  })

  describe('constructor', () => {
    it('should initialize with correct default values', () => {
      expect(scrollManager.autoScrollEnabled).toBe(true)
      expect(scrollManager.userScrollTimeout).toBeNull()
      expect(scrollManager.lastScrollTop).toBe(0)
      expect(scrollManager.isUserScrolling).toBe(false)
      expect(scrollManager.scrollObserver).toBeNull()
      expect(scrollManager.handleUserScroll).toBeNull()
      expect(scrollManager.lastUserScrollTime).toBe(0)
      expect(scrollManager.scrollVelocity).toBe(0)
      expect(scrollManager.consecutiveScrollCount).toBe(0)
    })
  })

  describe('initScrollListener', () => {
    it('should initialize scroll listener when window is available', () => {
      scrollManager.initScrollListener()

      expect(mockWindow.addEventListener).toHaveBeenCalledWith('scroll', expect.any(Function), { passive: true })
    })

    it('should not initialize when window is undefined', () => {
      const originalWindow = global.window
      delete global.window

      expect(() => scrollManager.initScrollListener()).not.toThrow()

      global.window = originalWindow
    })

    it('should set handleUserScroll function', () => {
      scrollManager.initScrollListener()

      expect(scrollManager.handleUserScroll).toBeDefined()
      expect(typeof scrollManager.handleUserScroll).toBe('function')
    })
  })

  describe('cleanupScrollListener', () => {
    it('should remove scroll listener when handleUserScroll exists', () => {
      scrollManager.handleUserScroll = vi.fn()
      scrollManager.initScrollListener()

      scrollManager.cleanupScrollListener()

      expect(mockWindow.removeEventListener).toHaveBeenCalledWith('scroll', expect.any(Function))
    })

    it('should clear user scroll timeout', () => {
      scrollManager.userScrollTimeout = 123
      scrollManager.cleanupScrollListener()

      expect(global.clearTimeout).toHaveBeenCalledWith(123)
      // Note: actual implementation doesn't set userScrollTimeout to null in cleanupScrollListener
      expect(scrollManager.userScrollTimeout).toBe(123)
    })

    it('should handle when handleUserScroll is null', () => {
      scrollManager.handleUserScroll = null

      expect(() => scrollManager.cleanupScrollListener()).not.toThrow()
    })
  })

  describe('throttle', () => {
    it('should throttle function calls', () => {
      const func = vi.fn()
      const throttledFunc = scrollManager.throttle(func, 100)

      // Call multiple times quickly
      throttledFunc()
      throttledFunc()
      throttledFunc()

      expect(func).toHaveBeenCalledTimes(1)
    })

    it('should allow function calls after throttle period', async () => {
      const func = vi.fn()
      const throttledFunc = scrollManager.throttle(func, 50)

      throttledFunc()
      
      // Wait for throttle period
      await new Promise(resolve => setTimeout(resolve, 60))
      
      throttledFunc()

      expect(func).toHaveBeenCalledTimes(2)
    })
  })

  describe('setContentCompletedChecker', () => {
    it('should set content completed checker function', () => {
      const checkerFunction = vi.fn()
      
      scrollManager.setContentCompletedChecker(checkerFunction)
      
      expect(scrollManager.checkAllContentCompleted).toBe(checkerFunction)
    })
  })

  describe('setDisplayStateChecker', () => {
    it('should set display state checker function', () => {
      const checkerFunction = vi.fn()
      
      scrollManager.setDisplayStateChecker(checkerFunction)
      
      expect(scrollManager.getDisplayState).toBe(checkerFunction)
    })
  })

  describe('checkAllContentCompleted', () => {
    it('should return true when content completed checker returns true', () => {
      const checkerFunction = vi.fn().mockReturnValue(true)
      scrollManager.setContentCompletedChecker(checkerFunction)

      const result = scrollManager.checkAllContentCompleted()

      expect(result).toBe(true)
      expect(checkerFunction).toHaveBeenCalled()
    })

    it('should return false when content completed checker returns false', () => {
      const checkerFunction = vi.fn().mockReturnValue(false)
      scrollManager.setContentCompletedChecker(checkerFunction)

      const result = scrollManager.checkAllContentCompleted()

      expect(result).toBe(false)
    })

    it('should return false when no checker is set', () => {
      const result = scrollManager.checkAllContentCompleted()

      expect(result).toBe(false)
    })
  })

  describe('getDisplayState', () => {
    it('should return display state when checker is set', () => {
      const mockState = { visible: true, completed: false }
      const checkerFunction = vi.fn().mockReturnValue(mockState)
      scrollManager.setDisplayStateChecker(checkerFunction)

      const result = scrollManager.getDisplayState()

      expect(result).toBe(mockState)
      expect(checkerFunction).toHaveBeenCalled()
    })

    it('should return default state when no checker is set', () => {
      const result = scrollManager.getDisplayState()

      expect(result).toEqual({
        selectedDayIndex: -1,
        isAnyLoading: false
      })
    })
  })

  describe('smartScrollToContent', () => {
    it('should scroll to content when auto scroll is enabled', () => {
      scrollManager.autoScrollEnabled = true
      const mockState = { visible: true, completed: false }
      const checkerFunction = vi.fn().mockReturnValue(mockState)
      scrollManager.setDisplayStateChecker(checkerFunction)

      const smartScrollToContentSpy = vi.spyOn(scrollManager, 'smartScrollToContent')

      scrollManager.smartScrollToContent()

      expect(smartScrollToContentSpy).toHaveBeenCalled()
    })

    it('should not scroll when auto scroll is disabled', () => {
      scrollManager.autoScrollEnabled = false
      const smartScrollToContentSpy = vi.spyOn(scrollManager, 'smartScrollToContent')

      scrollManager.smartScrollToContent()

      expect(smartScrollToContentSpy).toHaveBeenCalled()
    })

    it('should not scroll when all content is completed', () => {
      scrollManager.autoScrollEnabled = true
      const checkerFunction = vi.fn().mockReturnValue(true)
      scrollManager.setContentCompletedChecker(checkerFunction)
      const smartScrollToContentSpy = vi.spyOn(scrollManager, 'smartScrollToContent')

      scrollManager.smartScrollToContent()

      expect(smartScrollToContentSpy).toHaveBeenCalled()
    })
  })

  describe('scrollPageToBottom', () => {
    it('should scroll page to bottom', () => {
      const smartScrollToContentSpy = vi.spyOn(scrollManager, 'smartScrollToContent')
      
      scrollManager.scrollPageToBottom()

      expect(smartScrollToContentSpy).toHaveBeenCalled()
    })
  })

  describe('resetScrollState', () => {
    it('should reset scroll state to initial values', () => {
      // Set some non-default values
      scrollManager.autoScrollEnabled = false
      scrollManager.isUserScrolling = true
      scrollManager.consecutiveScrollCount = 5
      scrollManager.scrollVelocity = 10
      scrollManager.userScrollTimeout = 123

      scrollManager.resetScrollState()

      expect(scrollManager.autoScrollEnabled).toBe(true)
      expect(scrollManager.isUserScrolling).toBe(false)
      expect(scrollManager.consecutiveScrollCount).toBe(0)
      expect(scrollManager.scrollVelocity).toBe(0)
      expect(scrollManager.userScrollTimeout).toBeNull()
    })

    it('should clear user scroll timeout', () => {
      scrollManager.userScrollTimeout = 123

      scrollManager.resetScrollState()

      expect(global.clearTimeout).toHaveBeenCalledWith(123)
    })
  })

  describe('getScrollState', () => {
    it('should return current scroll state', () => {
      scrollManager.autoScrollEnabled = false
      scrollManager.isUserScrolling = true
      scrollManager.lastScrollTop = 100
      scrollManager.scrollVelocity = 5
      scrollManager.consecutiveScrollCount = 3

      const state = scrollManager.getScrollState()

      expect(state).toEqual({
        autoScrollEnabled: false,
        isUserScrolling: true,
        lastScrollTop: 100
      })
    })
  })

  describe('setAutoScrollEnabled', () => {
    it('should set auto scroll enabled state', () => {
      scrollManager.setAutoScrollEnabled(false)

      expect(scrollManager.autoScrollEnabled).toBe(false)
    })

    it('should enable auto scroll when set to true', () => {
      scrollManager.autoScrollEnabled = false
      scrollManager.setAutoScrollEnabled(true)

      expect(scrollManager.autoScrollEnabled).toBe(true)
    })
  })

  describe('isUserScrollingActive', () => {
    it('should return true when user is scrolling', () => {
      scrollManager.isUserScrolling = true

      expect(scrollManager.isUserScrollingActive()).toBe(true)
    })

    it('should return false when user is not scrolling', () => {
      scrollManager.isUserScrolling = false

      expect(scrollManager.isUserScrollingActive()).toBe(false)
    })
  })

  describe('forceScrollToBottom', () => {
    it('should force scroll to bottom regardless of state', () => {
      scrollManager.autoScrollEnabled = false
      scrollManager.isUserScrolling = true

      scrollManager.forceScrollToBottom()

      expect(mockWindow.scrollTo).toHaveBeenCalledWith({
        top: 1000,
        behavior: 'smooth'
      })
    })
  })

  describe('scrollToElement', () => {
    it('should scroll to element with smooth behavior', () => {
      const mockElement = {
        scrollIntoView: vi.fn()
      }

      scrollManager.scrollToElement(mockElement)

      expect(mockElement.scrollIntoView).toHaveBeenCalledWith({
        behavior: 'smooth',
        block: 'center'
      })
    })

    it('should scroll to element with custom behavior', () => {
      const mockElement = {
        scrollIntoView: vi.fn()
      }

      scrollManager.scrollToElement(mockElement, 'auto')

      expect(mockElement.scrollIntoView).toHaveBeenCalledWith({
        behavior: 'auto',
        block: 'center'
      })
    })

    it('should handle null element gracefully', () => {
      expect(() => scrollManager.scrollToElement(null)).not.toThrow()
    })
  })

  describe('cleanup', () => {
    it('should cleanup scroll listener and reset state', () => {
      scrollManager.handleUserScroll = vi.fn()
      scrollManager.userScrollTimeout = 123
      const mockObserver = { disconnect: vi.fn() }
      scrollManager.scrollObserver = mockObserver

      const cleanupScrollListenerSpy = vi.spyOn(scrollManager, 'cleanupScrollListener')

      scrollManager.cleanup()

      expect(cleanupScrollListenerSpy).toHaveBeenCalled()
      // Note: actual implementation doesn't call scrollObserver.disconnect()
      expect(scrollManager.scrollObserver).toBeNull()
    })

    it('should handle null scroll observer', () => {
      scrollManager.scrollObserver = null

      expect(() => scrollManager.cleanup()).not.toThrow()
    })
  })

  describe('user scroll detection', () => {
    it('should detect significant user scroll', () => {
      scrollManager.initScrollListener()
      
      // Simulate significant scroll
      mockWindow.pageYOffset = 100
      document.documentElement.scrollTop = 100
      
      // Trigger scroll event by calling the handler directly
      if (scrollManager.handleUserScroll) {
        scrollManager.handleUserScroll()
      }

      // Should detect user scroll
      expect(scrollManager.isUserScrolling).toBe(true)
      expect(scrollManager.autoScrollEnabled).toBe(false)
    })

    it('should reset user scroll state after timeout', async () => {
      scrollManager.initScrollListener()
      
      // Simulate user scroll
      mockWindow.pageYOffset = 100
      document.documentElement.scrollTop = 100
      
      if (scrollManager.handleUserScroll) {
        scrollManager.handleUserScroll()
      }

      expect(scrollManager.isUserScrolling).toBe(true)

      // Wait for timeout
      await new Promise(resolve => setTimeout(resolve, 5100))

      expect(scrollManager.isUserScrolling).toBe(false)
    }, 10000)

    it('should not detect minor scroll movements', () => {
      scrollManager.initScrollListener()
      
      // Simulate minor scroll
      mockWindow.pageYOffset = 3
      document.documentElement.scrollTop = 3
      
      if (scrollManager.handleUserScroll) {
        scrollManager.handleUserScroll()
      }

      expect(scrollManager.isUserScrolling).toBe(false)
    })
  })

  describe('scroll velocity calculation', () => {
    it('should calculate scroll velocity correctly', async () => {
      scrollManager.initScrollListener()
      
      // First scroll
      mockWindow.pageYOffset = 0
      document.documentElement.scrollTop = 0
      scrollManager.lastUserScrollTime = Date.now() - 100
      scrollManager.lastScrollTop = 0
      
      if (scrollManager.handleUserScroll) {
        scrollManager.handleUserScroll()
      }
      
      // Wait for throttle to reset
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // Second scroll with significant movement (more than 5px difference)
      mockWindow.pageYOffset = 50
      document.documentElement.scrollTop = 50
      if (scrollManager.handleUserScroll) {
        scrollManager.handleUserScroll()
      }

      // The scroll velocity should be calculated as scrollDiff / timeDiff
      // scrollDiff = 50, timeDiff = ~200ms (including wait time), so velocity should be ~0.25
      expect(scrollManager.scrollVelocity).toBeCloseTo(0.25, 2)
    })
  })

  describe('consecutive scroll detection', () => {
    it('should track consecutive scrolls', () => {
      scrollManager.initScrollListener()
      
      // Multiple scrolls
      for (let i = 0; i < 3; i++) {
        mockWindow.pageYOffset = (i + 1) * 20
        if (scrollManager.handleUserScroll) {
          scrollManager.handleUserScroll()
        }
      }

      expect(scrollManager.consecutiveScrollCount).toBeGreaterThan(0)
    })
  })

  describe('auto scroll recovery', () => {
    it('should recover auto scroll after user stops scrolling', async () => {
      scrollManager.initScrollListener()
      
      // Simulate user scroll
      mockWindow.pageYOffset = 100
      if (scrollManager.handleUserScroll) {
        scrollManager.handleUserScroll()
      }

      expect(scrollManager.autoScrollEnabled).toBe(false)

      // Wait for recovery timeout
      await new Promise(resolve => setTimeout(resolve, 5100))

      expect(scrollManager.autoScrollEnabled).toBe(true)
    }, 10000)

    it('should not recover auto scroll if content is completed', async () => {
      scrollManager.initScrollListener()
      const checkerFunction = vi.fn().mockReturnValue(true)
      scrollManager.setContentCompletedChecker(checkerFunction)
      
      // Simulate user scroll
      mockWindow.pageYOffset = 100
      if (scrollManager.handleUserScroll) {
        scrollManager.handleUserScroll()
      }

      // Wait for recovery timeout
      await new Promise(resolve => setTimeout(resolve, 5100))

      expect(scrollManager.autoScrollEnabled).toBe(false)
    }, 10000)
  })
}) 