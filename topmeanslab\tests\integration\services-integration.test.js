import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { TopmeansApiService } from '@services/TopmeansApiService'
import { TopmeansFormManager } from '@services/TopmeansFormManager'
import { TopmeansMapService } from '@services/TopmeansMapService'
import { TopmeansMarkdownService } from '@services/TopmeansMarkdownService'
import { TopmeansScrollManager } from '@services/TopmeansScrollManager'
import { TopmeansStyleManager } from '@services/TopmeansStyleManager'

describe('Services Integration Tests', () => {
  let apiService
  let formManager
  let mapService
  let markdownService
  let scrollManager
  let styleManager
  let mockLocalStorage
  let mockMutationObserver

  beforeEach(() => {
    // Mock localStorage
    mockLocalStorage = {}
    global.localStorage = {
      getItem: vi.fn((key) => mockLocalStorage[key] || null),
      setItem: vi.fn((key, value) => {
        mockLocalStorage[key] = value
      }),
      removeItem: vi.fn((key) => {
        delete mockLocalStorage[key]
      }),
      clear: vi.fn(() => {
        mockLocalStorage = {}
      })
    }

    // Mock MutationObserver
    mockMutationObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      disconnect: vi.fn()
    }))
    global.MutationObserver = mockMutationObserver

    // Mock document
    global.document = {
      getElementById: vi.fn(),
      createElement: vi.fn((tagName) => {
        if (tagName === 'style') {
          return {
            id: '',
            innerHTML: ''
          }
        }
        return {}
      }),
      head: {
        appendChild: vi.fn()
      },
      body: {
        classList: {
          contains: vi.fn().mockReturnValue(false)
        }
      },
      documentElement: {
        classList: {
          contains: vi.fn().mockReturnValue(false)
        }
      },
      querySelector: vi.fn(),
      querySelectorAll: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    }

    // Mock window
    global.window = {
      setInterval: vi.fn(() => 123),
      clearInterval: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      matchMedia: vi.fn(() => ({
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        matches: false
      })),
      getComputedStyle: vi.fn(() => ({
        getPropertyValue: vi.fn((property) => {
          const values = {
            '--vp-c-bg': '#ffffff',
            '--vp-c-bg-soft': '#f6f6f7',
            '--vp-c-text-1': '#213547',
            '--vp-c-divider': '#e2e2e2',
            '--vp-c-divider-light': '#f1f1f1',
            '--vp-c-brand': '#646cff',
            '--vp-c-brand-soft': '#646cff1a',
            '--vp-c-brand-dimm': '#646cff0d'
          }
          return values[property] || ''
        })
      }))
    }

    global.getComputedStyle = global.window.getComputedStyle
    global.clearInterval = global.window.clearInterval
    global.setInterval = global.window.setInterval

    // Initialize all services
    apiService = new TopmeansApiService()
    formManager = new TopmeansFormManager()
    mapService = new TopmeansMapService()
    markdownService = new TopmeansMarkdownService()
    scrollManager = new TopmeansScrollManager()
    styleManager = new TopmeansStyleManager()

    // Mock fetch for API service
    global.fetch = vi.fn()
  })

  afterEach(() => {
    vi.clearAllMocks()
    mockLocalStorage = {}
  })

  describe('Form and API Integration', () => {
    it('should handle complete travel planning workflow', async () => {
      // 1. Setup form data
      const formData = {
        s_address: '北京',
        e_address: '上海',
        startDate: '2024-01-01',
        dates: 3,
        s_location: {
          lng: 116.397,
          lat: 39.916,
          province: '北京市',
          city: '北京市'
        },
        e_location: {
          lng: 121.473,
          lat: 31.230,
          province: '上海市',
          city: '上海市'
        }
      }

      // 2. Save form data
      formManager.saveFormData(formData)

      // 3. Validate form data
      const validation = formManager.validateFormData(formData)
      expect(validation.isValid).toBe(true)

      // 4. Mock API responses
      const mockApiResponses = {
        hotel: { success: true, url: 'https://example.com/hotel.jpg' },
        food: { success: true, url: 'https://example.com/food.jpg' },
        view: { success: true, url: 'https://example.com/view.jpg' }
      }

      global.fetch.mockImplementation((url) => {
        if (url.includes('/api/hotel')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockApiResponses.hotel)
          })
        }
        if (url.includes('/api/ai_img')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockApiResponses.food)
          })
        }
        if (url.includes('/api/view')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockApiResponses.view)
          })
        }
        return Promise.resolve({ ok: false })
      })

      // 5. Test API calls
      const hotelUrl = await apiService.getHotelUrl('测试酒店', 'testuser', '2024-01-01', 1, '北京')
      const foodUrl = await apiService.getFoodImgUrl('宫保鸡丁', '川菜经典')
      const viewUrl = await apiService.getViewUrl('故宫')

      expect(hotelUrl).toBe('https://example.com/hotel.jpg')
      expect(foodUrl).toBe('https://example.com/food.jpg')
      expect(viewUrl).toBe('https://example.com/view.jpg')
    })
  })

  describe('Map and Style Integration', () => {
    it('should handle map initialization with style management', async () => {
      // 1. Initialize style manager
      styleManager.ensureAmapSuggestStyles()

      // 2. Mock API response for map keys
      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          AMAP_CODE: 'test_code',
          AMAP_KEY: 'test_key'
        })
      })

      // 3. Mock map service initialization
      const mockAMap = {
        plugin: vi.fn(),
        PlaceSearch: vi.fn(),
        Map: vi.fn(() => ({
          setFitView: vi.fn(),
          add: vi.fn(),
          on: vi.fn()
        }))
      }

      vi.spyOn(mapService, 'loadAMapScript').mockResolvedValue(mockAMap)

      // 4. Initialize map service
      const result = await mapService.initialize()

      expect(result).toBe(mockAMap)
      expect(styleManager.amapStyleInterval).toBeDefined()
    })

    it('should handle theme changes with map styles', () => {
      // 1. Setup theme change listener
      const themeChangeCallback = vi.fn()
      const observer = styleManager.watchThemeChange(themeChangeCallback)

      // 2. Simulate theme change by calling the observer callback
      const mutationObserverCallback = mockMutationObserver.mock.calls[0][0]
      mutationObserverCallback([{
        type: 'attributes',
        attributeName: 'class'
      }])

      expect(themeChangeCallback).toHaveBeenCalledWith(false)
    })
  })

  describe('Scroll and Content Integration', () => {
    it('should handle content generation with scroll management', () => {
      // 1. Setup scroll manager
      scrollManager.initScrollListener()

      // 2. Setup content completed checker
      const contentCompletedChecker = vi.fn().mockReturnValue(false)
      scrollManager.setContentCompletedChecker(contentCompletedChecker)

      // 3. Setup display state checker
      const displayStateChecker = vi.fn().mockReturnValue({
        visible: true,
        completed: false
      })
      scrollManager.setDisplayStateChecker(displayStateChecker)

      // 4. Test scroll behavior
      expect(scrollManager.autoScrollEnabled).toBe(true)
      expect(scrollManager.checkAllContentCompleted()).toBe(false)

      // 5. Simulate content completion
      contentCompletedChecker.mockReturnValue(true)
      expect(scrollManager.checkAllContentCompleted()).toBe(true)
    })

    it('should handle markdown content with scroll behavior', () => {
      // 1. Parse markdown content
      const markdownContent = '# 旅行计划\n\n## 第一天\n- 参观故宫\n- 游览天安门广场'
      
      // Mock markdown rendering
      const mockMd = {
        render: vi.fn().mockReturnValue('<h1>旅行计划</h1><h2>第一天</h2><ul><li>参观故宫</li><li>游览天安门广场</li></ul>')
      }
      markdownService.md = mockMd

      const htmlContent = markdownService.parse(markdownContent)

      expect(htmlContent).toContain('<h1>旅行计划</h1>')
      expect(htmlContent).toContain('<li>参观故宫</li>')

      // 2. Test scroll behavior with content
      scrollManager.setAutoScrollEnabled(true)
      expect(scrollManager.autoScrollEnabled).toBe(true)
    })
  })

  describe('Error Handling Integration', () => {
    it('should handle API failures gracefully', async () => {
      // 1. Setup form data
      const formData = {
        s_address: '北京',
        e_address: '上海',
        startDate: '2024-01-01',
        dates: 3
      }

      formManager.saveFormData(formData)

      // 2. Mock API failure
      global.fetch.mockRejectedValue(new Error('Network error'))

      // 3. Test error handling
      await expect(apiService.getHotelUrl('测试酒店', 'testuser', '2024-01-01', 1, '北京'))
        .rejects.toThrow('Network error')

      // 4. Verify form data is still intact
      const savedData = formManager.loadFormData()
      expect(savedData.s_address).toBe('北京')
    })

    it('should handle map service failures', async () => {
      // 1. Mock map service failure
      global.fetch.mockResolvedValue({
        ok: false,
        status: 500
      })

      // 2. Test error handling
      await expect(mapService.initialize()).rejects.toThrow('获取API密钥失败，请检查网络连接')

      // 3. Verify style manager is still functional
      expect(() => styleManager.ensureAmapSuggestStyles()).not.toThrow()
    })
  })

  describe('Data Persistence Integration', () => {
    it('should persist and restore complete application state', () => {
      // 1. Setup complete application state
      const appState = {
        formData: {
          s_address: '北京',
          e_address: '上海',
          startDate: '2024-01-01',
          dates: 3
        },
        scrollState: {
          autoScrollEnabled: true,
          isUserScrolling: false
        },
        styleState: {
          theme: 'light'
        }
      }

      // 2. Save form data
      formManager.saveFormData(appState.formData)

      // 3. Set scroll state
      scrollManager.setAutoScrollEnabled(appState.scrollState.autoScrollEnabled)

      // 4. Verify state persistence
      const savedFormData = formManager.loadFormData()
      expect(savedFormData.s_address).toBe('北京')

      const scrollState = scrollManager.getScrollState()
      expect(scrollState.autoScrollEnabled).toBe(true)
    })
  })

  describe('Performance Integration', () => {
    it('should handle multiple concurrent operations efficiently', async () => {
      // 1. Setup multiple services
      const services = [
        new TopmeansApiService(),
        new TopmeansFormManager(),
        new TopmeansMapService(),
        new TopmeansMarkdownService(),
        new TopmeansScrollManager(),
        new TopmeansStyleManager()
      ]

      // 2. Mock API responses
      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, url: 'https://example.com/test.jpg' })
      })

      // 3. Perform concurrent operations
      const startTime = performance.now()

      const promises = [
        apiService.getHotelUrl('酒店1', 'user1', '2024-01-01', 1, '北京'),
        apiService.getFoodImgUrl('美食1', '描述1'),
        apiService.getViewUrl('景点1'),
        Promise.resolve(formManager.getDefaultFormData()),
        Promise.resolve(markdownService.parse('# Test')),
        Promise.resolve(scrollManager.getScrollState())
      ]

      const results = await Promise.all(promises)
      const endTime = performance.now()

      // 4. Verify all operations completed
      expect(results).toHaveLength(6)
      expect(endTime - startTime).toBeLessThan(1000) // Should complete within 1 second
    })
  })

  describe('Cleanup Integration', () => {
    it('should cleanup all services properly', () => {
      // 1. Initialize all services
      apiService.abortController = { abort: vi.fn() }
      scrollManager.userScrollTimeout = 123
      styleManager.amapStyleInterval = 456

      // 2. Perform cleanup
      apiService.cleanup()
      scrollManager.cleanup()
      styleManager.cleanup()

      // 3. Verify cleanup
      expect(apiService.abortController).toBeNull()
      expect(scrollManager.userScrollTimeout).toBeNull()
      expect(styleManager.amapStyleInterval).toBeNull()
    })
  })

  describe('Real-world Scenario Tests', () => {
    it('should handle complete travel planning scenario', async () => {
      // 1. User fills out travel form
      const travelForm = {
        s_address: '北京天安门',
        e_address: '上海外滩',
        startDate: '2024-01-15',
        dates: 5,
        plan_mode: '往返',
        travel_mode: '自驾'
      }

      // 2. Save and validate form
      formManager.saveFormData(travelForm)
      const validation = formManager.validateFormData(travelForm)
      expect(validation.isValid).toBe(true)

      // 3. Mock API responses for travel planning
      global.fetch.mockImplementation((url) => {
        if (url.includes('/api/hotel')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ success: true, url: 'https://example.com/hotel.jpg' })
          })
        }
        if (url.includes('/api/ai_img')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ success: true, url: 'https://example.com/food.jpg' })
          })
        }
        if (url.includes('/api/view')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ success: true, url: 'https://example.com/view.jpg' })
          })
        }
        return Promise.resolve({ ok: false })
      })

      // 4. Generate travel content
      const travelContent = `
# 北京到上海5日游

## 第一天：北京出发
- 上午：天安门广场
- 下午：故宫博物院
- 晚上：王府井步行街

## 第二天：北京游览
- 上午：颐和园
- 下午：圆明园
- 晚上：鸟巢水立方

## 第三天：前往上海
- 上午：高铁前往上海
- 下午：外滩观光
- 晚上：南京路步行街

## 第四天：上海游览
- 上午：东方明珠
- 下午：豫园
- 晚上：陆家嘴夜景

## 第五天：返回北京
- 上午：上海博物馆
- 下午：高铁返回北京
      `

      // 5. Parse markdown content
      const mockMd = {
        render: vi.fn().mockReturnValue('<h1>北京到上海5日游</h1><h2>第一天：北京出发</h2>')
      }
      markdownService.md = mockMd

      const htmlContent = markdownService.parse(travelContent)
      expect(htmlContent).toContain('<h1>北京到上海5日游</h1>')

      // 6. Setup scroll management for content
      scrollManager.initScrollListener()
      scrollManager.setAutoScrollEnabled(true)

      // 7. Setup style management
      styleManager.ensureAmapSuggestStyles()

      // 8. Verify complete workflow
      expect(formManager.loadFormData().s_address).toBe('北京天安门')
      expect(scrollManager.autoScrollEnabled).toBe(true)
      expect(styleManager.amapStyleInterval).toBeDefined()
    })
  })
}) 