<template>
  <div class="journal-editor">
    <div class="editor-container">
      <div class="editor-header">
        <h3 class="editor-title">
          {{ isEditMode ? '编辑游记' : '写游记' }}
        </h3>
        <div class="editor-actions">
          <el-button @click="handleCancel" size="small">取消</el-button>
          <el-button @click="handleSave" :loading="isSaving" type="primary" size="small">
            {{ isEditMode ? '更新' : '发布' }}
          </el-button>
        </div>
      </div>

      <div class="editor-form">
        <!-- 游记标题 -->
        <div class="form-item">
          <label class="form-label">游记标题</label>
          <el-input
            v-model="journalData.title"
            placeholder="请输入游记标题"
            maxlength="100"
            show-word-limit
            class="title-input"
          />
        </div>

        <!-- 旅行信息 -->
        <div class="travel-info">
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">目的地</label>
              <el-input
                v-model="journalData.destination"
                placeholder="请输入目的地"
                maxlength="50"
              />
            </div>
            <div class="form-item">
              <label class="form-label">出发地</label>
              <el-input
                v-model="journalData.startLocation"
                placeholder="请输入出发地"
                maxlength="50"
              />
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">旅行日期</label>
              <el-date-picker
                v-model="journalData.travelDate"
                type="date"
                placeholder="选择旅行日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </div>
            <div class="form-item">
              <label class="form-label">旅行天数</label>
              <el-select v-model="journalData.travelDays" placeholder="选择天数">
                <el-option
                  v-for="day in 30"
                  :key="day"
                  :label="`${day}天`"
                  :value="day"
                />
              </el-select>
            </div>
            <div class="form-item">
              <label class="form-label">旅行方式</label>
              <el-select v-model="journalData.travelMode" placeholder="选择方式">
                <el-option label="自驾" value="自驾" />
                <el-option label="租车" value="租车" />
                <el-option label="公共交通" value="公共交通" />
                <el-option label="飞机" value="飞机" />
                <el-option label="火车" value="火车" />
                <el-option label="徒步" value="徒步" />
              </el-select>
            </div>
          </div>
        </div>

        <!-- 封面图片 -->
        <div class="form-item">
          <label class="form-label">封面图片</label>
          <div class="cover-upload">
            <el-upload
              ref="coverUpload"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :before-upload="beforeUpload"
              :on-success="handleCoverSuccess"
              :on-error="handleUploadError"
              :show-file-list="false"
              accept="image/*"
            >
              <div class="cover-preview" v-if="journalData.coverImage">
                <img :src="journalData.coverImage" alt="封面" />
                <div class="cover-overlay">
                  <el-icon><Edit /></el-icon>
                  <span>更换封面</span>
                </div>
              </div>
              <div class="cover-placeholder" v-else>
                <el-icon><Plus /></el-icon>
                <span>点击上传封面</span>
              </div>
            </el-upload>
            <button
              v-if="journalData.coverImage"
              @click="removeCover"
              class="remove-cover"
            >
              <el-icon><Delete /></el-icon>
            </button>
          </div>
        </div>

        <!-- 游记内容 -->
        <div class="form-item">
          <label class="form-label">游记内容</label>
          <div class="content-editor">
            <div class="editor-toolbar">
              <button
                v-for="tool in editorTools"
                :key="tool.name"
                @click="insertMarkdown(tool.syntax)"
                class="toolbar-btn"
                :title="tool.title"
              >
                {{ tool.icon }}
              </button>
              <el-upload
                :action="uploadUrl"
                :headers="uploadHeaders"
                :before-upload="beforeUpload"
                :on-success="handleImageSuccess"
                :on-error="handleUploadError"
                :show-file-list="false"
                accept="image/*"
                class="image-upload"
              >
                <button class="toolbar-btn" title="插入图片">
                  📷
                </button>
              </el-upload>
            </div>
            <el-input
              v-model="journalData.content"
              type="textarea"
              :rows="15"
              placeholder="请输入游记内容，支持Markdown语法"
              class="content-textarea"
            />
          </div>
        </div>

        <!-- 标签 -->
        <div class="form-item">
          <label class="form-label">标签</label>
          <div class="tags-input">
            <el-tag
              v-for="tag in journalData.tags"
              :key="tag"
              @close="removeTag(tag)"
              closable
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-if="showTagInput"
              v-model="newTag"
              @keyup.enter="addTag"
              @blur="addTag"
              size="small"
              class="new-tag-input"
              placeholder="添加标签"
            />
            <el-button
              v-else
              @click="showTagInput = true"
              size="small"
              class="new-tag-btn"
            >
              + 添加标签
            </el-button>
          </div>
        </div>

        <!-- 隐私设置 -->
        <div class="form-item">
          <label class="form-label">隐私设置</label>
          <div class="privacy-settings">
            <el-radio-group v-model="journalData.isPublic">
              <el-radio :label="true">
                <div class="privacy-option">
                  <el-icon><View /></el-icon>
                  <div>
                    <div class="option-title">公开</div>
                    <div class="option-desc">所有人都可以查看</div>
                  </div>
                </div>
              </el-radio>
              <el-radio :label="false">
                <div class="privacy-option">
                  <el-icon><Lock /></el-icon>
                  <div>
                    <div class="option-title">私密</div>
                    <div class="option-desc">只有自己可以查看</div>
                  </div>
                </div>
              </el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Delete, Plus, View, Lock } from '@element-plus/icons-vue'
import { useUserStore } from '../UserCenter/userStore'

const props = defineProps({
  journal: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['save', 'cancel'])

const userStore = useUserStore()
const API_BASE = import.meta.env.VITE_BACKEND_SRV_URL + '/api'

// 编辑器工具栏
const editorTools = [
  { name: 'bold', icon: 'B', syntax: '**粗体**', title: '粗体' },
  { name: 'italic', icon: 'I', syntax: '*斜体*', title: '斜体' },
  { name: 'heading', icon: 'H', syntax: '## 标题', title: '标题' },
  { name: 'quote', icon: '"', syntax: '> 引用', title: '引用' },
  { name: 'list', icon: '•', syntax: '- 列表项', title: '列表' },
  { name: 'link', icon: '🔗', syntax: '[链接文字](URL)', title: '链接' }
]

// 状态变量
const isSaving = ref(false)
const showTagInput = ref(false)
const newTag = ref('')
const isEditMode = computed(() => !!props.journal)

// 游记数据
const journalData = reactive({
  title: '',
  content: '',
  coverImage: '',
  destination: '',
  startLocation: '',
  travelDays: 1,
  travelMode: '自驾',
  travelDate: '',
  isPublic: true,
  tags: [],
  images: []
})

// 上传配置
const uploadUrl = `${API_BASE}/journals/upload-image`
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${userStore.token}`
}))

// 初始化数据
onMounted(() => {
  if (props.journal) {
    Object.assign(journalData, {
      ...props.journal,
      travelDate: props.journal.travel_date,
      travelDays: props.journal.travel_days,
      travelMode: props.journal.travel_mode,
      startLocation: props.journal.start_location,
      coverImage: props.journal.cover_image,
      isPublic: props.journal.is_public,
      tags: props.journal.tags || []
    })
  }
})

// 文件上传前验证
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件！')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB！')
    return false
  }
  return true
}

// 封面上传成功
const handleCoverSuccess = (response) => {
  if (response.success) {
    journalData.coverImage = `${import.meta.env.VITE_BACKEND_SRV_URL}${response.imageUrl}`
    ElMessage.success('封面上传成功')
  } else {
    ElMessage.error(response.message || '封面上传失败')
  }
}

// 内容图片上传成功
const handleImageSuccess = (response) => {
  if (response.success) {
    const imageUrl = `${import.meta.env.VITE_BACKEND_SRV_URL}${response.imageUrl}`
    const imageMarkdown = `![图片](${imageUrl})`
    journalData.content += imageMarkdown

    // 保存图片到images数组
    if (!journalData.images) {
      journalData.images = []
    }
    journalData.images.push(imageUrl)

    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

// 上传失败
const handleUploadError = (error) => {
  console.error('上传失败:', error)
  ElMessage.error('上传失败，请稍后重试')
}

// 移除封面
const removeCover = () => {
  journalData.coverImage = ''
}

// 插入Markdown语法
const insertMarkdown = (syntax) => {
  journalData.content += syntax
}

// 添加标签
const addTag = () => {
  if (newTag.value && !journalData.tags.includes(newTag.value)) {
    journalData.tags.push(newTag.value)
    newTag.value = ''
  }
  showTagInput.value = false
}

// 移除标签
const removeTag = (tag) => {
  const index = journalData.tags.indexOf(tag)
  if (index > -1) {
    journalData.tags.splice(index, 1)
  }
}

// 验证表单
const validateForm = () => {
  if (!journalData.title.trim()) {
    ElMessage.error('请输入游记标题')
    return false
  }
  if (!journalData.content.trim()) {
    ElMessage.error('请输入游记内容')
    return false
  }
  if (!journalData.destination.trim()) {
    ElMessage.error('请输入目的地')
    return false
  }
  if (!journalData.startLocation.trim()) {
    ElMessage.error('请输入出发地')
    return false
  }
  if (!journalData.travelDate) {
    ElMessage.error('请选择旅行日期')
    return false
  }
  return true
}

// 保存游记
const handleSave = async () => {
  if (!validateForm()) return

  try {
    isSaving.value = true

    const submitData = {
      title: journalData.title,
      content: journalData.content,
      coverImage: journalData.coverImage,
      destination: journalData.destination,
      startLocation: journalData.startLocation,
      travelDays: journalData.travelDays,
      travelMode: journalData.travelMode,
      travelDate: journalData.travelDate,
      isPublic: journalData.isPublic,
      tags: journalData.tags,
      images: journalData.images
    }

    const url = isEditMode.value
      ? `${API_BASE}/journals/${props.journal.id}`
      : `${API_BASE}/journals`

    const method = isEditMode.value ? 'PUT' : 'POST'

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userStore.token}`
      },
      body: JSON.stringify(submitData)
    })

    const result = await response.json()

    if (result.success) {
      ElMessage.success(isEditMode.value ? '游记更新成功' : '游记发布成功')
      emit('save', result)
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('保存游记失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  } finally {
    isSaving.value = false
  }
}

// 取消编辑
const handleCancel = () => {
  ElMessageBox.confirm('确定要取消编辑吗？未保存的内容将丢失。', '确认取消', {
    confirmButtonText: '确定',
    cancelButtonText: '继续编辑',
    type: 'warning'
  }).then(() => {
    emit('cancel')
  }).catch(() => {
    // 用户取消了取消操作
  })
}
</script>

<style scoped>
.journal-editor {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.editor-title {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.editor-actions {
  display: flex;
  gap: 10px;
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.title-input {
  font-size: 16px;
}

.travel-info {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-row .form-item {
  flex: 1;
  margin-bottom: 0;
}

.cover-upload {
  position: relative;
  display: inline-block;
}

.cover-preview {
  position: relative;
  width: 200px;
  height: 120px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
}

.cover-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s;
}

.cover-preview:hover .cover-overlay {
  opacity: 1;
}

.cover-placeholder {
  width: 200px;
  height: 120px;
  border: 2px dashed #ddd;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  cursor: pointer;
  transition: all 0.3s;
}

.cover-placeholder:hover {
  border-color: #409eff;
  color: #409eff;
}

.remove-cover {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: #ff4757;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-editor {
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
  gap: 5px;
}

.toolbar-btn {
  padding: 5px 10px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn:hover {
  background: #e6f7ff;
  border-color: #409eff;
}

.image-upload {
  display: inline-block;
}

.content-textarea {
  border: none;
  resize: vertical;
}

.tags-input {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.new-tag-input {
  width: 90px;
}

.new-tag-btn {
  border-style: dashed;
}

.privacy-settings {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.privacy-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-title {
  font-weight: 500;
  color: #333;
}

.option-desc {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .journal-editor {
    padding: 15px;
  }

  .form-row {
    flex-direction: column;
    gap: 10px;
  }

  .editor-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .editor-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .cover-preview,
  .cover-placeholder {
    width: 100%;
    max-width: 300px;
  }
}
</style>