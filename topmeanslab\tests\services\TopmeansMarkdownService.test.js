import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { TopmeansMarkdownService } from '@services/TopmeansMarkdownService'

// Mock markdown-it and its plugins
vi.mock('markdown-it', () => {
  const mockMarkdownIt = vi.fn().mockImplementation(() => ({
    use: vi.fn().mockReturnThis(),
    render: vi.fn(),
    renderInline: vi.fn(),
    enable: vi.fn(),
    disable: vi.fn(),
    set: vi.fn(),
    core: {
      ruler: {
        after: vi.fn()
      }
    },
    renderer: {
      rules: {
        heading_open: vi.fn(),
        link_open: vi.fn(),
        table_open: vi.fn(),
        table_close: vi.fn(),
        th_open: vi.fn(),
        td_open: vi.fn(),
        code_block: vi.fn(),
        code_inline: vi.fn(),
        fence: vi.fn(),
        blockquote_open: vi.fn(),
        list_item_open: vi.fn(),
        hr: vi.fn(),
        image: vi.fn()
      }
    }
  }))
  
  return {
    default: mockMarkdownIt
  }
})

vi.mock('markdown-it-attrs', () => ({
  default: vi.fn()
}))

vi.mock('markdown-it-container', () => ({
  default: vi.fn()
}))

describe('TopmeansMarkdownService', () => {
  let markdownService
  let mockMd

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
    
    // Create new service instance
    markdownService = new TopmeansMarkdownService()
    
    // Get the mock instance that was created
    mockMd = markdownService.md
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('constructor', () => {
    it('should initialize markdown-it instance with correct options', () => {
      // The constructor should have been called with the correct options
      expect(markdownService.md).toBeDefined()
      expect(markdownService.md.use).toBeDefined()
    })

    it('should call setupPlugins and setupCustomRules', () => {
      const setupPluginsSpy = vi.spyOn(markdownService, 'setupPlugins')
      const setupCustomRulesSpy = vi.spyOn(markdownService, 'setupCustomRules')
      
      // Re-initialize to trigger the setup methods
      markdownService.initialize()
      
      expect(setupPluginsSpy).toHaveBeenCalled()
      expect(setupCustomRulesSpy).toHaveBeenCalled()
    })
  })

  describe('initialize', () => {
    it('should set md property to markdown-it instance', () => {
      expect(markdownService.md).toBe(mockMd)
    })
  })

  describe('setupPlugins', () => {
    it('should configure markdown-it-attrs plugin', () => {
      markdownService.setupPlugins()
      
      // Check that use was called multiple times (for attrs, containers, etc.)
      expect(mockMd.use).toHaveBeenCalled()
      expect(mockMd.use.mock.calls.length).toBeGreaterThan(0)
    })

    it('should configure markdown-it-container plugins', () => {
      markdownService.setupPlugins()
      
      // Check that use was called for containers
      expect(mockMd.use).toHaveBeenCalled()
      expect(mockMd.use.mock.calls.length).toBeGreaterThan(0)
    })

    it('should call setupCustomEmoji', () => {
      const setupCustomEmojiSpy = vi.spyOn(markdownService, 'setupCustomEmoji')
      
      markdownService.setupPlugins()
      
      expect(setupCustomEmojiSpy).toHaveBeenCalled()
    })
  })

  describe('setupCustomEmoji', () => {
    it('should define emoji mappings', () => {
      // Test that setupCustomEmoji method exists and can be called
      expect(typeof markdownService.setupCustomEmoji).toBe('function')
      expect(mockMd.core.ruler.after).toHaveBeenCalled()
    })
  })

  describe('setupCustomRules', () => {
    it('should configure custom rendering rules', () => {
      const setupCustomRulesSpy = vi.spyOn(markdownService, 'setupCustomRules')
      
      markdownService.setupCustomRules()
      
      expect(setupCustomRulesSpy).toHaveBeenCalled()
    })
  })

  describe('parse', () => {
    it('should parse markdown text to HTML', () => {
      const markdownText = '# Hello World\n\nThis is a **test**.'
      const expectedHtml = '<h1>Hello World</h1>\n<p>This is a <strong>test</strong>.</p>\n'
      
      mockMd.render.mockReturnValue(expectedHtml)
      
      const result = markdownService.parse(markdownText)
      
      expect(mockMd.render).toHaveBeenCalledWith(markdownText)
      expect(result).toBe(expectedHtml)
    })

    it('should handle empty text', () => {
      const result = markdownService.parse('')
      
      expect(result).toBe('')
    })

    it('should handle null text', () => {
      const result = markdownService.parse(null)
      
      expect(result).toBe('')
    })
  })

  describe('parseInline', () => {
    it('should parse inline markdown text', () => {
      const inlineText = '**bold** and *italic*'
      const expectedHtml = '<strong>bold</strong> and <em>italic</em>'
      
      mockMd.renderInline.mockReturnValue(expectedHtml)
      
      const result = markdownService.parseInline(inlineText)
      
      expect(mockMd.renderInline).toHaveBeenCalledWith(inlineText)
      expect(result).toBe(expectedHtml)
    })

    it('should handle empty inline text', () => {
      const result = markdownService.parseInline('')
      
      expect(result).toBe('')
    })
  })

  describe('getInstance', () => {
    it('should return the markdown-it instance', () => {
      const instance = markdownService.getInstance()
      
      expect(instance).toBe(mockMd)
    })
  })

  describe('use', () => {
    it('should add plugin to markdown-it instance', () => {
      const plugin = vi.fn()
      const options = { test: true }
      
      markdownService.use(plugin, options)
      
      expect(mockMd.use).toHaveBeenCalledWith(plugin, options)
    })

    it('should add plugin without options', () => {
      const plugin = vi.fn()
      
      // Clear previous calls
      vi.clearAllMocks()
      
      markdownService.use(plugin)
      
      expect(mockMd.use).toHaveBeenCalledWith(plugin, undefined)
    })
  })

  describe('cleanup', () => {
    it('should reset md instance to null', () => {
      markdownService.cleanup()
      
      expect(markdownService.md).toBeNull()
    })
  })

  describe('emoji replacement', () => {
    it('should replace emoji codes with unicode emojis', () => {
      // Test that emoji replacement is set up during initialization
      expect(mockMd.core.ruler.after).toHaveBeenCalled()
    })
  })

  describe('container rendering', () => {
    it('should render tip container correctly', () => {
      const tipMarkdown = '::: tip 重要提示\n这是一个重要提示\n:::'
      
      mockMd.render.mockReturnValue('<div class="tip-container">...</div>')
      
      const result = markdownService.parse(tipMarkdown)
      
      expect(mockMd.render).toHaveBeenCalledWith(tipMarkdown)
      expect(result).toContain('tip-container')
    })

    it('should render warning container correctly', () => {
      const warningMarkdown = '::: warning 注意事项\n这是一个警告\n:::'
      
      mockMd.render.mockReturnValue('<div class="warning-container">...</div>')
      
      const result = markdownService.parse(warningMarkdown)
      
      expect(mockMd.render).toHaveBeenCalledWith(warningMarkdown)
      expect(result).toContain('warning-container')
    })
  })

  describe('link handling', () => {
    it('should render links correctly', () => {
      const linkMarkdown = '[Visit our website](https://example.com)'
      
      mockMd.render.mockReturnValue('<p><a href="https://example.com">Visit our website</a></p>\n')
      
      const result = markdownService.parse(linkMarkdown)
      
      expect(result).toContain('<a href="https://example.com">')
    })
  })

  describe('code block handling', () => {
    it('should render code blocks with syntax highlighting', () => {
      const codeMarkdown = '```javascript\nconsole.log("Hello World");\n```'
      
      mockMd.render.mockReturnValue('<pre><code class="language-javascript">console.log("Hello World");</code></pre>\n')
      
      const result = markdownService.parse(codeMarkdown)
      
      expect(result).toContain('language-javascript')
    })
  })

  describe('table rendering', () => {
    it('should render tables correctly', () => {
      const tableMarkdown = '| Name | Age |\n|------|-----|\n| John | 25  |'
      
      mockMd.render.mockReturnValue('<table><thead><tr><th>Name</th><th>Age</th></tr></thead><tbody><tr><td>John</td><td>25</td></tr></tbody></table>\n')
      
      const result = markdownService.parse(tableMarkdown)
      
      expect(result).toContain('<table>')
      expect(result).toContain('<th>Name</th>')
    })
  })

  describe('list rendering', () => {
    it('should render unordered lists correctly', () => {
      const listMarkdown = '- Item 1\n- Item 2\n- Item 3'
      
      mockMd.render.mockReturnValue('<ul>\n<li>Item 1</li>\n<li>Item 2</li>\n<li>Item 3</li>\n</ul>\n')
      
      const result = markdownService.parse(listMarkdown)
      
      expect(result).toContain('<ul>')
      expect(result).toContain('<li>Item 1</li>')
    })

    it('should render ordered lists correctly', () => {
      const listMarkdown = '1. First item\n2. Second item\n3. Third item'
      
      mockMd.render.mockReturnValue('<ol>\n<li>First item</li>\n<li>Second item</li>\n<li>Third item</li>\n</ol>\n')
      
      const result = markdownService.parse(listMarkdown)
      
      expect(result).toContain('<ol>')
      expect(result).toContain('<li>First item</li>')
    })
  })

  describe('heading rendering', () => {
    it('should render headings correctly', () => {
      const headingMarkdown = '# H1\n## H2\n### H3'
      
      mockMd.render.mockReturnValue('<h1>H1</h1>\n<h2>H2</h2>\n<h3>H3</h3>\n')
      
      const result = markdownService.parse(headingMarkdown)
      
      expect(result).toContain('<h1>H1</h1>')
      expect(result).toContain('<h2>H2</h2>')
      expect(result).toContain('<h3>H3</h3>')
    })
  })

  describe('emphasis rendering', () => {
    it('should render bold text correctly', () => {
      const boldMarkdown = '**Bold text**'
      
      mockMd.render.mockReturnValue('<p><strong>Bold text</strong></p>\n')
      
      const result = markdownService.parse(boldMarkdown)
      
      expect(result).toContain('<strong>Bold text</strong>')
    })

    it('should render italic text correctly', () => {
      const italicMarkdown = '*Italic text*'
      
      mockMd.render.mockReturnValue('<p><em>Italic text</em></p>\n')
      
      const result = markdownService.parse(italicMarkdown)
      
      expect(result).toContain('<em>Italic text</em>')
    })
  })

  describe('blockquote rendering', () => {
    it('should render blockquotes correctly', () => {
      const quoteMarkdown = '> This is a quote\n> With multiple lines'
      
      mockMd.render.mockReturnValue('<blockquote>\n<p>This is a quote\nWith multiple lines</p>\n</blockquote>\n')
      
      const result = markdownService.parse(quoteMarkdown)
      
      expect(result).toContain('<blockquote>')
    })
  })

  describe('horizontal rule rendering', () => {
    it('should render horizontal rules correctly', () => {
      const hrMarkdown = '---'
      
      mockMd.render.mockReturnValue('<hr>\n')
      
      const result = markdownService.parse(hrMarkdown)
      
      expect(result).toContain('<hr>')
    })
  })

  describe('image rendering', () => {
    it('should render images correctly', () => {
      const imageMarkdown = '![Alt text](https://example.com/image.jpg)'
      
      mockMd.render.mockReturnValue('<p><img src="https://example.com/image.jpg" alt="Alt text"></p>\n')
      
      const result = markdownService.parse(imageMarkdown)
      
      expect(result).toContain('<img src="https://example.com/image.jpg"')
      expect(result).toContain('alt="Alt text"')
    })
  })

  describe('error handling', () => {
    it('should handle markdown-it render errors gracefully', () => {
      mockMd.render.mockImplementation(() => {
        throw new Error('Render error')
      })
      
      const result = markdownService.parse('test')
      
      // Should return fallback content instead of throwing
      expect(result).toBe('<pre>test</pre>')
    })

    it('should handle markdown-it renderInline errors gracefully', () => {
      mockMd.renderInline.mockImplementation(() => {
        throw new Error('RenderInline error')
      })
      
      const result = markdownService.parseInline('test')
      
      // Should return original text instead of throwing
      expect(result).toBe('test')
    })
  })

  describe('performance', () => {
    it('should handle large markdown content efficiently', () => {
      const largeMarkdown = '# Large Document\n\n'.repeat(1000) + 'Content here'
      
      mockMd.render.mockReturnValue('<h1>Large Document</h1>\n<p>Content here</p>\n')
      
      const startTime = performance.now()
      const result = markdownService.parse(largeMarkdown)
      const endTime = performance.now()
      
      expect(result).toBeDefined()
      expect(endTime - startTime).toBeLessThan(1000) // Should complete within 1 second
    })
  })
}) 