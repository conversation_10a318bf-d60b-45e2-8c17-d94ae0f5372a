<template>
  <div class="home-container">
    <ClientOnly>
      <!-- 根据登录状态显示不同内容 -->
      <LoginPage v-if="isMounted && !userStore.isLoggedIn" @login-success="trackLoginSuccess" />
      <topmeans v-else-if="isMounted && userStore.isLoggedIn" @plan-created="trackPlanCreated" />
    </ClientOnly>
    <Footer v-if="userStore.isLoggedIn" />
  </div>
</template>

<style scoped>
.showcase-button-container {
  margin-top: 3rem;
  text-align: center;
}

.showcase-button {
  display: inline-block;
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.button-image {
  width: 320px;
  height: 180px;
  object-fit: cover;
  border: 2px solid var(--vp-c-brand);
  border-radius: 16px;
}

.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  opacity: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.hover-overlay span {
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.showcase-button:hover {
  transform: translateY(-5px);
}

.showcase-button:hover .hover-overlay {
  opacity: 1;
}

@media (max-width: 640px) {
  .button-image {
    width: 280px;
    height: 160px;
  }

  .showcase-button-container {
    margin-top: 2rem;
  }
}
</style>

<script setup>
import { ref, onMounted } from 'vue'
import { defineAsyncComponent } from 'vue'
import { useUserStore } from '../components/UserCenter/userStore'
import Footer from '../components/Footer.vue'

const isMounted = ref(false)
const scrollController = ref(null)
const userStore = useUserStore()

const topmeans = defineAsyncComponent(() =>
  import('../components/topmeans.vue')
)

const LoginPage = defineAsyncComponent(() =>
  import('../components/LoginPage.vue')
)

// Google Analytics 事件跟踪
const trackEvent = (eventName, parameters = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, {
      event_category: 'user_interaction',
      event_label: 'homepage',
      ...parameters
    })
    console.log('GA 事件跟踪:', eventName, parameters)
  }
}

// 跟踪登录成功事件
const trackLoginSuccess = () => {
  trackEvent('login_success', {
    event_category: 'authentication',
    event_label: 'homepage_login'
  })
}

// 跟踪攻略创建事件
const trackPlanCreated = (planData) => {
  trackEvent('plan_created', {
    event_category: 'content_creation',
    event_label: 'travel_plan',
    custom_parameters: {
      plan_type: planData?.type || 'unknown',
      days: planData?.days || 0
    }
  })
}

onMounted(async () => {
  // 初始化用户状态
  await userStore.initAuthState()
  isMounted.value = true

  // 跟踪首页访问
  trackEvent('page_view', {
    event_category: 'navigation',
    event_label: 'homepage_visit'
  })
})
</script>