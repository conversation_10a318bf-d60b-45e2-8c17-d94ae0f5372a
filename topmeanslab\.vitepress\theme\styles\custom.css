.home-container {
    max-width: 100%;
    margin: 2rem auto;
    padding: clamp(20px, 4vw, 40px);
    width: 100%;
    box-sizing: border-box;
  }

  /* 输入框动画 */
  .question-input {
    transition: box-shadow 0.3s;
  }
  .question-input:focus {
    box-shadow: 0 0 10px rgba(66, 153, 225, 0.5);
  }

  /* 回答区域渐显效果 */
  .answer-display {
    opacity: 0;
    animation: fadeIn 0.5s forwards;
  }
  @keyframes fadeIn {
    to { opacity: 1; }
  }