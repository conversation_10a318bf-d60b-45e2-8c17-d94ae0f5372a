import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { TopmeansStyleManager } from '@services/TopmeansStyleManager'

describe('TopmeansStyleManager', () => {
  let styleManager
  let mockDocument
  let mockWindow
  let mockMutationObserver

  beforeEach(() => {
    styleManager = new TopmeansStyleManager()
    
    // Mock MutationObserver
    mockMutationObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      disconnect: vi.fn()
    }))
    global.MutationObserver = mockMutationObserver
    
    // Mock document
    mockDocument = {
      getElementById: vi.fn(),
      createElement: vi.fn((tagName) => {
        if (tagName === 'style') {
          return {
            id: '',
            innerHTML: ''
          }
        }
        return {}
      }),
      head: {
        appendChild: vi.fn()
      },
      body: {
        classList: {
          contains: vi.fn().mockReturnValue(false)
        }
      },
      documentElement: {
        classList: {
          contains: vi.fn().mockReturnValue(false)
        }
      },
      querySelector: vi.fn(),
      querySelectorAll: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    }
    
    // Mock window
    mockWindow = {
      setInterval: vi.fn(() => 123),
      clearInterval: vi.fn(),
      matchMedia: vi.fn(() => ({
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        matches: false
      })),
      getComputedStyle: vi.fn(() => ({
        getPropertyValue: vi.fn((property) => {
          const values = {
            '--vp-c-bg': '#ffffff',
            '--vp-c-bg-soft': '#f6f6f7',
            '--vp-c-text-1': '#213547',
            '--vp-c-divider': '#e2e2e2',
            '--vp-c-divider-light': '#f1f1f1',
            '--vp-c-brand': '#646cff',
            '--vp-c-brand-soft': '#646cff1a',
            '--vp-c-brand-dimm': '#646cff0d'
          }
          return values[property] || ''
        })
      }))
    }
    
    global.document = mockDocument
    global.window = mockWindow
    global.getComputedStyle = mockWindow.getComputedStyle
    global.clearInterval = mockWindow.clearInterval
    global.setInterval = mockWindow.setInterval
  })

  afterEach(() => {
    vi.clearAllMocks()
    vi.clearAllTimers()
  })

  describe('constructor', () => {
    it('should initialize with null observer and interval', () => {
      expect(styleManager.amapObserver).toBeNull()
      expect(styleManager.amapStyleInterval).toBeNull()
    })
  })

  describe('ensureAmapSuggestStyles', () => {
    it('should inject styles and start monitor when window is available', () => {
      const injectAmapStylesSpy = vi.spyOn(styleManager, 'injectAmapStyles')
      const startAmapStyleMonitorSpy = vi.spyOn(styleManager, 'startAmapStyleMonitor')

      styleManager.ensureAmapSuggestStyles()

      expect(injectAmapStylesSpy).toHaveBeenCalled()
      expect(startAmapStyleMonitorSpy).toHaveBeenCalled()
    })

    it('should not execute when window is undefined', () => {
      const originalWindow = global.window
      delete global.window

      const injectAmapStylesSpy = vi.spyOn(styleManager, 'injectAmapStyles')
      const startAmapStyleMonitorSpy = vi.spyOn(styleManager, 'startAmapStyleMonitor')

      styleManager.ensureAmapSuggestStyles()

      expect(injectAmapStylesSpy).not.toHaveBeenCalled()
      expect(startAmapStyleMonitorSpy).not.toHaveBeenCalled()

      global.window = originalWindow
    })
  })

  describe('injectAmapStyles', () => {
    it('should remove existing style and create new one', () => {
      const existingStyle = { remove: vi.fn() }
      mockDocument.getElementById.mockReturnValue(existingStyle)
      
      const newStyle = {
        id: '',
        innerHTML: ''
      }
      mockDocument.createElement.mockReturnValue(newStyle)

      styleManager.injectAmapStyles()

      expect(mockDocument.getElementById).toHaveBeenCalledWith('amap-dark-theme-fix')
      expect(existingStyle.remove).toHaveBeenCalled()
      expect(mockDocument.createElement).toHaveBeenCalledWith('style')
      expect(newStyle.id).toBe('amap-dark-theme-fix')
      expect(newStyle.innerHTML).toContain('.amap-sug-result')
      expect(mockDocument.head.appendChild).toHaveBeenCalledWith(newStyle)
    })

    it('should handle when no existing style is found', () => {
      mockDocument.getElementById.mockReturnValue(null)
      
      const newStyle = {
        id: '',
        innerHTML: ''
      }
      mockDocument.createElement.mockReturnValue(newStyle)

      styleManager.injectAmapStyles()

      expect(mockDocument.getElementById).toHaveBeenCalledWith('amap-dark-theme-fix')
      expect(mockDocument.createElement).toHaveBeenCalledWith('style')
      expect(mockDocument.head.appendChild).toHaveBeenCalledWith(newStyle)
    })
  })

  describe('startAmapStyleMonitor', () => {
    it('should start monitoring with interval', () => {
      styleManager.startAmapStyleMonitor()

      expect(mockWindow.setInterval).toHaveBeenCalledWith(expect.any(Function), 500)
      expect(styleManager.amapStyleInterval).toBe(123)
    })

    it('should clear existing interval before starting new one', () => {
      styleManager.amapStyleInterval = 456

      styleManager.startAmapStyleMonitor()

      expect(mockWindow.clearInterval).toHaveBeenCalledWith(456)
      expect(mockWindow.setInterval).toHaveBeenCalledWith(expect.any(Function), 500)
    })
  })

  describe('forceApplyAmapStyles', () => {
    it('should force apply styles to all amap elements', () => {
      const mockElements = [
        { 
          style: { setProperty: vi.fn() },
          querySelectorAll: vi.fn().mockReturnValue([
            { style: { setProperty: vi.fn() } }
          ])
        },
        { 
          style: { setProperty: vi.fn() },
          querySelectorAll: vi.fn().mockReturnValue([
            { style: { setProperty: vi.fn() } }
          ])
        }
      ]
      
      // Mock querySelectorAll to return elements for each selector
      mockDocument.querySelectorAll.mockImplementation((selector) => {
        if (selector.includes('.amap-sug-result')) {
          return mockElements
        }
        return []
      })

      styleManager.forceApplyAmapStyles()

      expect(mockDocument.querySelectorAll).toHaveBeenCalledWith('.amap-sug-result')
      expect(mockElements[0].style.setProperty).toHaveBeenCalledWith('background', 'var(--vp-c-bg)', 'important')
    })

    it('should handle when no elements are found', () => {
      mockDocument.querySelectorAll.mockReturnValue([])

      expect(() => styleManager.forceApplyAmapStyles()).not.toThrow()
    })
  })

  describe('isDarkMode', () => {
    it('should return true when dark mode is active', () => {
      mockDocument.documentElement.classList.contains.mockReturnValue(true)

      const result = styleManager.isDarkMode()

      expect(result).toBe(true)
      expect(mockDocument.documentElement.classList.contains).toHaveBeenCalledWith('dark')
    })

    it('should return false when dark mode is not active', () => {
      mockDocument.documentElement.classList.contains.mockReturnValue(false)

      const result = styleManager.isDarkMode()

      expect(result).toBe(false)
    })
  })

  describe('getThemeColors', () => {
    it('should return theme colors', () => {
      const colors = styleManager.getThemeColors()

      expect(colors).toEqual({
        bg: '#ffffff',
        bgSoft: '#f6f6f7',
        text1: '#213547',
        divider: '#e2e2e2',
        dividerLight: '#f1f1f1',
        brand: '#646cff',
        brandSoft: '#646cff1a',
        brandDimm: '#646cff0d'
      })
    })
  })

  describe('watchThemeChange', () => {
    it('should set up theme change listener', () => {
      const callback = vi.fn()

      const observer = styleManager.watchThemeChange(callback)

      expect(mockMutationObserver).toHaveBeenCalled()
      expect(observer.observe).toHaveBeenCalledWith(mockDocument.documentElement, {
        attributes: true,
        attributeFilter: ['class']
      })
    })
  })

  describe('applyCustomStyles', () => {
    it('should apply custom styles with given ID', () => {
      const styleId = 'custom-style'
      const cssText = '.test { color: red; }'

      const mockStyle = {
        id: '',
        innerHTML: ''
      }
      mockDocument.createElement.mockReturnValue(mockStyle)

      styleManager.applyCustomStyles(styleId, cssText)

      expect(mockDocument.getElementById).toHaveBeenCalledWith(styleId)
      expect(mockDocument.createElement).toHaveBeenCalledWith('style')
      expect(mockStyle.id).toBe(styleId)
      expect(mockStyle.innerHTML).toBe(cssText)
      expect(mockDocument.head.appendChild).toHaveBeenCalledWith(mockStyle)
    })

    it('should remove existing style before applying new one', () => {
      const styleId = 'custom-style'
      const cssText = '.test { color: red; }'

      const existingStyle = { remove: vi.fn() }
      mockDocument.getElementById.mockReturnValue(existingStyle)
      
      const newStyle = {
        id: '',
        innerHTML: ''
      }
      mockDocument.createElement.mockReturnValue(newStyle)

      styleManager.applyCustomStyles(styleId, cssText)

      expect(existingStyle.remove).toHaveBeenCalled()
    })
  })

  describe('removeCustomStyles', () => {
    it('should remove custom styles by ID', () => {
      const styleId = 'custom-style'
      const mockStyle = { remove: vi.fn() }
      mockDocument.getElementById.mockReturnValue(mockStyle)

      styleManager.removeCustomStyles(styleId)

      expect(mockDocument.getElementById).toHaveBeenCalledWith(styleId)
      expect(mockStyle.remove).toHaveBeenCalled()
    })

    it('should handle when style is not found', () => {
      const styleId = 'non-existent-style'
      mockDocument.getElementById.mockReturnValue(null)

      expect(() => styleManager.removeCustomStyles(styleId)).not.toThrow()
    })
  })

  describe('cleanup', () => {
    it('should cleanup all resources', () => {
      const mockObserver = { disconnect: vi.fn() }
      styleManager.amapObserver = mockObserver
      styleManager.amapStyleInterval = 123

      styleManager.cleanup()

      expect(mockObserver.disconnect).toHaveBeenCalled()
      expect(mockWindow.clearInterval).toHaveBeenCalledWith(123)
      expect(styleManager.amapObserver).toBeNull()
      expect(styleManager.amapStyleInterval).toBeNull()
    })

    it('should handle null observer and interval', () => {
      styleManager.amapObserver = null
      styleManager.amapStyleInterval = null

      expect(() => styleManager.cleanup()).not.toThrow()
    })
  })

  describe('reinitialize', () => {
    it('should cleanup and reinitialize styles', () => {
      const cleanupSpy = vi.spyOn(styleManager, 'cleanup')
      const ensureAmapSuggestStylesSpy = vi.spyOn(styleManager, 'ensureAmapSuggestStyles')

      styleManager.reinitialize()

      expect(cleanupSpy).toHaveBeenCalled()
      expect(ensureAmapSuggestStylesSpy).toHaveBeenCalled()
    })
  })

  describe('getStyleState', () => {
    it('should return current style state', () => {
      const mockObserver = { test: 'observer' }
      styleManager.amapObserver = mockObserver
      styleManager.amapStyleInterval = 123

      const state = styleManager.getStyleState()

      expect(state).toEqual({
        hasAmapObserver: true,
        hasStyleInterval: true,
        isDarkMode: false,
        themeColors: {
          bg: '#ffffff',
          bgSoft: '#f6f6f7',
          text1: '#213547',
          divider: '#e2e2e2',
          dividerLight: '#f1f1f1',
          brand: '#646cff',
          brandSoft: '#646cff1a',
          brandDimm: '#646cff0d'
        }
      })
    })
  })

  describe('CSS injection validation', () => {
    it('should inject correct CSS for light theme', () => {
      mockDocument.documentElement.classList.contains.mockReturnValue(false)
      
      const newStyle = {
        id: '',
        innerHTML: ''
      }
      mockDocument.createElement.mockReturnValue(newStyle)

      styleManager.injectAmapStyles()

      expect(newStyle.innerHTML).toContain('background: var(--vp-c-bg)')
      expect(newStyle.innerHTML).toContain('color: var(--vp-c-text-1)')
      expect(newStyle.innerHTML).toContain('border: 1px solid var(--vp-c-divider)')
    })

    it('should inject correct CSS for dark theme', () => {
      mockDocument.documentElement.classList.contains.mockReturnValue(true)
      
      const newStyle = {
        id: '',
        innerHTML: ''
      }
      mockDocument.createElement.mockReturnValue(newStyle)

      styleManager.injectAmapStyles()

      expect(newStyle.innerHTML).toContain('.dark .amap-sug-result')
      expect(newStyle.innerHTML).toContain('background: var(--vp-c-bg-soft)')
      expect(newStyle.innerHTML).toContain('color: #ffffff')
    })
  })

  describe('style monitoring', () => {
    it('should monitor styles at regular intervals', () => {
      styleManager.startAmapStyleMonitor()

      expect(mockWindow.setInterval).toHaveBeenCalledWith(expect.any(Function), 500)
    })

    it('should stop monitoring when cleanup is called', () => {
      styleManager.amapStyleInterval = 123

      styleManager.cleanup()

      expect(mockWindow.clearInterval).toHaveBeenCalledWith(123)
    })
  })

  describe('theme change handling', () => {
    it('should handle theme change events', () => {
      const callback = vi.fn()

      const observer = styleManager.watchThemeChange(callback)

      // Simulate theme change by calling the observer callback
      const mutationObserverCallback = mockMutationObserver.mock.calls[0][0]
      mutationObserverCallback([{
        type: 'attributes',
        attributeName: 'class'
      }])

      expect(callback).toHaveBeenCalledWith(false)
    })
  })

  describe('error handling', () => {
    it('should handle errors during style injection gracefully', () => {
      mockDocument.createElement.mockImplementation(() => {
        throw new Error('DOM error')
      })

      expect(() => styleManager.injectAmapStyles()).toThrow('DOM error')
    })

    it('should handle errors during style monitoring gracefully', () => {
      mockDocument.querySelectorAll.mockImplementation(() => {
        throw new Error('Query error')
      })

      expect(() => styleManager.forceApplyAmapStyles()).not.toThrow()
    })
  })

  describe('performance', () => {
    it('should not create excessive intervals', () => {
      styleManager.startAmapStyleMonitor()
      styleManager.startAmapStyleMonitor()
      styleManager.startAmapStyleMonitor()

      // Should only create one interval, clearing previous ones
      expect(mockWindow.setInterval).toHaveBeenCalledTimes(3)
      expect(mockWindow.clearInterval).toHaveBeenCalledTimes(2)
    })
  })
}) 