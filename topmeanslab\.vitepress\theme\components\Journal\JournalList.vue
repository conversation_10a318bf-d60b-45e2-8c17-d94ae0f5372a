<template>
  <div class="journal-list">
    <div class="list-header" v-if="showHeader">
      <div class="header-title">
        <h2>{{ title }}</h2>
        <span class="journal-count" v-if="total > 0">共{{ total }}篇游记</span>
      </div>
      <div class="header-actions">
        <el-button
          v-if="showCreateButton"
          type="primary"
          @click="handleCreate"
          :icon="Plus"
        >
          写游记
        </el-button>
        <div class="filter-controls">
          <el-select
            v-model="sortBy"
            placeholder="排序方式"
            @change="handleSortChange"
            style="width: 120px"
          >
            <el-option label="最新发布" value="created_at" />
            <el-option label="最多点赞" value="like_count" />
            <el-option label="最多浏览" value="view_count" />
          </el-select>
          <el-select
            v-if="showPublicFilter"
            v-model="publicFilter"
            placeholder="公开状态"
            @change="handlePublicFilterChange"
            style="width: 100px"
          >
            <el-option label="全部" value="" />
            <el-option label="公开" value="public" />
            <el-option label="私密" value="private" />
          </el-select>
        </div>
      </div>
    </div>

    <div class="list-content">
      <div v-if="loading" class="loading-container">
        <el-skeleton
          v-for="i in 3"
          :key="i"
          :rows="4"
          animated
          class="skeleton-item"
        />
      </div>

      <div v-else-if="journals.length === 0" class="empty-state">
        <div class="empty-icon">📝</div>
        <div class="empty-text">{{ emptyText }}</div>
        <el-button
          v-if="showCreateButton"
          type="primary"
          @click="handleCreate"
          class="empty-action"
        >
          写下第一篇游记
        </el-button>
      </div>

      <div v-else class="journals-grid">
        <JournalCard
          v-for="journal in journals"
          :key="journal.id"
          :journal="journal"
          :show-actions="showActions"
          :show-like="showLike"
          :show-privacy="showPrivacy"
          @like="handleLike"
          @comment="handleComment"
          @edit="handleEdit"
          @delete="handleDelete"
          @toggle-public="handleTogglePublic"
          @click="handleJournalClick"
        />
      </div>

      <div v-if="showPagination && pagination.totalPages > 1" class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 游记编辑器对话框 -->
    <el-dialog
      v-model="showEditor"
      :title="editingJournal ? '编辑游记' : '写游记'"
      width="90%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <JournalEditor
        :journal="editingJournal"
        @save="handleEditorSave"
        @cancel="handleEditorCancel"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import JournalCard from './JournalCard.vue'
import JournalEditor from './JournalEditor.vue'
import { useUserStore } from '../UserCenter/userStore'

const props = defineProps({
  title: {
    type: String,
    default: '游记列表'
  },
  apiUrl: {
    type: String,
    required: true
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  showCreateButton: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: true
  },
  showLike: {
    type: Boolean,
    default: true
  },
  showPrivacy: {
    type: Boolean,
    default: false
  },
  showPublicFilter: {
    type: Boolean,
    default: false
  },
  showPagination: {
    type: Boolean,
    default: true
  },
  emptyText: {
    type: String,
    default: '暂无游记'
  },
  autoLoad: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['journalClick', 'journalCreated', 'journalUpdated', 'journalDeleted'])

const userStore = useUserStore()
const API_BASE = import.meta.env.VITE_BACKEND_SRV_URL + '/api'

// 状态变量
const loading = ref(false)
const journals = ref([])
const total = ref(0)
const sortBy = ref('created_at')
const publicFilter = ref('')
const showEditor = ref(false)
const editingJournal = ref(null)

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
})

// 初始化
onMounted(() => {
  if (props.autoLoad) {
    loadJournals()
  }
})

// 监听排序变化
watch([sortBy, publicFilter], () => {
  pagination.page = 1
  loadJournals()
})

// 加载游记列表
const loadJournals = async () => {
  if (loading.value) return

  try {
    loading.value = true

    const params = new URLSearchParams({
      page: pagination.page.toString(),
      pageSize: pagination.pageSize.toString(),
      sortBy: sortBy.value
    })

    if (publicFilter.value) {
      params.append('public', publicFilter.value === 'public' ? '1' : '0')
    }

    const url = `${API_BASE}${props.apiUrl}?${params}`

    const response = await fetch(url, {
      headers: userStore.token ? {
        'Authorization': `Bearer ${userStore.token}`
      } : {}
    })

    const result = await response.json()

    if (result.success) {
      journals.value = result.data || []
      total.value = result.pagination?.total || 0
      pagination.total = result.pagination?.total || 0
      pagination.totalPages = result.pagination?.totalPages || 0
    } else {
      ElMessage.error(result.message || '加载失败')
    }
  } catch (error) {
    console.error('加载游记列表失败:', error)
    ElMessage.error('加载失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 处理排序变化
const handleSortChange = () => {
  pagination.page = 1
  loadJournals()
}

// 处理公开状态筛选变化
const handlePublicFilterChange = () => {
  pagination.page = 1
  loadJournals()
}

// 处理分页变化
const handlePageChange = (page) => {
  pagination.page = page
  loadJournals()
}

// 处理页面大小变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadJournals()
}

// 处理创建游记
const handleCreate = () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    return
  }
  editingJournal.value = null
  showEditor.value = true
}

// 处理编辑游记
const handleEdit = (journal) => {
  editingJournal.value = journal
  showEditor.value = true
}

// 处理编辑器保存
const handleEditorSave = (result) => {
  showEditor.value = false
  editingJournal.value = null

  if (result.journalId) {
    // 新创建的游记
    emit('journalCreated', result)
    ElMessage.success('游记发布成功')
  } else {
    // 更新的游记
    emit('journalUpdated', result)
    ElMessage.success('游记更新成功')
  }

  loadJournals()
}

// 处理编辑器取消
const handleEditorCancel = () => {
  showEditor.value = false
  editingJournal.value = null
}

// 处理点赞
const handleLike = ({ journalId, isLiked }) => {
  const journal = journals.value.find(j => j.id === journalId)
  if (journal) {
    journal.is_liked = isLiked
    journal.like_count += isLiked ? 1 : -1
  }
}

// 处理评论
const handleComment = (journal) => {
  // 可以打开评论对话框或跳转到详情页
  emit('journalClick', journal)
}

// 处理删除游记
const handleDelete = (journalId) => {
  journals.value = journals.value.filter(j => j.id !== journalId)
  total.value--
  pagination.total--
  emit('journalDeleted', journalId)
}

// 处理切换公开状态
const handleTogglePublic = ({ journalId, isPublic }) => {
  const journal = journals.value.find(j => j.id === journalId)
  if (journal) {
    journal.is_public = isPublic
  }
}

// 处理游记点击
const handleJournalClick = (journal) => {
  emit('journalClick', journal)
}

// 暴露刷新方法
defineExpose({
  refresh: loadJournals
})
</script>

<style scoped>
.journal-list {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 16px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title h2 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.journal-count {
  font-size: 14px;
  color: #666;
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-controls {
  display: flex;
  gap: 8px;
}

.list-content {
  padding: 0 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skeleton-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
}

.empty-action {
  margin-top: 16px;
}

.journals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .journal-list {
    padding: 0 8px;
  }

  .list-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
    margin-bottom: 16px;
    padding: 0 8px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .filter-controls {
    flex-direction: column;
    gap: 8px;
  }

  .list-content {
    padding: 0 8px;
  }

  .journals-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .pagination-container {
    margin-top: 20px;
  }
}

@media (max-width: 480px) {
  .header-title h2 {
    font-size: 20px;
  }

  .filter-controls {
    width: 100%;
  }

  .filter-controls .el-select {
    width: 100% !important;
  }

  .empty-state {
    padding: 40px 16px;
  }

  .empty-icon {
    font-size: 36px;
  }
}
</style>