import{_ as z}from"./PaymentMethods.BgGK8a_4.js";import{_ as L,p as o,a3 as R,C as i,c as g,o as p,G as t,b as q,e as H,w as a,j as e,t as u,a as P,F as I,B as S,n as J}from"./framework.oPHriSgN.js";import{E as x}from"./theme.DE6uTiF9.js";const K={class:"service-purchase"},O={class:"service-info"},Q={class:"service-description"},W={class:"service-price"},X={class:"price-value"},Y={class:"service-actions"},Z={class:"vip-plans"},D={class:"plan-price"},ee={class:"price-value"},se={class:"price-unit"},ae={class:"plan-features"},ne={class:"vip-actions"},te={class:"recharge-amount"},le={class:"recharge-actions"},oe={__name:"ServicePurchase",setup(ce){const V=o("single"),d=o(!1),v=o(0),_=o(""),r=o({}),m=o(null),f=o(100),w=o(1),B=o("<EMAIL>"),c=R({name:"高级服务",description:"提供专业的一对一服务，解决您的所有问题",price:1}),C=[{id:1,name:"月度VIP",price:99,unit:"月",features:["无限次使用服务","优先响应","专属客服"]},{id:2,name:"季度VIP",price:269,unit:"季",features:["无限次使用服务","优先响应","专属客服","享受9折优惠"]},{id:3,name:"年度VIP",price:999,unit:"年",features:["无限次使用服务","优先响应","专属客服","享受8折优惠","年度专属礼包"]}],T=()=>{v.value=c.price,_.value=`TopMeans - ${c.name}`,r.value={type:"single_service",name:c.name,description:c.description,price:c.price},d.value=!0},j=()=>{const n=C.find(s=>s.id===m.value);n&&(v.value=n.price,_.value=`TopMeans VIP - ${n.name}`,r.value={type:"vip_plan",planId:n.id,name:n.name,price:n.price,unit:n.unit,features:n.features},d.value=!0)},M=()=>{v.value=f.value,_.value="TopMeans 账户充值",r.value={type:"account_recharge",amount:f.value},d.value=!0},N=n=>{d.value=!1,x.success(`支付成功！订单号：${n.orderId}`),r.value.type==="single_service"?console.log("单次服务购买成功",n):r.value.type==="vip_plan"?console.log("VIP会员开通成功",n):r.value.type==="account_recharge"&&console.log("账户充值成功",n)},U=()=>{d.value=!1,x.info("支付已取消")};return(n,s)=>{const b=i("el-button"),h=i("el-card"),y=i("el-tab-pane"),$=i("el-col"),A=i("el-row"),E=i("el-input-number"),F=i("el-tabs");return p(),g("div",K,[t(F,{modelValue:V.value,"onUpdate:modelValue":s[1]||(s[1]=l=>V.value=l),class:"service-tabs"},{default:a(()=>[t(y,{label:"单次服务",name:"single"},{default:a(()=>[t(h,{class:"service-card"},{header:a(()=>s[2]||(s[2]=[e("div",{class:"card-header"},[e("span",null,"单次服务购买")],-1)])),default:a(()=>[e("div",O,[e("h3",null,u(c.name),1),e("p",Q,u(c.description),1),e("div",W,[s[3]||(s[3]=e("span",{class:"price-label"},"价格：",-1)),e("span",X,"¥"+u(c.price),1)])]),e("div",Y,[t(b,{type:"primary",onClick:T},{default:a(()=>s[4]||(s[4]=[P(" 立即购买 ")])),_:1})])]),_:1})]),_:1}),t(y,{label:"VIP会员",name:"vip"},{default:a(()=>[t(h,{class:"vip-card"},{header:a(()=>s[5]||(s[5]=[e("div",{class:"card-header"},[e("span",null,"VIP会员订阅")],-1)])),default:a(()=>[e("div",Z,[t(A,{gutter:20},{default:a(()=>[(p(),g(I,null,S(C,l=>t($,{span:8,key:l.id},{default:a(()=>[t(h,{class:J(["plan-card",{"is-selected":m.value===l.id}]),onClick:k=>m.value=l.id},{default:a(()=>[e("h3",null,u(l.name),1),e("div",D,[e("span",ee,"¥"+u(l.price),1),e("span",se,"/"+u(l.unit),1)]),e("ul",ae,[(p(!0),g(I,null,S(l.features,(k,G)=>(p(),g("li",{key:G},u(k),1))),128))])]),_:2},1032,["class","onClick"])]),_:2},1024)),64))]),_:1})]),e("div",ne,[t(b,{type:"primary",onClick:j,disabled:!m.value},{default:a(()=>s[6]||(s[6]=[P(" 立即开通 ")])),_:1},8,["disabled"])])]),_:1})]),_:1}),t(y,{label:"账户充值",name:"recharge"},{default:a(()=>[t(h,{class:"recharge-card"},{header:a(()=>s[7]||(s[7]=[e("div",{class:"card-header"},[e("span",null,"账户充值")],-1)])),default:a(()=>[e("div",te,[t(E,{modelValue:f.value,"onUpdate:modelValue":s[0]||(s[0]=l=>f.value=l),min:100,max:1e4,step:100,placeholder:"请输入充值金额"},null,8,["modelValue"])]),e("div",le,[t(b,{type:"primary",onClick:M},{default:a(()=>s[8]||(s[8]=[P(" 立即充值 ")])),_:1})])]),_:1})]),_:1})]),_:1},8,["modelValue"]),d.value?(p(),q(z,{key:0,amount:v.value,subject:_.value,goods:r.value,"user-id":w.value,"user-account":B.value,onPaymentSuccess:N,onPaymentCancel:U},null,8,["amount","subject","goods","user-id","user-account"])):H("",!0)])}}},de=L(oe,[["__scopeId","data-v-9da6d832"]]);export{de as S};
