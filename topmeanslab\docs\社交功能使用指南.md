# 旅游规划网站社交功能使用指南

## 功能概述

本次更新为旅游规划网站增加了完整的社交功能，包括：

1. **游记创建与管理**：用户可以创建、编辑、删除自己的游记
2. **隐私控制**：支持设置游记为公开或私密状态
3. **游记推荐**：在规划完成后，自动推荐相同目的地的其他用户游记
4. **互动功能**：支持点赞、评论、浏览统计等社交互动

## 功能特点

### 1. 游记编辑器
- **富文本编辑**：支持Markdown格式的内容编辑
- **图片上传**：可上传封面图片和内容图片
- **标签管理**：为游记添加标签分类
- **旅行信息**：记录出发地、目的地、旅行天数、方式等信息
- **隐私设置**：可选择公开或私密状态

### 2. 游记展示
- **卡片式布局**：美观的卡片式游记展示
- **作者信息**：显示游记作者头像、昵称和发布时间
- **互动统计**：显示点赞数、评论数、浏览数
- **标签展示**：展示游记标签信息

### 3. 游记推荐
- **智能推荐**：基于目的地自动推荐相关游记
- **精美展示**：渐变背景的推荐卡片展示
- **快速预览**：支持点击查看游记详情

### 4. 用户中心集成
- **我的游记**：在用户中心新增游记管理选项卡
- **筛选排序**：支持按公开状态筛选、按时间/点赞数排序
- **批量管理**：支持批量操作游记

## 安装配置

### 1. 数据库设置

执行以下SQL脚本创建相关数据表：

```sql
-- 执行文件：topmeans_srv/db/social_features.sql
-- 这个文件包含了所有社交功能需要的数据库表结构
```

### 2. 后端API

社交功能的后端API已经集成到现有的后端服务中：

- **游记路由**：`topmeans_srv/src/routes/journalRoutes.js`
- **游记服务**：`topmeans_srv/src/services/journalService.js`
- **API接口**：所有接口都以 `/api/journal` 为前缀

主要API接口：
- `POST /api/journal` - 创建游记
- `GET /api/journal/my` - 获取用户游记
- `GET /api/journal/public` - 获取公开游记
- `GET /api/journal/recommendations/:destination` - 获取推荐游记
- `PUT /api/journal/:id` - 更新游记
- `DELETE /api/journal/:id` - 删除游记
- `PATCH /api/journal/:id/toggle-public` - 切换公开状态
- `POST /api/journal/:id/like` - 点赞游记

### 3. 前端组件

新增的前端组件：
- `JournalEditor.vue` - 游记编辑器
- `JournalCard.vue` - 游记卡片组件
- `JournalList.vue` - 游记列表组件
- `JournalRecommendation.vue` - 游记推荐组件

## 使用说明

### 1. 创建游记

1. 登录后，在用户中心的"我的游记"选项卡中点击"写游记"
2. 填写游记标题、旅行信息、封面图片
3. 使用编辑器编写游记内容，支持Markdown语法
4. 添加标签分类
5. 选择隐私设置（公开/私密）
6. 点击"发布"完成创建

### 2. 管理游记

在用户中心的"我的游记"选项卡中：
- **查看游记**：点击游记卡片查看详情
- **编辑游记**：点击游记卡片上的"编辑"按钮
- **删除游记**：点击游记卡片上的"删除"按钮
- **切换公开状态**：点击"设为公开/私密"按钮
- **筛选游记**：使用筛选器按公开状态筛选
- **排序游记**：支持按时间、点赞数、浏览数排序

### 3. 游记推荐

在完成旅游规划后，系统会自动显示游记推荐区域：
- 推荐相同目的地的其他用户公开游记
- 按浏览量、点赞数、发布时间排序
- 点击游记卡片查看详细内容
- 支持点赞和评论功能

### 4. 互动功能

- **点赞**：点击❤️按钮为游记点赞
- **评论**：点击评论按钮查看和发表评论
- **浏览统计**：自动统计游记浏览次数

## 技术特点

### 1. 响应式设计
- 适配桌面、平板、手机等不同设备
- 流畅的用户体验

### 2. 性能优化
- 分页加载减少服务器压力
- 图片懒加载优化页面性能
- 组件级别的状态管理

### 3. 数据安全
- JWT身份验证
- 权限控制确保数据安全
- 输入验证防止XSS攻击

### 4. 扩展性
- 模块化设计便于功能扩展
- 标准化API接口
- 清晰的代码结构

## 注意事项

1. **图片上传**：单张图片大小限制为5MB
2. **内容审核**：公开游记需要遵守平台规范
3. **隐私保护**：私密游记只有本人可见
4. **数据备份**：建议定期备份重要游记内容

## 故障排除

### 常见问题

1. **游记推荐不显示**
   - 确保用户已登录
   - 检查目的地信息是否正确
   - 确认有其他用户发布了相同目的地的公开游记

2. **图片上传失败**
   - 检查图片格式是否支持（jpg、png、gif、webp）
   - 确认图片大小不超过5MB
   - 检查网络连接是否正常

3. **游记无法保存**
   - 确认所有必填字段已填写
   - 检查网络连接
   - 确认登录状态有效

### 技术支持

如果遇到其他问题，请检查：
1. 浏览器控制台是否有错误信息
2. 网络请求是否正常
3. 后端服务是否正常运行
4. 数据库连接是否正常

## 未来规划

1. **增强互动功能**：
   - 评论系统完善
   - 用户关注功能
   - 游记收藏功能

2. **内容推荐优化**：
   - 基于用户行为的个性化推荐
   - 热门游记推荐
   - 地区热门推荐

3. **社交分享**：
   - 分享到社交媒体
   - 生成游记二维码
   - 离线阅读功能

---

*本指南会根据功能更新持续完善，如有疑问请联系技术支持。*