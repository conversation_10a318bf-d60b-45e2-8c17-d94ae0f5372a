#topmeans_srv

# 后端服务通过pm2启动与守护

## pm2安装
```bash
npm install -g pm2
```

## 创建 pm2 配置文件ecosystem.config.js
```javascript
module.exports = {
  apps: [{
    name: 'topmeans_srv',
    script: './start_server.sh', // 或你的主入口文件
    instances: 'max', // 或 'max' 使用所有CPU核心
    exec_mode: 'fork', // 或 'fork'
    env: {
      NODE_ENV: 'development',
      PORT: 3999
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3999
    },
    // 自动重启配置
    watch: false, // 生产环境建议关闭
    max_memory_restart: '1G',
    // 日志配置
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    // 重启策略
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
}
```

## 后端服务启动脚本 start_server.sh
```bash
#!/bin/bash

# 杀死重复进程
#fuser -k 3999/tcp

echo 'start topmeans server on 3999...'

npm run dev
```

## 启动pm2
```bash
pm2 start ecosystem.config.js --env production
```

## 停止 topmeans_srv 服务（注意，这种方式停止服务再重启服务，不会重新加载 ecosystem.config.js 文件）
```bash
pm2 stop topmeans_srv
```

## 完全停止 pm2 进程（这种方式重新启动会重新加载 ecosystem.config.js 配置文件）
```bash
pm2 kill
```

## 查看 pm2 状态
```bash
pm2 status

# 查看日志
pm2 logs topmeans_srv
```

## 其他
```bash
# 重新加载配置
pm2 reload ecosystem.config.js

# 保存当前进程列表
pm2 save

# 开机自启动
pm2 startup
pm2 save
```