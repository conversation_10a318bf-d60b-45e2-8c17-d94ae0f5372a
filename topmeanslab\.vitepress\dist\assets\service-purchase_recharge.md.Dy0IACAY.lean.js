import{S as e}from"./chunks/ServicePurchase.D8dfbI0D.js";import{c as r,o as a,G as t}from"./chunks/framework.oPHriSgN.js";import"./chunks/PaymentMethods.BgGK8a_4.js";import"./chunks/theme.DE6uTiF9.js";const h=JSON.parse('{"title":"","description":"","frontmatter":{"layout":"page"},"headers":[],"relativePath":"service-purchase/recharge.md","filePath":"service-purchase/recharge.md"}'),c={name:"service-purchase/recharge.md"},l=Object.assign(c,{setup(s){return(o,i)=>(a(),r("div",null,[t(e)]))}});export{h as __pageData,l as default};
